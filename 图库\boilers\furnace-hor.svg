<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 417 257" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param name="coldColor" description="Cold Color" type="C" cssAttributes="stop-color,fill" classes="coldColor" forceRepaint="true"/>
	<agg:param name="hotColor" description="Hot Color" type="C" cssAttributes="stop-color" classes="hotColor" forceRepaint="true"/>
	<agg:param name="furnaceState" description="Furnace State" type="state">
		<agg:state name="on" description="On">
			<agg:param attributes="visibility" ids="offState" value="hidden"/>
			<agg:param attributes="visibility" ids="onState" value="visible"/>
		</agg:state>
		<agg:state name="off" description="Off">
			<agg:param attributes="visibility" ids="offState" value="visible"/>
			<agg:param attributes="visibility" ids="onState" value="hidden"/>
		</agg:state>
	</agg:param>
</agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.flameColor,.inlineColor,.boundaryColor{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		stroke-width:1px;
	}

	.caseColor {
		fill:#4C5B75;
	}
	
	.flameColor {
		stroke:#CE0000;
	}
	
	.inlineColor {
		stroke:white;
		opacity:0.75;
	}
	
	.boundaryColor {
		stroke:black;
		stroke-width:1.5px;
		opacity:0.75;
	}
	
	.coldColor {
		stop-color:#29B1FF;
		fill:#29B1FF;
	}

	.hotColor {
		stop-color:#FF0000;
	}
	
	.smokeColor {
		fill:#7D7D7D
	}

      ]]>
</style>

<defs>
	<linearGradient id="gassesGradient" gradientUnits="userSpaceOnUse" x1="52.7554" y1="229.7104" x2="436.8709" y2="19.7637">
		<stop  offset="0" style="stop-color:#FCDA2F"/>
		<stop  offset="0.0596" style="stop-color:#FCCC29"/>
		<stop  offset="0.1722" style="stop-color:#FCA71A"/>
		<stop  offset="0.3248" style="stop-color:#FC6C01"/>
		<stop  offset="0.3333" style="stop-color:#FC6900"/>
		<stop  offset="0.5853" style="stop-color:#CA8A5B"/>
		<stop  offset="0.7939" style="stop-color:#A3A3A3"/>
		<stop  offset="1" style="stop-color:#7D7D7D"/>
	</linearGradient>

	<linearGradient id="waterGradient" gradientUnits="userSpaceOnUse" x1="0" y1="256.2881" x2="0" y2="4.8412">
		<stop  offset="0" class="coldColor"/>
		<stop  offset="1" class="hotColor"/>
	</linearGradient>
	
	<radialGradient id="flameGradient" cx="264.5728" cy="6570.4834" r="47.2298" gradientTransform="matrix(1 0 0 -0.9975 -229.6201 6800.7754)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#FCDA2F"/>
		<stop  offset="0.1084" style="stop-color:#FCCC29"/>
		<stop  offset="0.3132" style="stop-color:#FCA71A"/>
		<stop  offset="0.5906" style="stop-color:#FC6C01"/>
		<stop  offset="0.6061" style="stop-color:#FC6900"/>
		<stop  offset="1" style="stop-color:#FC0000"/>
	</radialGradient>
	
	<linearGradient id="caseGradient" gradientUnits="userSpaceOnUse" x1="1" y1="1" x2="420" y2="260">
		<stop  offset="0" style="stop-color:#4C5B75"/>
		<stop  offset="0.5" style="stop-color:#C1CEE0"/>
		<stop  offset="1" style="stop-color:#4C5B75"/>
	</linearGradient>
	
	<path id="gassesBoundary" d="M390.37,1.08v79.451h-6.405H363.94h-6.405H118.679H43.797L7.415,189.407v16.447v23.932
		l29.797,19.826H65.78l6.625-26.444l3.018-12.04h36.851v-29.295h6.405h238.856h6.405h20.025h6.405c0,3.17,0,6.339,0,9.519
		c4.277,0,8.543,0,12.82,0c3.538,0,6.405-2.861,6.405-6.389c0-56.875,0-127.017,0-183.881H390.37z M390.37,169.183h-6.405H363.94
		h-6.405H118.679h-6.405v-9.519h6.405h238.856h6.405h20.025h6.405V169.183z M390.37,147.015h-6.405H363.94h-6.405H118.679h-6.405
		v-9.509h6.405h238.856h6.405h20.025h6.405V147.015z M390.37,124.857h-6.405H363.94h-6.405H118.679h-6.405v-9.519h6.405h238.856
		h6.405h20.025h6.405V124.857z M390.37,102.689h-6.405H363.94h-6.405H118.679h-6.405V93.18h6.405h238.856h6.405h20.025h6.405V102.689
		z"/>
		
	<path id="waterBoundary" d="M357.535,181.832v29.295h-25.79V256h-12.811v-44.874H118.679v-29.295H357.535z
		 M118.679,159.664v9.519h238.856v-9.519H118.679z M118.679,137.505v9.509h238.856v-9.509H118.679z M118.679,115.338v9.519h238.856
		v-9.519H118.679z M118.679,93.18v9.509h238.856V93.18H118.679z M157.279,32.956V1h-12.81v31.956h-25.791v47.575h238.856V32.956
		H157.279z"/>
</defs>

<g id="offState" visibility="hidden">
	<use xlink:href="#gassesBoundary" class="smokeColor"/>
	<use xlink:href="#waterBoundary" class="coldColor"/>
</g>

<g id="onState" visibility="visible">
	<use xlink:href="#gassesBoundary" fill="url(#gassesGradient)"/>
	<use xlink:href="#waterBoundary" fill="url(#waterGradient)"/>
	<path fill="url(#flameGradient)" d="M55.63,214.254c0.332-2.315-0.566-5.003,0.535-6.904
		c0.544-0.949,1.613-1.707,2.661-2.262c0.396-0.203,0.78-0.383,1.197-0.534c-0.728,0.971-1.283,2.123-1.464,3.328
		c-0.192,1.238,0,2.529,0.396,3.713c0.375,1.142,0.93,2.187,1.603,3.19c0.363,0.554,0.78,1.099,0.93,1.728
		c0.139,0.599,0.054,1.27-0.139,1.856c0.673-0.725,1.218-1.664,1.336-2.656c0.118-1.045-0.256-2.155-0.802-3.053
		c0.908,0.246,1.818,0.727,2.394,1.463c0.652,0.832,0.855,2.005,0.802,3.051c0.845-0.715,1.582-1.621,1.999-2.657
		c0.514-1.281,0.544-2.774,0.268-4.117c1.154,0.917,2.19,2.262,2.395,3.723c0.149,1.173-0.235,2.42-0.802,3.455
		c0.919-0.181,1.882-0.682,2.394-1.46c0.182-0.289,0.311-0.607,0.396-0.929c0.246,1.046,0.417,2.113,0.535,3.179
		c0.169,1.591,0.203,3.201,0.105,4.791c0,0.01,0,0.01,0,0.021l-6.625,26.457H37.184L7.385,229.809v-23.951
		c0.897-0.374,1.741-0.908,2.469-1.558c0.994-0.917,1.764-2.081,2.255-3.329c0.011,0.853-0.063,1.696-0.255,2.529
		c-0.343,1.45-1.017,2.826-1.902,4.021c1.518-1.205,3.014-2.624,3.88-4.341c1.111-2.197,1.175-4.886,2.287-7.075
		c0.952-1.855,2.64-3.34,4.35-4.565c-1.357,3.042-2.308,6.272-2.747,9.59c-0.514,3.862-0.321,7.832,0.459,11.64
		c0.715-2.229,1.261-4.524,1.603-6.851c0.085-0.606,0.16-1.216,0.225-1.824c1.549,0.993,2.993,2.4,3.655,4.107
		c0.833,2.134,0.428,4.737-0.449,6.851c1.293-1.367,2.608-2.967,2.972-4.792c0.449-2.304-0.588-4.959,0.225-7.083
		c0.544-1.397,1.902-2.56,3.206-3.424c-0.77,2.005-1.336,4.354-0.684,6.39c0.501,1.6,1.741,2.999,2.052,4.566
		c0.501,2.506-1.336,5.409-3.196,7.309c1.187,0.778,2.757,1.45,4.114,1.142c1.689-0.374,3.036-2.273,3.432-4.107
		c0.535-2.508-0.716-4.898-0.685-7.308c0.011-1.707,0.674-3.425,1.368-5.026c-0.449,2.55,0.011,5.708,1.828,7.542
		c0.341,0.342,0.737,0.642,1.144,0.908c-0.342-1.014-0.375-2.315,0.235-3.19c0.182-0.267,0.428-0.491,0.685-0.694
		c0.105,0.993,0.501,2.027,1.218,2.72c0.278,0.276,0.61,0.501,0.972,0.671c-0.214-0.885-0.341-1.771-0.332-2.624
		c0.011-2.112,0.791-4.065,1.165-6.113c0.161-0.908,0.246-1.836,0.342-2.764c0.726,1.547,1.325,3.147,1.357,4.652
		c0.032,1.418-0.428,2.763-0.342,4.172c0.107,1.825,1.133,3.766,1.539,5.644c0.321,1.536,0.225,3.008,0.085,4.458
		c0.942-0.149,2.01-0.479,2.662-1.055c1.582-1.398,0.738-4.203,0.535-6.913c-0.161-2.103,0.075-4.15,1.464-5.175
		c0.587-0.438,1.379-0.682,2.126-0.8c-0.887,1.045-1.699,2.422-1.731,3.723c-0.032,1.719,1.314,3.319,1.999,5.047
		c0.759,1.91,0.717,3.968,0.664,5.975C54.498,217.454,55.384,215.951,55.63,214.254z"/>
	
	<path class="flameColor" d="M7.345,205.883
		c0.021-0.01,0.05-0.02,0.07-0.03c0.889-0.389,1.739-0.917,2.458-1.565c0.999-0.917,1.769-2.083,2.268-3.318
		c0.01,0.836-0.08,1.694-0.27,2.521c-0.34,1.445-1.009,2.83-1.898,4.016c1.519-1.206,3.018-2.621,3.887-4.335
		c1.109-2.192,1.169-4.884,2.288-7.077c0.939-1.844,2.638-3.339,4.346-4.555c-1.359,3.03-2.308,6.27-2.748,9.579
		c-0.51,3.867-0.32,7.834,0.459,11.642c0.72-2.233,1.259-4.525,1.599-6.847c0.09-0.608,0.17-1.216,0.23-1.824
		c1.549,0.986,2.998,2.392,3.657,4.106c0.83,2.133,0.43,4.735-0.45,6.847c1.289-1.365,2.608-2.97,2.968-4.794
		c0.459-2.302-0.59-4.964,0.23-7.077c0.54-1.396,1.898-2.562,3.208-3.418c-0.77,2.003-1.339,4.346-0.689,6.389
		c0.5,1.585,1.738,2.991,2.059,4.565c0.5,2.492-1.339,5.403-3.208,7.297c1.199,0.787,2.767,1.445,4.117,1.146
		c1.699-0.379,3.047-2.283,3.437-4.107c0.54-2.512-0.709-4.894-0.689-7.306c0.02-1.704,0.689-3.429,1.379-5.023
		c-0.45,2.551,0,5.701,1.829,7.535c0.339,0.349,0.729,0.648,1.139,0.907c-0.339-1.006-0.379-2.312,0.23-3.189
		c0.189-0.269,0.429-0.488,0.689-0.688c0.11,0.987,0.5,2.023,1.219,2.721c0.279,0.269,0.609,0.489,0.969,0.668
		c-0.21-0.887-0.34-1.764-0.33-2.622c0.01-2.113,0.799-4.067,1.169-6.11c0.16-0.917,0.24-1.844,0.339-2.771
		c0.729,1.555,1.329,3.149,1.359,4.665c0.04,1.416-0.43,2.751-0.35,4.167c0.11,1.814,1.14,3.758,1.539,5.642
		c0.33,1.525,0.24,3,0.09,4.455c0.949-0.149,2.009-0.488,2.668-1.066c1.579-1.386,0.729-4.187,0.529-6.897
		c-0.16-2.104,0.07-4.157,1.469-5.184c0.58-0.428,1.379-0.678,2.128-0.796c-0.89,1.046-1.709,2.421-1.739,3.718
		c-0.03,1.724,1.319,3.329,1.999,5.053c0.759,1.904,0.719,3.967,0.669,5.971c0.859-1.445,1.758-2.951,1.999-4.645
		c0.33-2.323-0.57-5.014,0.53-6.908c0.549-0.957,1.608-1.705,2.658-2.252c0.399-0.209,0.789-0.389,1.199-0.538
		c-0.719,0.977-1.279,2.123-1.459,3.319c-0.189,1.235,0.01,2.532,0.4,3.718c0.37,1.136,0.919,2.183,1.599,3.189
		c0.37,0.558,0.78,1.096,0.929,1.724c0.14,0.598,0.06,1.266-0.129,1.864c0.669-0.737,1.209-1.674,1.329-2.661
		c0.119-1.046-0.25-2.153-0.8-3.05c0.899,0.25,1.819,0.718,2.398,1.456c0.639,0.837,0.849,2.004,0.799,3.06
		c0.839-0.718,1.579-1.625,1.989-2.662c0.519-1.276,0.549-2.771,0.269-4.116c1.159,0.917,2.199,2.262,2.398,3.718
		c0.15,1.176-0.23,2.422-0.799,3.458c0.919-0.179,1.878-0.678,2.397-1.465c0.18-0.279,0.31-0.598,0.4-0.927
		c0.23,1.046,0.41,2.113,0.529,3.189c0.17,1.595,0.2,3.2,0.1,4.804"/>
</g>
	 
<path id="case" fill="url(#caseGradient)" d="M118.679,159.664v9.519h-6.405v-9.519H118.679z M112.274,137.505v9.509h6.405v-9.509H112.274z
	 M112.274,115.338v9.519h6.405v-9.519H112.274z M112.274,93.18v9.509h6.405V93.18H112.274z M383.965,159.664v9.519h6.405v-9.519
	H383.965z M357.535,159.664v9.519h6.405v-9.519H357.535z M357.535,137.505v9.509h6.405v-9.509H357.535z M383.965,137.505v9.509
	h6.405v-9.509H383.965z M383.965,115.338v9.519h6.405v-9.519H383.965z M357.535,115.338v9.519h6.405v-9.519H357.535z M357.535,93.18
	v9.509h6.405V93.18H357.535z M383.965,93.18v9.509h6.405V93.18H383.965z M357.535,181.832v29.295h-25.79V256h6.405v-38.485h25.79
	v-35.684H357.535z M7.415,189.407L43.797,80.531h74.882V32.956h25.791V1h-6.405v25.567h-25.791v47.565H39.181L1,188.371v44.834
	L35.273,256h35.503l9.643-38.485H312.53V256h6.405v-44.874H118.679v-29.295h-6.405v29.295H75.423l-3.018,12.04l-6.625,26.444H37.212
	L7.415,229.785v-23.932V189.407z M163.685,26.567V1h-6.405v31.956h200.256v47.575h6.405V26.567H163.685z M383.965,1.08v79.451h6.405
	V1.08H383.965z M409.595,1.08c0,56.865,0,127.006,0,183.881c0,3.528-2.868,6.389-6.405,6.389c-4.277,0-8.543,0-12.82,0
	c0-3.18,0-6.349,0-9.519h-6.405c0,5.302,0,10.605,0,15.908c6.405,0,12.82,0,19.225,0c7.075,0,12.81-5.722,12.81-12.778
	c0-56.875,0-127.017,0-183.881H409.595z"/>

<g id="stroked">
	<g class="inlineColor">
		<path d="M390.37,181.832c0,3.17,0,6.339,0,9.519c4.277,0,8.543,0,12.82,0c3.538,0,6.405-2.861,6.405-6.389c0-56.875,0-127.017,0-183.881c0-0.05,0-0.109,0-0.16"/>
		<line x1="118.679" y1="102.689" x2="118.679" y2="93.18"/>
		<line x1="357.535" y1="102.689" x2="357.535" y2="93.18"/>
		<polyline points="157.279,1 157.279,32.956 357.535,32.956 357.535,80.531 	"/>
		<polyline points="144.47,1 144.47,32.956 118.679,32.956 118.679,80.531 	"/>
		<polyline points="318.935,256 318.935,211.126 118.679,211.126 118.679,181.832 	"/>
		<polyline points="357.535,181.832 357.535,211.126 331.745,211.126 331.745,256 	"/>
		<polyline points="363.94,80.531 43.797,80.531 
			7.415,189.407 7.415,229.785 37.212,249.611 65.78,249.611 75.423,211.126 112.274,211.126 112.274,181.832 363.94,181.832 	"/>
		<polyline points="390.37,1.08 390.37,80.531 383.965,80.531 	"/>
		<polyline points="363.94,93.18 112.274,93.18 112.274,102.689 363.94,102.689 	"/>
		<polyline points="383.965,93.18 390.37,93.18 390.37,102.689 383.965,102.689 	"/>
		<line x1="118.679" y1="124.852" x2="118.679" y2="115.343"/>
		<line x1="357.535" y1="124.852" x2="357.535" y2="115.343"/>
		<polyline points="363.94,115.343 112.274,115.343 112.274,124.852 363.94,124.852 	"/>
		<polyline points="383.965,115.343 390.37,115.343 390.37,124.852 383.965,124.852 	"/>
		<line x1="118.679" y1="147.015" x2="118.679" y2="137.505"/>
		<line x1="357.535" y1="147.015" x2="357.535" y2="137.505"/>
		<polyline points="363.94,137.505 112.274,137.505 112.274,147.015 363.94,147.015 	"/>
		<polyline points="383.965,137.505 390.37,137.505 390.37,147.015 383.965,147.015 	"/>
		<line x1="118.679" y1="169.177" x2="118.679" y2="159.668"/>
		<line x1="357.535" y1="169.177" x2="357.535" y2="159.668"/>
		<polyline points="363.94,159.668 112.274,159.668 112.274,169.177 363.94,169.177 	"/>
		<polyline points="383.965,159.668 390.37,159.668 390.37,169.177 383.965,169.177 	"/>
	</g>

	<g class="boundaryColor">
		<rect x="363.94" y="93.18"  />
		<rect x="363.94" y="115.338"  />
		<rect x="363.94" y="137.505"  />
		<rect x="363.94" y="159.664"  />
		<path d="M383.965,181.832v15.908c0,0,12.82,0,19.225,0
			c7.075,0,12.81-5.722,12.81-12.778c0-56.875,0-183.881,0-183.881h-32.035v79.451H363.94V26.567H163.685V1h-25.62v25.567h-25.791
			v47.565H39.181L1,188.371v44.834L35.273,256h35.503l9.643-38.485H312.53V256h25.621v-38.485h25.79v-35.684H383.965z"/>
	</g>
</g>
</svg>
