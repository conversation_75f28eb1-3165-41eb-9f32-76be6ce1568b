<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 252 265" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param name="coldColor" description="Cold Color" type="C" cssAttributes="stop-color,fill" classes="coldColor" forceRepaint="true"/>
	<agg:param name="hotColor" description="Hot Color" type="C" cssAttributes="stop-color" classes="hotColor" forceRepaint="true"/>
	<agg:param name="furnaceState" description="Furnace State" type="state">
		<agg:state name="on" description="On">
			<agg:param attributes="visibility" ids="offState" value="hidden"/>
			<agg:param attributes="visibility" ids="onState" value="visible"/>
		</agg:state>
		<agg:state name="off" description="Off">
			<agg:param attributes="visibility" ids="offState" value="visible"/>
			<agg:param attributes="visibility" ids="onState" value="hidden"/>
		</agg:state>
	</agg:param>
</agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.flameColor,.inlineColor,.boundaryColor{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		stroke-width:1px;
	}

	.caseColor {
		fill:#4C5B75;
	}
	
	.flameColor {
		stroke:#CE0000;
	}
	
	.inlineColor {
		stroke:white;
		opacity:0.75;
	}
	
	.boundaryColor {
		stroke:black;
		stroke-width:1.5px;
		opacity:0.75;
	}
	
	.coldColor {
		stop-color:#29B1FF;
		fill:#29B1FF;
	}

	.hotColor {
		stop-color:#FF0000;
	}
	
	.smokeColor {
		fill:#7D7D7D
	}

      ]]>
</style>
	 
<defs>	 
	<linearGradient id="gassesGradient" gradientUnits="userSpaceOnUse" x1="115.4731" y1="255.2651" x2="166.844" y2="-8.0105">
		<stop  offset="0" style="stop-color:#FCDA2F"/>
		<stop  offset="0.0596" style="stop-color:#FCCC29"/>
		<stop  offset="0.1722" style="stop-color:#FCA71A"/>
		<stop  offset="0.3248" style="stop-color:#FC6C01"/>
		<stop  offset="0.3333" style="stop-color:#FC6900"/>
		<stop  offset="0.5853" style="stop-color:#CA8A5B"/>
		<stop  offset="0.7939" style="stop-color:#A3A3A3"/>
		<stop  offset="1" style="stop-color:#7D7D7D"/>
	</linearGradient>
	
	<linearGradient id="waterGradient" gradientUnits="userSpaceOnUse" x1="0" y1="237.5415" x2="0" y2="14.063">
		<stop  offset="0" class="coldColor"/>
		<stop  offset="1" class="hotColor"/>
	</linearGradient>

	<radialGradient id="flameGradient" cx="34.9963" cy="254.1504" r="47.2673" gradientTransform="matrix(1 0 0 1 0 0.346)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#FCDA2F"/>
		<stop  offset="0.1084" style="stop-color:#FCCC29"/>
		<stop  offset="0.3132" style="stop-color:#FCA71A"/>
		<stop  offset="0.5906" style="stop-color:#FC6C01"/>
		<stop  offset="0.6061" style="stop-color:#FC6900"/>
		<stop  offset="1" style="stop-color:#FC0000"/>
	</radialGradient>
	
	<linearGradient id="caseGradient" gradientUnits="userSpaceOnUse" x1="1" y1="1" x2="260" y2="260">
		<stop  offset="0" style="stop-color:#4C5B75"/>
		<stop  offset="0.5" style="stop-color:#C1CEE0"/>
		<stop  offset="1" style="stop-color:#4C5B75"/>
	</linearGradient>
	
	<path id="gassesBoundary" d="M222.153,193.434c8.856,0,16.023,7.182,16.023,16.044v28.867c-4.268,0-8.536,0-12.814,0
		v-32.078H78.622l-12.814,51.322h-28.58L7.407,237.691v-40.522L57.796,45.9h122.695V13.833c4.268,0,8.536,0,12.814,0V45.9h32.057V1
		c6.407,0,12.824,0,19.231,0v147.533c0,10.628-8.61,19.245-19.231,19.245h-32.057v25.656H222.153z"/>
		
	<path id="waterBoundary" d="M238.176,209.478v28.867c-4.268,0-8.536,0-12.814,0v-32.078H93.95v-12.833h12.824V65.145
		H93.95V45.9h86.542V13.833c4.268,0,8.536,0,12.814,0V45.9v19.245h-16.022v128.289h16.022h28.848
		C231.009,193.434,238.176,200.615,238.176,209.478z M170.876,193.434V65.145h-6.418v128.289H170.876z M158.051,193.434V65.145
		h-6.406v128.289H158.051z M145.227,193.434V65.145h-6.407v128.289H145.227z M132.413,193.434V65.145h-6.417v128.289H132.413z
		 M119.588,193.434V65.145h-6.407v128.289H119.588z"/>
</defs>
	
<g id="offState" visibility="hidden">
	<use xlink:href="#gassesBoundary" class="smokeColor"/>
	<use xlink:href="#waterBoundary" class="coldColor"/>
</g>

<g id="onState" visibility="visible">
	<use xlink:href="#gassesBoundary" fill="url(#gassesGradient)"/>
	<use xlink:href="#waterBoundary" fill="url(#waterGradient)"/>
	<path id="flame" fill="url(#flameGradient)" d="M55.689,222.086c0.332-2.323-0.567-5.02,0.535-6.926c0.545-0.952,1.615-1.712,2.663-2.269
		c0.396-0.204,0.781-0.385,1.198-0.535c-0.728,0.973-1.284,2.129-1.465,3.338c-0.192,1.242,0,2.537,0.396,3.725
		c0.375,1.146,0.931,2.194,1.604,3.201c0.364,0.556,0.781,1.102,0.931,1.734c0.139,0.6,0.054,1.274-0.139,1.862
		c0.674-0.728,1.219-1.67,1.337-2.665c0.118-1.049-0.256-2.162-0.802-3.062c0.909,0.246,1.819,0.728,2.396,1.467
		c0.652,0.835,0.855,2.012,0.802,3.061c0.845-0.717,1.583-1.627,2-2.665c0.514-1.285,0.545-2.783,0.268-4.131
		c1.155,0.92,2.192,2.269,2.396,3.735c0.15,1.177-0.235,2.429-0.802,3.467c0.92-0.182,1.883-0.685,2.396-1.466
		c0.182-0.289,0.311-0.609,0.396-0.931c0.246,1.049,0.417,2.119,0.535,3.189c0.17,1.595,0.203,3.211,0.106,4.806
		c0,0.01,0,0.01,0,0.021l-6.631,26.543h-28.58L7.407,237.691v-24.029c0.898-0.375,1.743-0.91,2.471-1.563
		c0.995-0.92,1.765-2.087,2.257-3.339c0.011,0.856-0.064,1.702-0.256,2.537c-0.343,1.455-1.017,2.836-1.904,4.035
		c1.519-1.209,3.016-2.633,3.883-4.356c1.112-2.205,1.176-4.902,2.289-7.097c0.952-1.862,2.642-3.351,4.353-4.581
		c-1.358,3.051-2.31,6.293-2.749,9.622c-0.514,3.875-0.321,7.857,0.46,11.677c0.716-2.236,1.262-4.538,1.604-6.872
		c0.085-0.609,0.16-1.22,0.225-1.83c1.55,0.996,2.995,2.408,3.658,4.121c0.834,2.14,0.428,4.752-0.449,6.872
		c1.294-1.371,2.61-2.976,2.974-4.807c0.449-2.311-0.588-4.976,0.225-7.106c0.545-1.402,1.904-2.569,3.208-3.436
		c-0.77,2.012-1.337,4.367-0.684,6.411c0.502,1.605,1.743,3.008,2.054,4.581c0.502,2.515-1.337,5.426-3.199,7.332
		c1.188,0.781,2.76,1.456,4.118,1.146c1.69-0.375,3.038-2.28,3.434-4.121c0.535-2.516-0.717-4.913-0.685-7.332
		c0.011-1.712,0.674-3.436,1.369-5.042c-0.449,2.559,0.011,5.727,1.829,7.567c0.342,0.343,0.738,0.643,1.145,0.911
		c-0.342-1.017-0.375-2.323,0.235-3.201c0.182-0.267,0.428-0.492,0.685-0.696c0.106,0.996,0.502,2.034,1.219,2.729
		c0.278,0.278,0.61,0.503,0.973,0.674c-0.214-0.888-0.342-1.777-0.332-2.633c0.011-2.119,0.792-4.078,1.166-6.133
		c0.161-0.91,0.246-1.841,0.342-2.772c0.727,1.552,1.326,3.157,1.358,4.667c0.032,1.423-0.428,2.771-0.342,4.185
		c0.107,1.831,1.134,3.778,1.54,5.662c0.321,1.542,0.225,3.018,0.085,4.474c0.942-0.15,2.011-0.481,2.664-1.059
		c1.583-1.402,0.738-4.217,0.535-6.936c-0.161-2.109,0.075-4.164,1.465-5.191c0.588-0.439,1.38-0.685,2.128-0.803
		c-0.888,1.049-1.701,2.43-1.733,3.735c-0.032,1.724,1.315,3.33,2,5.063c0.759,1.916,0.717,3.981,0.664,5.994
		C54.556,225.297,55.443,223.788,55.689,222.086z"/>
	<path class="flameColor" d="
		M7.343,213.695c0.021-0.011,0.043-0.021,0.064-0.032c0.898-0.375,1.743-0.91,2.471-1.563c0.995-0.92,1.765-2.087,2.257-3.339
		c0.011,0.856-0.064,1.702-0.256,2.537c-0.343,1.455-1.017,2.836-1.904,4.035c1.519-1.209,3.016-2.633,3.883-4.356
		c1.112-2.205,1.176-4.902,2.289-7.097c0.952-1.862,2.642-3.351,4.353-4.581c-1.358,3.051-2.31,6.293-2.749,9.622
		c-0.514,3.875-0.321,7.857,0.46,11.677c0.716-2.236,1.262-4.538,1.604-6.872c0.085-0.609,0.16-1.22,0.225-1.83
		c1.55,0.996,2.995,2.408,3.658,4.121c0.834,2.14,0.428,4.752-0.449,6.872c1.294-1.371,2.61-2.976,2.974-4.807
		c0.449-2.311-0.588-4.976,0.225-7.106c0.545-1.402,1.904-2.569,3.208-3.436c-0.77,2.012-1.337,4.367-0.684,6.411
		c0.502,1.605,1.743,3.008,2.054,4.581c0.502,2.515-1.337,5.426-3.199,7.332c1.188,0.781,2.76,1.456,4.118,1.146
		c1.69-0.375,3.038-2.28,3.434-4.121c0.535-2.516-0.717-4.913-0.685-7.332c0.011-1.712,0.674-3.436,1.369-5.042
		c-0.449,2.559,0.011,5.727,1.829,7.567c0.342,0.343,0.738,0.643,1.145,0.911c-0.342-1.017-0.375-2.323,0.235-3.201
		c0.182-0.267,0.428-0.492,0.685-0.696c0.106,0.996,0.502,2.034,1.219,2.729c0.278,0.278,0.61,0.503,0.973,0.674
		c-0.214-0.888-0.342-1.777-0.332-2.633c0.011-2.119,0.792-4.078,1.166-6.133c0.161-0.91,0.246-1.841,0.342-2.772
		c0.727,1.552,1.326,3.157,1.358,4.667c0.032,1.423-0.428,2.771-0.342,4.185c0.107,1.831,1.134,3.778,1.54,5.662
		c0.321,1.542,0.225,3.018,0.085,4.474c0.942-0.15,2.011-0.481,2.664-1.059c1.583-1.402,0.738-4.217,0.535-6.936
		c-0.161-2.109,0.075-4.164,1.465-5.191c0.588-0.439,1.38-0.685,2.128-0.803c-0.888,1.049-1.701,2.43-1.733,3.735
		c-0.032,1.724,1.315,3.33,2,5.063c0.759,1.916,0.717,3.981,0.664,5.994c0.866-1.456,1.754-2.965,2-4.666
		c0.332-2.323-0.567-5.02,0.535-6.926c0.545-0.952,1.615-1.712,2.663-2.269c0.396-0.204,0.781-0.385,1.198-0.535
		c-0.728,0.973-1.284,2.129-1.465,3.338c-0.192,1.242,0,2.537,0.396,3.725c0.375,1.146,0.931,2.194,1.604,3.201
		c0.364,0.556,0.781,1.102,0.931,1.734c0.139,0.6,0.054,1.274-0.139,1.862c0.674-0.728,1.219-1.67,1.337-2.665
		c0.118-1.049-0.256-2.162-0.802-3.062c0.909,0.246,1.819,0.728,2.396,1.467c0.652,0.835,0.855,2.012,0.802,3.061
		c0.845-0.717,1.583-1.627,2-2.665c0.514-1.285,0.545-2.783,0.268-4.131c1.155,0.92,2.192,2.269,2.396,3.735
		c0.15,1.177-0.235,2.429-0.802,3.467c0.92-0.182,1.883-0.685,2.396-1.466c0.182-0.289,0.311-0.609,0.396-0.931
		c0.246,1.049,0.417,2.119,0.535,3.189c0.17,1.595,0.203,3.211,0.106,4.806c0,0.01,0,0.01,0,0.021"/>
</g>
	
<path id="case" fill="url(#caseGradient)" d="M222.153,187.022c12.396,0,22.44,10.051,22.44,22.456c0,9.623,0,19.244,0,28.867
	c-2.139,0-4.278,0-6.417,0v-28.867c0-8.862-7.167-16.044-16.023-16.044h-28.848v-25.656h16.034h16.023
	c10.622,0,19.231-8.616,19.231-19.245V1c2.139,0,4.268,0,6.407,0c0,49.182,0,98.352,0,147.533c0,14.171-11.477,25.656-25.638,25.656
	c-5.348,0-10.686,0-16.023,0c-3.208,0-6.417,0-9.616,0c0,4.281,0,8.563,0,12.833C207.2,187.022,214.676,187.022,222.153,187.022z
	 M93.95,206.267H78.622l-6.183,24.757c0,0.01,0,0.01,0,0.021l-6.631,26.543h-28.58L7.407,237.691v-24.029v-16.494L57.796,45.9H93.95
	h86.542V13.833c-2.139,0-4.278,0-6.417,0c0,8.551,0,17.104,0,25.656c-40.292,0-80.595,0-120.898,0
	C35.784,91.7,18.392,143.91,1,196.12c0,15.006,0,30.001,0,45.008c11.424,7.621,22.858,15.241,34.281,22.873
	c11.84,0,23.692,0,35.532,0c4.268-17.104,8.546-34.218,12.814-51.322c45.106,0,90.211,0,135.316,0c0,8.552,0,17.104,0,25.666
	c2.14,0,4.279,0,6.418,0v-32.078H93.95z M218.944,1v38.489h-19.221V13.833c0,0-4.279,0-6.418,0V45.9h32.057V1H218.944z"/>
	
<g id="strokes">
	<g id="white" class="inlineColor">
		<polyline points="93.95,45.9 93.95,65.145 106.774,65.145 106.774,193.434 93.95,193.434 93.95,206.267"/>
		<rect x="164.458" y="65.145"  />
		<rect x="151.645" y="65.145"  />
		<rect x="138.819" y="65.145"  />
		<rect x="125.995" y="65.145"  />
		<rect x="113.181" y="65.145"  />
		<path d="M238.176,238.344
			v-28.867c0-8.862-7.167-16.044-16.023-16.044h-44.87V65.145h16.022V45.9V13.833"/>
		<polyline points="225.362,1 225.362,45.9 193.305,45.9"/>
		<path d="M193.305,193.434v-25.656h32.057c10.622,0,19.231-8.616,19.231-19.245V1"/>
		<polyline points="180.491,13.833 180.491,45.9 57.796,45.9 7.407,197.169 7.407,237.691 37.228,257.588 65.808,257.588 78.622,206.267 
			225.362,206.267 225.362,238.344"/>
	</g>
	
	<path id="boundary" class="boundaryColor" d="
		M218.944,1v38.489h-19.221V13.833h-25.649v25.656H53.176L1,196.12v45.008L35.281,264h35.532l12.814-51.322h135.316v25.666h25.649
		c0,0,0-19.244,0-28.867c0-12.405-10.044-22.456-22.44-22.456c-7.477,0-22.43,0-22.43,0v-12.833c0,0,20.291,0,25.639,0
		c14.161,0,25.638-11.484,25.638-25.656C251,99.352,251,1,251,1H218.944z"/>
</g>
</svg>
