<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 160 202" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param name="coldColor" description="Cold Color" type="C" cssAttributes="stop-color,fill" classes="coldColor" forceRepaint="true"/>
	<agg:param name="hotColor" description="Hot Color" type="C" cssAttributes="stop-color,fill" classes="hotColor" forceRepaint="true"/>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>	 
	 
<style type="text/css" >
   <![CDATA[

	.flameColor,.inlineColor,.boundaryColor{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		stroke-width:1px;
	}
	
	.inlineColor {
		stroke:white;
		opacity:0.75;
	}
	
	.boundaryColor {
		stroke:black;
		stroke-width:1.5px;
		opacity:0.75;
	}
	
	.coldColor {
		stop-color:#29B1FF;
		fill:#29B1FF;
	}

	.hotColor {
		stop-color:#FF0000;
		fill:#FF0000;
	}

	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="flat" gradientUnits="userSpaceOnUse" x1="55.647" y1="79.0532" x2="122.1901" y2="175.5184">
		<stop  offset="0.0051" style="stop-color:#4D5C75"/>
		<stop  offset="0.1609" style="stop-color:#5E6C83"/>
		<stop  offset="0.4695" style="stop-color:#8B96A7"/>
		<stop  offset="0.5576" style="stop-color:#99A3B2"/>
		<stop  offset="0.5636" style="stop-color:#AEB7C4"/>
		<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
	</linearGradient>
	
	<radialGradient id="flameGradient" cx="48.0972" cy="167.9473" r="100.5324" gradientTransform="matrix(0.9991 -0.0178 0.0049 0.2754 -0.8129 122.5454)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#FCDA2F"/>
		<stop  offset="0.1084" style="stop-color:#FCCC29"/>
		<stop  offset="0.3132" style="stop-color:#FCA71A"/>
		<stop  offset="0.5906" style="stop-color:#FC6D01"/>
		<stop  offset="0.6061" style="stop-color:#FC6900"/>
		<stop  offset="1" style="stop-color:#FC0000"/>
	</radialGradient>
	
	<radialGradient id="pumpGradient" cx="18.7305" cy="174.8486" r="17.2839" gradientTransform="matrix(0.9992 0 0 1 0.0012 0)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#F6FAFC"/>
		<stop  offset="0.3756" style="stop-color:#89878B"/>
		<stop  offset="0.7056" style="stop-color:#F6FAFC"/>
		<stop  offset="0.7572" style="stop-color:#F2F6F8"/>
		<stop  offset="0.8107" style="stop-color:#E5EAEE"/>
		<stop  offset="0.865" style="stop-color:#D1D7DC"/>
		<stop  offset="0.92" style="stop-color:#B4BBC3"/>
		<stop  offset="0.9748" style="stop-color:#8F98A3"/>
		<stop  offset="1" style="stop-color:#7B8692"/>
	</radialGradient>
	
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0.0051" style="stop-color:#4D5C75"/>
		<stop  offset="0.0311" style="stop-color:#616F85"/>
		<stop  offset="0.097" style="stop-color:#8E99A9"/>
		<stop  offset="0.1634" style="stop-color:#B4BCC7"/>
		<stop  offset="0.2292" style="stop-color:#D1D7DE"/>
		<stop  offset="0.2942" style="stop-color:#E5EAEF"/>
		<stop  offset="0.3583" style="stop-color:#F2F6F9"/>
		<stop  offset="0.42" style="stop-color:#F6FAFC"/>
		<stop  offset="0.4711" style="stop-color:#F2F6F8"/>
		<stop  offset="0.5241" style="stop-color:#E6E9EC"/>
		<stop  offset="0.5779" style="stop-color:#D3D5D7"/>
		<stop  offset="0.6324" style="stop-color:#B7B8BA"/>
		<stop  offset="0.6868" style="stop-color:#949396"/>
		<stop  offset="0.7005" style="stop-color:#8A888B"/>
		<stop  offset="1" style="stop-color:#F6FAFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="37.1636" y1="73.4312" x2="70.3851" y2="73.4312"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="37.4453" y1="18.6675" x2="69.7651" y2="18.6675"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="18.749" y1="59.4302" x2="81.6693" y2="33.0823"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="21.1206" y1="53.876" x2="85.8755" y2="53.876"/>
	
	<path id="flame" d="M49.439,172.123c3.481,0,7.067,1.756,9.905,3.82c6.691,4.869,9.218,11.459,14.489,13.088
		c1.226,0.377,2.6,0.488,3.916,0.521c-3.163-1.244-8.895-4.277-7.832-6.531c0.537-1.139,2.808-2.076,4.438-2.611
		c2.939-0.842,6.185-1.34,9.137-0.785c4.397,0.828,8.139,3.992,12.269,6.01c3.247,1.586,6.728,2.463,10.182,3.396
		c-3.922-1.27-8.138-4.033-8.876-8.621c-0.155-0.973-0.155-2.031,0.262-2.613c1.01-1.414,4.458-0.059,6.266,1.045
		c3.26,1.541,6.672,2.645,10.18,3.658c3.343,0.965,6.773,1.854,10.182,1.566c1.594-0.133,3.182-0.521,4.7-1.045
		c-2.225-0.289-4.948-1.279-5.914-3.219c-0.734-1.475-0.453-3.5,0.079-5.123c1.991-1.445,4.178-2.982,6.747-3.002
		c3.251-0.023,7.118,2.387,9.285,1.094c1.602-0.953,2.276-3.916,3.826-5.74c1.674-1.973,4.373-2.605,6.827-3.006
		c-2.635-2.834-6.82-6.549-10.379-5.193c-2.905,1.105-5.394,5.588-7.921,5.193c-1.7-0.266-3.416-2.74-4.375-4.639
		c0.355-2.873,1.214-5.73,2.637-8.275c1.538-2.75,3.734-5.137,6.235-7.016c-2.78-0.727-5.956-1.086-8.615-0.262
		c-4.028,1.248-6.877,5.209-10.441,7.84c-4.737,3.488-10.731,4.631-16.448,5.486c-1.388,0.205-3.025-0.215-2.845-1.324
		c0.355-2.193,7.826-7.08,9.371-9.389c-0.988,0.695-2.03,1.365-3.131,1.83c-4.377,1.834-9.685,0.336-15.154,1.92
		c-6.04,1.748-12.275,7.256-16.938,9.799c-1.231,0.674-4.493,0.523-5.392-0.404c-1.779-1.84,5.693-6.725,7.239-9.033
		c-4.741,1.271-9.542,2.131-13.685,5.439c-3.193,2.547-5.999,6.551-9.432,6.25c-0.259-0.021-0.521-0.07-0.792-0.07"/>
</defs>
	 

<rect id="case" x="44.799" y="91.208" fill="url(#flat)"  />

<use xlink:href="#flame" fill="url(#flameGradient)"/>
	
<rect x="49.439" y="95.851" class="coldColor"  />
	
<g id="grid" class="inlineColor">
	<line x1="49.439" y1="135.422" x2="154.36" y2="135.422"/>
	<line x1="154.36" y1="121.944" x2="49.439" y2="121.944"/>
	<line x1="49.439" y1="108.858" x2="154.36" y2="108.858"/>
	<line x1="143.034" y1="135.422" x2="143.034" y2="95.851"/>
	<line x1="132.495" y1="135.422" x2="132.495" y2="95.851"/>
	<line x1="122.034" y1="135.422" x2="122.034" y2="95.851"/>
	<line x1="111.967" y1="135.422" x2="111.967" y2="95.851"/>
	<line x1="101.427" y1="135.422" x2="101.427" y2="95.851"/>
	<line x1="90.967" y1="135.422" x2="90.967" y2="95.851"/>
	<line x1="80.9" y1="135.422" x2="80.9" y2="95.851"/>
	<line x1="70.361" y1="135.422" x2="70.361" y2="95.851"/>
	<line x1="60.292" y1="135.422" x2="60.292" y2="95.851"/>
	
	<use xlink:href="#flame"/>
</g>

<g id="outlet" transform="translate(48.5 5.512)">
	<rect x="37.445" y="61.167" fill="url(#cyl_1)"  />
	<path fill="url(#cyl_2)" d="M64.974,2.51c-3.977-1.527-8.617,1.342-12.28,4.883c-0.332-0.906-0.729-1.779-1.309-2.567
		C48.6,1.083,41.738-0.326,39.087,2.477c-1.642,1.745-1.642,5.117-1.642,7.751v26.107h32.104V10.228
		C70.545,7.225,67.941,3.651,64.974,2.51z"/>
	<polygon fill="url(#cyl_3)" points="85.875,46.586 21.121,46.586 37.445,36.335 69.549,36.335 	"/>
	<rect x="21.121" y="46.586" fill="url(#cyl_4)"  />
	<path opacity="0.3" d="M64.758,2.587c2.967,1.142,5.57,4.715,4.576,7.718c-1.558,4.765-10.939,6.191-14.354,2.685
		c-1.343-1.359-1.757-3.49-2.502-5.52C56.139,3.93,60.781,1.061,64.758,2.587z"/>
</g>

<path fill="url(#pumpGradient)" d="M44.527,157.174H18.501C8.835,157.174,1,165.014,1,174.688
	c0,9.672,7.835,17.514,17.501,17.514c8.865,0,16.171-6.602,17.324-15.158h8.702V157.174z"/>

<path class="color" d="M118.049,91.208V66.679h16.327V52.098h-0.001l-16.326-10.251V15.74
	c0.996-3.003-1.607-6.577-4.574-7.718c-1.758-0.675-3.645-0.489-5.52,0.232c-2.374,0.859-4.734,2.593-6.798,4.56
	c-0.325-0.874-0.712-1.715-1.272-2.476C97.1,6.595,90.238,5.186,87.587,7.989c-1.642,1.745-1.642,5.117-1.642,7.751v26.107
	L69.621,52.098v14.581h16.324v24.529H44.799v65.966H18.501C8.835,157.174,1,165.014,1,174.688c0,9.672,7.835,17.515,17.501,17.515
	c8.865,0,16.171-6.603,17.324-15.158h8.974V201H159V91.208H118.049z M142.68,167.031c-1.55,1.824-2.224,4.787-3.826,5.74
	c-2.166,1.293-6.033-1.117-9.285-1.095c-2.568,0.021-4.756,1.558-6.746,3.002c-0.532,1.623-0.813,3.648-0.079,5.123
	c0.966,1.94,3.688,2.931,5.914,3.22c-1.519,0.523-3.106,0.912-4.7,1.045c-3.409,0.287-6.839-0.602-10.182-1.566
	c-3.508-1.014-6.92-2.117-10.18-3.658c-1.809-1.104-5.257-2.459-6.267-1.045c-0.417,0.582-0.417,1.641-0.263,2.613
	c0.738,4.588,4.955,7.351,8.877,8.621c-3.455-0.934-6.936-1.811-10.182-3.396c-4.131-2.018-7.873-5.182-12.27-6.01
	c-2.952-0.555-6.198-0.057-9.137,0.785c-1.63,0.535-3.901,1.472-4.438,2.611c-1.063,2.254,4.669,5.287,7.832,6.53
	c-1.316-0.032-2.69-0.144-3.916-0.521c-5.271-1.629-7.798-8.219-14.489-13.088c-2.838-2.064-6.424-3.82-9.905-3.82l0.002-9.946
	c0.271,0,0.533,0.05,0.792,0.07c3.433,0.301,6.239-3.703,9.432-6.25c4.143-3.308,8.944-4.168,13.685-5.438
	c-1.546,2.308-9.018,7.192-7.239,9.032c0.899,0.927,4.161,1.078,5.392,0.404c4.663-2.543,10.897-8.052,16.938-9.8
	c5.469-1.584,10.776-0.086,15.154-1.92c1.1-0.465,2.143-1.135,3.131-1.83c-1.545,2.31-9.017,7.197-9.371,9.39
	c-0.181,1.108,1.457,1.529,2.845,1.324c5.717-0.855,11.711-1.998,16.448-5.486c3.563-2.631,6.412-6.592,10.44-7.84
	c2.659-0.824,5.835-0.465,8.615,0.262c-2.501,1.88-4.697,4.267-6.235,7.017c-1.423,2.545-2.281,5.402-2.637,8.274
	c0.959,1.899,2.675,4.373,4.375,4.64c2.527,0.395,5.016-4.088,7.921-5.193c3.56-1.355,7.744,2.359,10.379,5.193
	C147.053,164.426,144.354,165.059,142.68,167.031z M154.359,135.422H49.439V95.851h104.92V135.422z"/>

<g transform="translate(48.5 5.512)">
	<g class="boundaryColor">
		<path d="M69.549,10.228c0.996-3.003-1.608-6.577-4.576-7.718c-3.977-1.527-8.617,1.342-12.28,4.883"/>
		<path d="M37.445,10.228
			c0-2.635,0-6.007,1.642-7.751c2.651-2.802,9.513-1.393,12.298,2.349c0.581,0.789,0.977,1.662,1.309,2.567
			c0.745,2.03,1.16,4.161,2.502,5.52c3.414,3.506,12.794,2.08,14.354-2.685"/>
		<line x1="37.445" y1="36.335" x2="69.549" y2="36.335"/>
		<line x1="21.121" y1="46.586" x2="85.875" y2="46.586"/>
		<line x1="37.445" y1="61.167" x2="69.549" y2="61.167"/>
		<polyline points="69.549,85.696 69.549,61.167 85.875,61.167 85.875,46.586 69.549,36.335 69.549,10.228 	"/>
		<polyline points="37.445,85.696 37.445,61.167 21.121,61.167 21.121,46.586 37.445,36.335 37.445,10.228 	"/>
	</g>
</g>
	
<g class="boundaryColor">
	<path d="M44.527,157.174H18.501C8.835,157.174,1,165.014,1,174.688c0,9.672,7.835,17.514,17.501,17.514c8.865,0,16.171-6.602,17.324-15.158h8.702"/>
	<rect x="49.439" y="95.851"  />
	<rect x="44.798" y="91.208"  />
	<line fill="none" x1="49.439" y1="172.123" x2="44.799" y2="172.123"/>
	<line fill="none" x1="49.439" y1="162.178" x2="44.799" y2="162.178"/>
</g>
</svg>
