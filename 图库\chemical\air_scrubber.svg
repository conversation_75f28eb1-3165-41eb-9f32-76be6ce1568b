<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 13.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 14948)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="-14.846 0 80.5 90" enable-background="new -14.846 0 80.5 90" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="base" gradientUnits="userSpaceOnUse" x1="22.1858" y1="44.7817" x2="28.7083" y2="44.7817">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	<linearGradient id="body1" gradientUnits="userSpaceOnUse" x1="25.447" y1="8.8398" x2="25.447" y2="73.4663">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0131" style="stop-color:#868686"/>
		<stop  offset="0.0448" style="stop-color:#aaaaaa"/>
		<stop  offset="0.0767" style="stop-color:#c7c7c7"/>
		<stop  offset="0.1083" style="stop-color:#dedede"/>
		<stop  offset="0.1396" style="stop-color:#efefef"/>
		<stop  offset="0.1703" style="stop-color:#f9f9f9"/>
		<stop  offset="0.2" style="stop-color:#fcfcfc"/>
		<stop  offset="0.6709" style="stop-color:#fdfdfd"/>
		<stop  offset="0.9" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#b0b0b0"/>
	</linearGradient>
	<linearGradient id="body2"  xlink:href="#base"  x1="5.8801" y1="41.3369" x2="45.0149" y2="41.3369">
		<stop  offset="0.0051" style="stop-color:#757575"/>
		<stop  offset="0.0171" style="stop-color:#868686"/>
		<stop  offset="0.046" style="stop-color:#aaaaaa"/>
		<stop  offset="0.0751" style="stop-color:#c7c7c7"/>
		<stop  offset="0.104" style="stop-color:#dedede"/>
		<stop  offset="0.1326" style="stop-color:#efefef"/>
		<stop  offset="0.1607" style="stop-color:#f9f9f9"/>
		<stop  offset="0.1878" style="stop-color:#fcfcfc"/>
		<stop  offset="0.3085" style="stop-color:#f8f8f8"/>
		<stop  offset="0.4335" style="stop-color:#ececec"/>
		<stop  offset="0.5606" style="stop-color:#d7d7d7"/>
		<stop  offset="0.689" style="stop-color:#bbbbbb"/>
		<stop  offset="0.8173" style="stop-color:#969696"/>
		<stop  offset="0.8528" style="stop-color:#8b8b8b"/>
	</linearGradient>
	<linearGradient id="base2" xlink:href="#base" x1="20.9592" y1="0" x2="29.9348" y2="0"/>
	<linearGradient id="base3" xlink:href="#base" x1="0.9758" y1="0" x2="49.9192" y2="0"/>
	<linearGradient id="base4" xlink:href="#base" x1="0" y1="38.0869" x2="0" y2="57.5347"/>
	<linearGradient id="base5" xlink:href="#base" x1="0" y1="41.2158" x2="0" y2="53.9663"/>
		
	<path id="scrubber_body" d="M5.88,67.334c0,1.944,2.231,3.113,3.577,3.747c1.666,0.784,3.388,1.275,5.173,1.669
		c3.587,0.791,7.157,1.084,10.816,1.084s7.229-0.293,10.815-1.083c1.785-0.394,3.507-0.885,5.173-1.67
		c1.346-0.634,3.58-1.802,3.58-3.747c0-17.331,0-34.662,0-51.994c0-1.944-2.232-3.113-3.578-3.746
		c-1.666-0.784-3.387-1.275-5.172-1.67c-3.588-0.79-7.158-1.084-10.818-1.084c-3.658,0-7.228,0.293-10.813,1.083
		c-1.786,0.394-3.508,0.886-5.175,1.67C8.113,12.227,5.88,13.396,5.88,15.34C5.88,32.672,5.88,50.003,5.88,67.334z"/>
</defs>

<rect x="22.186" y="5.592" fill="url(#base)"  />
<rect x="20.959" y="79.917" fill="url(#base2)"  />
<rect x="20.959" y="75.861" fill="url(#base2)"  />

<use xlink:href="#scrubber_body" fill="url(#body1)"/>
<use xlink:href="#scrubber_body" opacity="0.5" fill="url(#body2)"/>

<rect x="0.976" y="64.865" fill="url(#base3)"  />
<rect x="0.976" y="62.447" fill="url(#base3)"  />

<rect x="58.059" y="38.087" fill="url(#base4)"  />
<rect x="-10.427" y="38.087" fill="url(#base4)"  />

<path fill="url(#base5)" d="M58.059,54.336V41.337H45.014c-3.599-0.013-6.522,2.9-6.522,6.5c0,3.599,2.924,6.512,6.522,6.499H58.059z"/>
<path fill="url(#base5)" d="M-7.165,54.336V41.337H5.88c3.598-0.014,6.521,2.9,6.521,6.5c0,3.598-2.924,6.512-6.521,6.499H-7.165z"/>

<path class="color" d="M58.059,38.087v3.25H45.015c0-8.666,0-17.331,0-25.997c0-1.944-2.232-3.113-3.578-3.746
	c-1.666-0.784-3.387-1.275-5.172-1.67c-2.515-0.554-5.022-0.853-7.557-0.989V5.592h-6.522v3.343
	c-2.533,0.136-5.04,0.435-7.553,0.988c-1.786,0.394-3.508,0.886-5.175,1.67C8.113,12.227,5.88,13.396,5.88,15.34
	c0,8.666,0,17.331,0,25.997H-7.165v-3.25h-3.262v19.447h3.262v-3.198H5.88c0,2.166,0,4.333,0,6.499H4.654v1.612H0.976v2.418v2.469
	h3.678v1.612h1.813c0.752,1.01,2.071,1.701,2.991,2.135c1.666,0.784,3.388,1.275,5.173,1.669c2.514,0.555,5.021,0.854,7.556,0.989
	v2.122h-1.227v2.027h1.227v2.028h-1.227v2.027h1.227v2.027h6.522v-2.027h1.227v-2.027h-1.227v-2.028h1.227v-2.027h-1.227v-2.122
	c2.533-0.136,5.04-0.435,7.554-0.988c1.785-0.394,3.507-0.885,5.173-1.67c0.92-0.434,2.24-1.124,2.993-2.135h1.813v-1.612h3.679
	v-2.469v-2.418H46.24v-1.612h-1.226c0-2.166,0-4.333,0-6.499h13.044v3.198h3.262V38.087H58.059z"/>

<g opacity="0.5" class="stroke">
	<path  d="M58.059,41.337
		c-4.349,0-8.696,0-13.044,0c-3.6-0.013-6.523,2.9-6.523,6.5c0,3.599,2.924,6.512,6.522,6.499c4.349,0,8.696,0,13.045,0"/>
	<path  d="M-7.165,41.337
		c4.348,0,8.696,0,13.045,0c3.598-0.014,6.521,2.9,6.521,6.5c0,3.598-2.924,6.512-6.521,6.499c-4.349,0-8.697,0-13.045,0"/>
	<line  x1="5.88" y1="54.336" x2="5.88" y2="62.447"/>
	<path  d="M45.015,41.337
		c0-8.665,0-17.331,0-25.997c0-1.944-2.232-3.113-3.578-3.746c-1.666-0.784-3.387-1.275-5.172-1.67
		c-3.588-0.79-7.158-1.084-10.818-1.084c-3.658,0-7.228,0.293-10.813,1.083c-1.786,0.394-3.508,0.886-5.175,1.67
		C8.113,12.227,5.88,13.396,5.88,15.34c0,8.666,0,17.331,0,25.997"/>
	<line  x1="45.015" y1="62.447" x2="45.015" y2="54.336"/>
	<path  d="M5.88,67.334
		c0,1.944,2.231,3.113,3.577,3.746c1.666,0.785,3.388,1.276,5.173,1.67c3.587,0.791,7.157,1.084,10.816,1.084
		s7.229-0.293,10.814-1.083c1.786-0.394,3.508-0.885,5.174-1.67c1.346-0.634,3.58-1.802,3.58-3.747"/>
	<rect x="0.975" y="62.447"   />
	<line  x1="0.975" y1="64.865" x2="49.919" y2="64.865"/>
	<polyline  points="22.186,81.944 22.186,83.972 28.708,83.972 28.708,81.944 	"/>
	<line  x1="22.186" y1="77.889" x2="22.186" y2="79.917"/>
	<line  x1="28.708" y1="79.917" x2="28.708" y2="77.889"/>
	<line  x1="28.708" y1="75.861" x2="28.708" y2="73.742"/>
	<line  x1="22.186" y1="73.742" x2="22.186" y2="75.861"/>
	<rect x="20.959" y="79.917"   />
	<rect x="20.959" y="75.861"   />
	<polyline  points="28.708,8.932 28.708,5.592 22.186,5.592 22.186,8.932 	"/>
	<rect x="58.058" y="38.087"   />
	<rect x="-10.427" y="38.087"   />
</g>

<g id="nuts" fill="#606060">
	<rect x="13.993" y="67.334"   />
	<rect x="41.962" y="67.334"   />
	<rect x="32.648" y="67.334"   />
	<rect x="23.308" y="67.334"   />
	<rect x="4.654" y="67.334"   />
	<rect x="13.993" y="60.835"   />
	<rect x="41.962" y="60.835"   />
	<rect x="32.648" y="60.835"   />
	<rect x="23.308" y="60.835"   />
	<rect x="4.654" y="60.835"   />
</g>

</svg>
