<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='49.122px'

    height='157.744px'

    viewBox='0 0 49.122 157.744'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M47.924,16.617c0,31.034,0,62.065,0,93.101c0,6.731,0,13.464,0,20.193c0,9.106,1.909,17.36-8.268,23.098   c-7.186,4.054-16.793,4.634-24.533,2.349c-6.188-1.828-13.891-6.479-13.891-14.23c0-31.033,0-62.07,0-93.108   c0-6.585,0-13.171,0-19.759c0-9.225-2.112-17.669,8.269-23.524C16.69,0.682,26.297,0.099,34.036,2.39   C40.224,4.215,47.924,8.866,47.924,16.617z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M47.924,16.617c0,31.034,0,62.065,0,93.101c0,6.731,0,13.464,0,20.193c0,9.106,1.909,17.36-8.268,23.098   c-7.186,4.054-16.793,4.634-24.533,2.349c-6.188-1.828-13.891-6.479-13.891-14.23c0-31.033,0-62.07,0-93.108   c0-6.585,0-13.171,0-19.759c0-9.225-2.112-17.669,8.269-23.524C16.69,0.682,26.297,0.099,34.036,2.39   C40.224,4.215,47.924,8.866,47.924,16.617z' />

  <line x1='1.232'

      y1='117.782'

      x2='47.924'

      y2='117.782' />

  <line x1='1.232'

      y1='39.962'

      x2='47.924'

      y2='39.962' />

  <line x1='47.924'

      y1='39.962'

      x2='1.232'

      y2='117.782' />

  <line x1='1.232'

      y1='39.962'

      x2='47.924'

      y2='117.782' />

 </g>

</svg>