<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='49.122px'

    height='157.743px'

    viewBox='0 0 49.122 157.743'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M47.925,16.617c0,31.032,0,62.066,0,93.099c0,6.731,0,13.464,0,20.192c0,9.109,1.906,17.361-8.269,23.099   c-7.188,4.054-16.793,4.637-24.534,2.349c-6.188-1.828-13.888-6.478-13.888-14.23c0-31.034,0-62.068,0-93.105   c0-6.588,0-13.172,0-19.759c0-9.225-2.119-17.67,8.268-23.524C16.688,0.683,26.295,0.1,34.035,2.388   C40.226,4.216,47.925,8.864,47.925,16.617z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='47.925'

      y1='86.653'

      x2='1.234'

      y2='125.563' />

  <line x1='1.234'

      y1='71.089'

      x2='47.925'

      y2='71.089' />

  <line x1='1.234'

      y1='125.563'

      x2='47.925'

      y2='125.563' />

  <line x1='1.234'

      y1='86.653'

      x2='47.925'

      y2='86.653' />

  <line x1='1.234'

      y1='32.181'

      x2='47.925'

      y2='71.089' />

  <line x1='1.234'

      y1='86.653'

      x2='47.925'

      y2='125.563' />

  <path d='M47.925,16.617c0,31.032,0,62.066,0,93.099c0,6.731,0,13.464,0,20.192c0,9.109,1.906,17.361-8.269,23.099   c-7.188,4.054-16.793,4.637-24.534,2.349c-6.188-1.828-13.888-6.478-13.888-14.23c0-31.034,0-62.068,0-93.105   c0-6.588,0-13.172,0-19.759c0-9.225-2.119-17.67,8.268-23.524C16.688,0.683,26.295,0.1,34.035,2.388   C40.226,4.216,47.925,8.864,47.925,16.617z' />

  <line x1='47.925'

      y1='32.181'

      x2='1.234'

      y2='71.089' />

  <line x1='1.234'

      y1='32.181'

      x2='47.925'

      y2='32.181' />

 </g>

</svg>