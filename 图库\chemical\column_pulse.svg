<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='48.803px'

    height='157.751px'

    viewBox='0 0 48.803 157.751'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M47.747,47.75c-2.596,2.593-5.188,5.185-7.78,7.78c0,15.565,0,31.128,0,46.69   c2.592,2.596,5.185,5.188,7.78,7.783c0,10.335,0,20.667,0,31.005c0,11.625-14.321,15.687-23.347,15.687   c-9.023,0-23.345-4.066-23.345-15.689c0-10.335,0-20.667,0-31.002c2.595-2.595,5.188-5.188,7.783-7.783c0-15.563,0-31.125,0-46.69   c-2.595-2.596-5.188-5.188-7.783-7.78c0-10.333,0-20.659,0-30.991C1.056,5.123,15.361,1.056,24.4,1.056   c9.03,0,23.347,4.067,23.347,15.697C47.747,27.085,47.747,37.417,47.747,47.75z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M47.747,47.75c-2.596,2.593-5.188,5.185-7.78,7.78c0,15.565,0,31.128,0,46.69   c2.592,2.596,5.185,5.188,7.78,7.783c0,10.335,0,20.667,0,31.005c0,11.625-14.321,15.687-23.347,15.687   c-9.023,0-23.345-4.066-23.345-15.689c0-10.335,0-20.667,0-31.002c2.595-2.595,5.188-5.188,7.783-7.783c0-15.563,0-31.125,0-46.69   c-2.595-2.596-5.188-5.188-7.783-7.78c0-10.333,0-20.659,0-30.991C1.056,5.123,15.361,1.056,24.4,1.056   c9.03,0,23.347,4.067,23.347,15.697C47.747,27.085,47.747,37.417,47.747,47.75z' />

  <line x1='1.056'

      y1='133.35'

      x2='47.747'

      y2='133.35' />

  <line x1='1.056'

      y1='24.404'

      x2='47.747'

      y2='24.404' />

 </g>

</svg>