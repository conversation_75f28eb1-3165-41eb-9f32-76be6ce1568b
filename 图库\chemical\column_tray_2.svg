<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='49.123px'

    height='157.744px'

    viewBox='0 0 49.123 157.744'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M47.926,16.617c0,31.034,0,62.067,0,93.101c0,6.732,0,13.461,0,20.193c0,9.107,1.905,17.359-8.269,23.099   c-7.188,4.054-16.794,4.633-24.534,2.347c-6.187-1.83-13.89-6.478-13.89-14.231c0-31.034,0-62.069,0-93.106   c0-6.586,0-13.171,0-19.758c0-9.226-2.115-17.67,8.269-23.525c7.188-4.054,16.796-4.635,24.533-2.349   C40.226,4.215,47.926,8.864,47.926,16.617z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='1.233'

      y1='117.779'

      x2='40.142'

      y2='117.779' />

  <line x1='1.233'

      y1='55.525'

      x2='40.142'

      y2='55.525' />

  <line x1='1.233'

      y1='24.397'

      x2='40.142'

      y2='24.397' />

  <line x1='9.017'

      y1='102.218'

      x2='47.926'

      y2='102.218' />

  <line x1='1.233'

      y1='86.654'

      x2='40.142'

      y2='86.654' />

  <line x1='9.017'

      y1='39.964'

      x2='47.926'

      y2='39.964' />

  <line x1='9.017'

      y1='71.089'

      x2='47.926'

      y2='71.089' />

  <path d='M47.926,16.617c0,31.034,0,62.067,0,93.101c0,6.732,0,13.461,0,20.193c0,9.107,1.905,17.359-8.269,23.099   c-7.188,4.054-16.794,4.633-24.534,2.347c-6.187-1.83-13.89-6.478-13.89-14.231c0-31.034,0-62.069,0-93.106   c0-6.586,0-13.171,0-19.758c0-9.226-2.115-17.67,8.269-23.525c7.188-4.054,16.796-4.635,24.533-2.349   C40.226,4.215,47.926,8.864,47.926,16.617z' />

  <line x1='9.017'

      y1='133.345'

      x2='47.926'

      y2='133.345' />

 </g>

</svg>