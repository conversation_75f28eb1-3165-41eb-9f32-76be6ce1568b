<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_01" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 90 202" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param  type="C" name="vapourColor" classes="vapour" description="Vapour Color" cssAttributes="fill"/>
	<agg:param  type="C" name="activeColor" classes="active" description="Active Volume Color" cssAttributes="fill"/>
	<agg:param  type="C" name="coolingColor" classes="cooling" description="Cooling Liquid Color" cssAttributes="fill"/>
</agg:params>
	 
<style type="text/css" >
	<![CDATA[
		.stroke,.thin,.thick,.dash{
			stroke-linecap:butt;
			stroke-linejoin:round;
			stroke: black;
			fill:none;
		}

		.stroke,.dash{stroke-width:0.75;}

		.thin {stroke-width:0.35;}

		.thick {stroke-width:1;}
		
		.dash {
			stroke-dasharray:4,3;
		}

		.cooling {
			fill:#33AAFF;
		}
		
		.vapour {
			fill:#FFCE00;
		}
		
		.active {
			fill:#7FE57F;
		}
	]]>
</style>

<defs>
	<linearGradient id="baseGradient" gradientUnits="userSpaceOnUse" x1="39.7681" y1="56.4419" x2="89" y2="56.4419">
		<stop  offset="0" style="stop-color:#000000"/>
		<stop  offset="0.45" style="stop-color:#FFFFFF"/>
		<stop  offset="0.55" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#000000"/>
	</linearGradient>
</defs>

<path class="vapour" d="M89,25.623c0,14.903,0,29.806,0,44.709H39.768c0-14.903,0-29.807,0-44.709
	c7.106-6.109,14.213-12.227,21.319-18.344c0-2.042,0-4.084,0-6.118h6.603c0,2.034,0,4.076,0,6.118
	C74.796,13.396,81.894,19.514,89,25.623z"/>
<path class="active" d="M67.689,111.722C74.796,103.666,81.894,95.617,89,87.561c0-5.74,0-11.479,0-17.229
	H39.768c-4.168,0-8.336,0-12.504,0c-2.443,0-4.894,0-7.337,0c-5.62,0-10.172,4.573-10.172,10.219
	c-1.683,3.32-3.366,6.632-5.04,9.952h3.332v87.773H4.715c1.674,3.32,3.356,6.632,5.04,9.953c0,5.637,4.552,10.218,10.172,10.218
	c2.443,0,4.886,0,7.337,0c4.561,0,9.122,0,13.691,0c2.349-0.446,4.697-0.883,7.047-1.33c1.46,0,2.913,0,4.364,0
	c0.692,0,1.375-0.052,2.042-0.146c3.767-0.558,7.047-2.617,9.208-5.56c1.726-2.342,2.75-5.234,2.75-8.357c0-1.458,0-2.926,0-4.384
	c0.444-2.359,0.879-4.719,1.324-7.079c0-2.385,0-3.337,0-5.534h3.707v-4.436h-3.707c0-2.806,0-4.856,0-7.911h3.707v-4.436h-3.707
	c0-11.686,0-22.248,0-33.934C67.689,114.132,67.689,112.932,67.689,111.722z M11.386,90.503h3.331v87.773h-3.331V90.503z
	 M61.087,171.592c0.436,2.36,0.88,4.719,1.315,7.079c0,1.458,0,2.926,0,4.384c0,5.568-4.492,10.082-10.036,10.082
	c-1.451,0-2.904,0-4.364,0c-2.35-0.438-4.698-0.884-7.047-1.322c-4.57,0-9.13,0-13.691,0c-2.451,0-4.894,0-7.337,0
	c-1.973,0-3.57-1.604-3.57-3.586c1.674-3.32,3.356-6.632,5.031-9.953h-3.331V90.503h3.331c-1.674-3.32-3.356-6.632-5.031-9.952
	c0-1.982,1.597-3.587,3.57-3.587c2.443,0,4.895,0,7.337,0c4.168,0,8.336,0,12.504,0c0,3.535,0,7.062,0,10.596
	c7.106,8.057,14.213,16.105,21.319,24.161c0,1.21,0,2.411,0,3.621C61.087,134.09,61.087,152.837,61.087,171.592z"/>
<path class="cooling" d="M21.388,90.503c0,29.258,0,58.515,0,87.773h-3.331V90.503H21.388z M11.386,178.276
	h3.331V90.503h-3.331V178.276z M4.715,98.483H1v4.436h3.715c0,21.639,0,41.535,0,62.942H1v4.436h3.715c0,3.054,0,4.891,0,7.979
	h3.332V90.503H4.715C4.715,93.549,4.715,95.437,4.715,98.483z"/>
	
<g opacity="0.25">
	<path fill="url(#baseGradient)" d="M39.768,87.56l21.319,24.161v3.619h6.603c0-1.208,0-2.408,0-3.617C74.796,103.666,81.895,95.617,89,87.561
		c0-5.74,0-47.035,0-61.938c-7.105-6.109-14.204-12.227-21.311-18.344c0-2.042,0-4.084,0-6.118h-6.603c0,2.034,0,4.076,0,6.118
		c-7.106,6.117-14.213,12.235-21.319,18.344C39.768,40.525,39.768,87.56,39.768,87.56z"/>
</g>
	
<g id="demister" class="thin">
	<line x1="49.914" y1="16.734" x2="55.361" y2="22.207"/>
	<line x1="76.524" y1="16.066" x2="79.934" y2="19.491"/>
	<line x1="73.115" y1="16.066" x2="79.227" y2="22.207"/>
	<line x1="69.705" y1="16.066" x2="75.817" y2="22.207"/>
	<line x1="66.296" y1="16.066" x2="72.408" y2="22.207"/>
	<line x1="62.886" y1="16.066" x2="68.999" y2="22.207"/>
	<line x1="59.477" y1="16.066" x2="65.589" y2="22.207"/>
	<line x1="56.068" y1="16.066" x2="62.18" y2="22.207"/>
	<line x1="52.658" y1="16.066" x2="58.771" y2="22.207"/>
	<line x1="49.249" y1="22.207" x2="55.361" y2="16.066"/>
	<line x1="52.658" y1="22.207" x2="58.771" y2="16.066"/>
	<line x1="56.068" y1="22.207" x2="62.18" y2="16.066"/>
	<line x1="59.477" y1="22.207" x2="65.589" y2="16.066"/>
	<line x1="62.886" y1="22.207" x2="68.999" y2="16.066"/>
	<line x1="66.296" y1="22.207" x2="72.408" y2="16.066"/>
	<line x1="69.705" y1="22.207" x2="75.817" y2="16.066"/>
	<line x1="73.115" y1="22.207" x2="78.697" y2="16.599"/>
	<line x1="76.524" y1="22.207" x2="79.934" y2="18.782"/>
	<line x1="49.249" y1="22.207" x2="79.934" y2="22.207"/>
	<line x1="79.934" y1="17.663" x2="79.934" y2="22.207"/>
	<line x1="49.249" y1="17.306" x2="49.249" y2="22.207"/>
	<line x1="50.688" y1="16.066" x2="78.079" y2="16.066"/>
</g>

<g id="boundary" class="stroke">
	<path d="M61.083,1c0,2.039,0,4.078,0,6.117c-7.104,6.117-14.209,12.234-21.313,18.351c0,20.645,0,41.29,0,61.935
		c7.104,8.054,14.208,16.108,21.313,24.162c0,1.207,0,41.117,0,59.866c0.44,2.36,0.88,4.721,1.32,7.081c0,1.461,0,2.923,0,4.385
		c0,5.568-4.494,10.083-10.037,10.083c-1.455,0-2.91,0-4.366,0c-2.35-0.442-4.7-0.885-7.049-1.327c-7.01,0-14.02,0-21.03,0
		c-1.971,0-3.569-1.605-3.569-3.585c1.679-3.316,3.358-6.631,5.037-9.947c0-29.26,0-58.52,0-87.78
		c-1.679-3.316-3.358-6.631-5.037-9.947c0-1.98,1.599-3.585,3.569-3.585c6.616,0,13.232,0,19.849,0"/>
	<path d="M39.77,70.176c-6.617,0-13.233,0-19.849,0c-5.618,0-10.172,4.575-10.172,10.219
		c-1.679,3.315-3.358,6.631-5.037,9.947c0,2.822,0,5.163,0,7.984H1"/>
	<path d="M1,102.761h3.711c0,21.476,0,41.712,0,62.94H1"/>
	<line x1="39.77" y1="70.176" x2="88.998" y2="70.176"/>
	<line x1="18.053" y1="90.341" x2="18.053" y2="178.121"/>
	<line x1="14.718" y1="90.341" x2="14.718" y2="178.121"/>
	<line x1="11.382" y1="90.341" x2="11.382" y2="178.121"/>
	<line x1="8.047" y1="90.341" x2="8.047" y2="178.121"/>
	<path d="M1,170.137h3.711c0,3.027,0,4.929,0,7.984c1.679,3.316,3.358,6.631,5.037,9.947
		c0,5.644,4.554,10.218,10.172,10.218c7.01,0,14.02,0,21.03,0c2.35-0.442,4.699-0.884,7.049-1.327c1.456,0,2.911,0,4.366,0
		c7.731,0,13.999-6.296,13.999-14.063c0-1.462,0-2.924,0-4.385c0.44-2.36,0.88-4.721,1.32-7.081c0-2.002,0-3.122,0-5.534h3.711"/>
	<path d="M71.396,161.462h-3.711c0-3.116,0-4.483,0-7.911"/>
	<line x1="67.685" y1="153.551" x2="71.396" y2="153.551"/>
	<path d="M71.396,149.116h-3.711c0-18.208,0-36.708,0-37.551c7.104-8.054,14.209-16.108,21.313-24.162
		c0-20.645,0-41.29,0-61.935C81.894,19.351,74.79,13.234,67.685,7.117c0-2.039,0-4.078,0-6.117"/>
</g>

<g id="flange" class="thick">
	<line x1="71.396" y1="159.411" x2="71.396" y2="167.948"/>
	<line x1="71.396" y1="147.064" x2="71.396" y2="155.602"/>
	<line x1="1" y1="172.188" x2="1" y2="163.65"/>
	<line x1="1" y1="96.274" x2="1" y2="104.813"/>
	<line x1="7.047" y1="80.395" x2="19.052" y2="80.395"/>
	<line x1="27.257" y1="79.522" x2="27.257" y2="67.462"/>
	<line x1="2.01" y1="178.121" x2="24.09" y2="178.121"/>
	<line x1="2.01" y1="90.341" x2="24.09" y2="90.341"/>
	<line x1="7.047" y1="188.068" x2="19.052" y2="188.068"/>
	<line x1="27.257" y1="188.94" x2="27.257" y2="201"/>
	<line x1="52.366" y1="190.266" x2="52.366" y2="199.674"/>
	<line x1="40.951" y1="188.94" x2="40.951" y2="201"/>
	<line x1="58.381" y1="171.431" x2="70.386" y2="171.431"/>
	<line x1="59.702" y1="182.897" x2="69.066" y2="182.897"/>
	<line x1="58.381" y1="115.185" x2="70.386" y2="115.185"/>
	<line x1="58.588" y1="1" x2="70.594" y2="1"/>
</g>

<g id="flange" class="thick" visibility="hidden">
	<line stroke-miterlimit="10" x1="71.396" y1="159.411" x2="71.396" y2="167.948"/>
	<line stroke-miterlimit="10" x1="71.396" y1="147.064" x2="71.396" y2="155.602"/>
	<line stroke-miterlimit="10" x1="1" y1="172.188" x2="1" y2="163.65"/>
	<line stroke-miterlimit="10" x1="1" y1="96.274" x2="1" y2="104.813"/>
	<line stroke-miterlimit="10" x1="2.01" y1="178.121" x2="24.09" y2="178.121"/>
	<line stroke-miterlimit="10" x1="2.01" y1="90.341" x2="24.09" y2="90.341"/>
	<line stroke-miterlimit="10" x1="58.381" y1="115.185" x2="70.386" y2="115.185"/>
	<line stroke-miterlimit="10" x1="58.588" y1="1" x2="70.594" y2="1"/>
</g>

</svg>
