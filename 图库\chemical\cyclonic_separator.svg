<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" 
	  viewBox="17.272 3.083 46.077 85.387" enable-background="new 17.272 3.083 46.077 85.387" xml:space="preserve"
	>

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param  type="C" name="liquidColor" classes="liquid" cssAttributes="fill" description="Liquid Color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
	<agg:param  type="F" name="liquidOpacity" classes="liquid" min="0" max="1" cssAttributes="opacity" description="Liquid Saturation"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}
	
	.liquid {
		stroke:#BF56A1;
		stroke-width:0.6;
		opacity:0.5;
		fill:none;
	}
		
	.section {
		fill:red;
	}

      ]]>
</style>

<defs>
	<linearGradient id="base" gradientUnits="userSpaceOnUse" x1="40.5508" y1="13.5552" x2="55.0976" y2="13.5552">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	<linearGradient id="base2" xlink:href="#base" x1="33.5908" y1="0" x2="73.0912" y2="0"/>
	<linearGradient id="base3" xlink:href="#base" x1="40.5908" y1="0" x2="57.8698" y2="0"/>
	<linearGradient id="base4" xlink:href="#base" x1="32.5508" y1="0" x2="59.8868" y2="0"/>
	<linearGradient id="base5" xlink:href="#base" x1="0" y1="20" x2="0" y2="30.7964"/>
</defs>

<path fill="url(#base)" d="M45.381,8.21c2.1,3.64,1.61,7.24,0.81,10.69h-5.64v-0.04V8.21H45.381z"/>
<path fill="url(#base2)" d="M54.931,23.31V19.74l7.13,1.439v32.91l-8.42,30.271l-0.01,0.06l-0.021,2.67h-2.609
	c-5.42-5.93-7.04-12.02-6.03-18.04c1.4-8.399,7.92-16.7,6.07-24.41c-1.05-4.34-4.75-8.5-5.96-12.83c-0.771-2.75-0.54-5.56,0.06-8.5
	h8.91H54.931z"/>

<path class="section" d="M62.94,20.46v33.75l-8.449,30.38v3.38h-2.66c-0.29-0.29-0.561-0.59-0.83-0.88h2.609l0.021-2.67
	l0.01-0.06l8.42-30.271V21.18l-7.13-1.439v3.569h-0.88V8.21h0.88v10.64L62.94,20.46z"/>
<path fill="url(#base3)" d="M46.19,18.9c0.801-3.45,1.29-7.051-0.81-10.69h8.67v15.1h-8.91c0.09-0.43,0.19-0.859,0.29-1.3
	C45.671,20.99,45.94,19.95,46.19,18.9z"/>
	
<g id="liquid" class="liquid">
	<line x1="47.743" y1="3.583" x2="47.743" y2="41.383"/>
	<line x1="47.743" y1="53.409" x2="47.743" y2="60.121"/>
	<line x1="47.743" y1="62.639" x2="47.743" y2="69.287"/>
	<path d="M47.743,72.708
		c0,0.878,0,1.756,0,2.636c0,1.806-0.004,3-0.008,3.817"/>
	<path d="M47.873,83.089
		c0.403,2.029,1.495,1.706,2.155,1.388c0.822-0.396,0.971-0.783,0.837-1.121c-0.135-0.339-0.556-0.63-1.05-0.844
		c-0.495-0.216-1.064-0.354-1.768-0.543c-0.703-0.184-1.535-0.413-2.034-0.55c-0.5-0.138-0.661-0.18-0.796-0.212
		c-0.135-0.033-0.242-0.057-0.317-0.075c-0.076-0.016-0.119-0.029-0.131-0.04c-0.009-0.01,0.015-0.018,0.07-0.029
		c0.058-0.014,0.148-0.029,0.271-0.051c0.122-0.023,0.275-0.054,0.458-0.096c0.179-0.043,0.388-0.095,0.618-0.163
		c0.228-0.068,0.478-0.151,0.739-0.253c0.263-0.101,0.538-0.217,0.817-0.354c0.279-0.137,0.563-0.29,0.841-0.467
		c0.277-0.172,0.549-0.366,0.806-0.577c0.258-0.212,0.499-0.44,0.717-0.687c0.219-0.244,0.413-0.502,0.576-0.774
		c0.164-0.272,0.296-0.556,0.392-0.848c0.094-0.292,0.152-0.59,0.173-0.893c0.019-0.303-0.003-0.608-0.065-0.908
		c-0.062-0.302-0.163-0.602-0.304-0.892c-0.142-0.29-0.319-0.571-0.532-0.841c-0.213-0.268-0.458-0.523-0.731-0.763
		c-0.271-0.236-0.57-0.458-0.886-0.657s-0.647-0.377-0.985-0.531c-0.338-0.156-0.682-0.291-1.021-0.4
		c-0.337-0.108-0.67-0.195-0.983-0.26c-0.316-0.063-0.612-0.106-0.882-0.132c-0.269-0.021-0.509-0.025-0.711-0.014
		c-0.203,0.009-0.368,0.037-0.486,0.072c-0.12,0.037-0.195,0.083-0.221,0.132c-0.024,0.05-0.001,0.102,0.073,0.152
		c0.073,0.049,0.198,0.096,0.37,0.132s0.392,0.063,0.652,0.071c0.262,0.012,0.563,0.003,0.899-0.023
		c0.335-0.027,0.702-0.078,1.092-0.155s0.8-0.177,1.217-0.306c0.417-0.13,0.843-0.289,1.261-0.476
		c0.416-0.187,0.826-0.402,1.216-0.646c0.389-0.243,0.757-0.513,1.088-0.807c0.331-0.295,0.629-0.611,0.877-0.948
		c0.248-0.335,0.449-0.692,0.597-1.057c0.147-0.369,0.238-0.748,0.269-1.13c0.03-0.384-0.001-0.771-0.094-1.154
		c-0.092-0.38-0.246-0.76-0.459-1.126c-0.214-0.365-0.484-0.718-0.807-1.053c-0.152-0.157-0.317-0.311-0.49-0.457"/>
	<path d="M46.2,59.519
		c-0.509-0.091-1.01-0.147-1.483-0.17c-0.474-0.021-0.922-0.01-1.324,0.028c-0.401,0.041-0.759,0.109-1.061,0.2
		c-0.3,0.091-0.542,0.204-0.718,0.331s-0.281,0.268-0.315,0.413c-0.032,0.146,0.008,0.296,0.124,0.442
		c0.116,0.143,0.307,0.282,0.568,0.405c0.261,0.123,0.593,0.229,0.985,0.313c0.392,0.081,0.846,0.139,1.346,0.163
		c0.5,0.025,1.048,0.019,1.624-0.028c0.577-0.049,1.182-0.134,1.796-0.259c0.615-0.129,1.239-0.298,1.85-0.508
		c0.61-0.209,1.209-0.462,1.774-0.754c0.564-0.294,1.096-0.626,1.571-0.99c0.476-0.366,0.901-0.766,1.255-1.19
		c0.354-0.426,0.638-0.879,0.843-1.345c0.202-0.469,0.323-0.952,0.357-1.44c0.035-0.488-0.02-0.982-0.162-1.469
		c-0.144-0.484-0.372-0.963-0.687-1.422c-0.165-0.243-0.355-0.481-0.567-0.712"/>
	<path d="M51.771,49.803
		c-0.592-0.342-1.237-0.644-1.916-0.897c-0.301-0.113-0.606-0.218-0.917-0.313"/>
	<path d="M46.546,48.054
		c-0.322-0.049-0.646-0.087-0.967-0.117c-0.712-0.063-1.41-0.077-2.065-0.045c-0.655,0.029-1.271,0.105-1.821,0.224
		c-0.552,0.115-1.04,0.27-1.448,0.455c-0.406,0.184-0.731,0.397-0.959,0.63c-0.229,0.231-0.364,0.482-0.397,0.736
		c-0.033,0.253,0.036,0.514,0.207,0.763c0.17,0.249,0.442,0.487,0.809,0.705c0.368,0.216,0.828,0.411,1.37,0.569
		c0.542,0.159,1.164,0.282,1.847,0.361c0.683,0.077,1.425,0.109,2.206,0.089c0.779-0.018,1.595-0.089,2.416-0.218
		c0.824-0.128,1.657-0.312,2.469-0.552s1.603-0.534,2.345-0.881c0.744-0.348,1.438-0.746,2.06-1.188
		c0.622-0.44,1.17-0.929,1.625-1.446c0.455-0.519,0.816-1.07,1.069-1.641c0.252-0.572,0.399-1.161,0.428-1.754
		c0.029-0.595-0.055-1.191-0.253-1.779c-0.2-0.586-0.513-1.161-0.933-1.71"/>
	<path d="M54.292,39.195
		c-0.428-0.285-0.889-0.554-1.376-0.801c-0.768-0.391-1.602-0.729-2.474-1.01c-0.361-0.116-0.732-0.222-1.106-0.319"/>
	<path d="M46.207,36.518
		c-0.406-0.038-0.809-0.067-1.207-0.08c-0.9-0.035-1.777-0.009-2.599,0.076c-0.819,0.083-1.587,0.223-2.269,0.413
		c-0.684,0.188-1.283,0.424-1.777,0.695c-0.498,0.271-0.889,0.577-1.162,0.904c-0.269,0.326-0.422,0.675-0.446,1.024
		c-0.025,0.352,0.074,0.704,0.302,1.045c0.226,0.34,0.576,0.668,1.041,0.964c0.467,0.298,1.046,0.562,1.721,0.783
		c0.677,0.221,1.446,0.397,2.288,0.519c0.841,0.118,1.753,0.184,2.705,0.184c0.95-0.001,1.942-0.067,2.938-0.199
		c0.998-0.132,2.001-0.332,2.976-0.601c0.975-0.268,1.919-0.601,2.804-0.996s1.706-0.851,2.439-1.356
		c0.729-0.508,1.371-1.063,1.897-1.658c0.527-0.593,0.941-1.224,1.225-1.876s0.439-1.326,0.457-2
		c0.02-0.675-0.098-1.353-0.349-2.015c-0.249-0.662-0.633-1.308-1.138-1.919c-0.505-0.614-1.13-1.192-1.855-1.722
		c-0.725-0.532-1.552-1.017-2.45-1.438c-0.899-0.421-1.872-0.781-2.884-1.074c-0.606-0.174-1.226-0.325-1.851-0.45"/>
	<path d="M46.36,25.365
		c-0.593-0.05-1.183-0.077-1.76-0.084c-0.146,0-0.288,0-0.433,0c0.145,0,0.289,0,0.433,0c-9.079,0-17.748,0-26.826,0"/>
	<line x1="47.743" y1="44.396" x2="47.743" y2="50.699"/>
</g>

<path fill="url(#base4)" d="M44.971,69.05c-1.01,6.021,0.61,12.11,6.03,18.04c0.27,0.29,0.54,0.59,0.83,0.88h-10.84v-3.38
	l-8.44-30.38V30.58h0.01c2.25-3.021,2.25-7.1,0-10.12v-0.01l7.99-1.59v0.04h5.64c-0.25,1.05-0.52,2.09-0.76,3.109
	c-0.1,0.44-0.2,0.87-0.29,1.3c-0.6,2.94-0.83,5.75-0.06,8.5c1.21,4.33,4.91,8.49,5.96,12.83C52.891,52.35,46.371,60.65,44.971,69.05
	z"/>
<path fill="url(#base5)" d="M32.561,20.46c2.25,3.021,2.25,7.1,0,10.12h-0.01h-6.75V20.46h6.71l0.05-0.01V20.46z"/>

<polygon class="color" points="54.931,18.85 54.931,8.21 40.551,8.21 40.551,18.86 32.561,20.45 32.511,20.46 
	25.801,20.46 25.801,30.58 32.551,30.58 32.551,54.21 40.991,84.59 40.991,87.97 54.491,87.97 54.491,84.59 62.94,54.21 
	62.94,20.46 "/>

<g class="stroke">
	<line x1="54.051" y1="8.21" x2="54.051" y2="23.31"/>
	<polyline points="45.141,23.31 54.051,23.31 54.931,23.31 54.931,19.74 62.061,21.18 62.061,54.09 53.641,84.36 53.631,84.42 53.61,87.09 
		51.001,87.09 "/>
	<line x1="34.281" y1="54.22" x2="44.701" y2="54.22"/>
	<path d="M45.381,8.21c2.1,3.64,1.61,7.24,0.81,10.69c-0.25,1.05-0.52,2.09-0.76,3.109c-0.1,0.44-0.2,0.87-0.29,1.3c-0.6,2.94-0.83,5.75-0.06,8.5
		c1.21,4.33,4.91,8.49,5.96,12.83c1.85,7.71-4.67,16.011-6.07,24.41c-1.01,6.021,0.61,12.11,6.03,18.04
		c0.27,0.29,0.54,0.59,0.83,0.88"/>
	<line x1="40.551" y1="18.9" x2="46.19" y2="18.9"/>
	<line x1="40.991" y1="84.6" x2="48.952" y2="84.6"/>
	<polygon points="51.831,87.97 54.491,87.97 54.491,84.59 62.94,54.21 62.94,20.46 54.931,18.85 54.931,8.21 54.051,8.21 45.381,8.21 40.551,8.21 
		40.551,18.86 32.561,20.45 32.511,20.46 25.801,20.46 25.801,30.58 32.551,30.58 32.551,54.21 40.991,84.59 40.991,87.97 	"/>
	<path d="M32.561,20.46c2.25,3.021,2.25,7.1,0,10.12"/>
</g>

</svg>
