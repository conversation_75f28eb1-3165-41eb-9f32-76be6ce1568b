<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   xmlns:xlink="http://www.w3.org/1999/xlink" x="-1px" y="-1px"   viewBox="-1 -1 42 152" enable-background="new 0 0 40 150" xml:space="preserve" xmlns:agg="http://www.example.com">

<agg:params>
    <agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
    <agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.5px;
		opacity:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>
	 
<defs>
	<linearGradient id="base" gradientUnits="userSpaceOnUse">
		<stop  offset="0.0051" style="stop-color:#767677"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BBBBBB"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F7F8F8"/>
		<stop  offset="0.42" style="stop-color:#FDFEFF"/>
		<stop  offset="0.4749" style="stop-color:#F7F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BBBBBB"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFEFF"/>
	</linearGradient>
	<linearGradient id="SVGID_1_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="0" y1="119.3203" x2="40" y2="119.3203"/>
	<linearGradient id="SVGID_2_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="-11" y1="85.23" x2="52.6744" y2="85.23"/>
	<linearGradient id="SVGID_3_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="6.6699" y1="44.3198" x2="33.3301" y2="44.3198"/>
	<linearGradient id="SVGID_4_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="0" y1="3.4102" x2="41" y2="3.4102"/>
</defs>
	
<rect y="88.64" fill="url(#SVGID_1_)"  />	
<polygon fill="url(#SVGID_2_)" points="33.33,81.82 40,88.64 0,88.64 6.67,81.82 "/>
<rect x="6.67" y="6.82" fill="url(#SVGID_3_)"  />
<polygon fill="url(#SVGID_4_)" points="26.67,0 33.33,6.82 6.67,6.82 13.33,0 "/>

<polygon class="color" points="33.33,81.82 33.33,6.82 26.67,0 13.33,0 6.67,6.82 6.67,81.82 0,88.64 0,150 40,150 40,88.64 "/>

<g class="stroke">
	<line x1="0" y1="88.64" x2="40" y2="88.64"/>
	<line x1="6.67" y1="6.82" x2="33.33" y2="6.82"/>
	<line x1="6.67" y1="81.82" x2="33.33" y2="81.82"/>
	<polygon points="40,88.64 40,150 0,150 0,88.64 6.67,81.82 6.67,6.82 13.33,0 26.67,0 33.33,6.82 33.33,81.82"/>
</g>

</svg>
