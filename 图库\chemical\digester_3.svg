<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   xmlns:xlink="http://www.w3.org/1999/xlink" x="-1px" y="-1px"	   viewBox="-1 -1 42 152" xml:space="preserve" xmlns:agg="http://www.example.com">

<agg:params>
    <agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
    <agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.5px;
		opacity:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>
	 
<defs>
	<linearGradient id="base" gradientUnits="userSpaceOnUse">
		<stop  offset="0.0051" style="stop-color:#767677"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BBBBBB"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F7F8F8"/>
		<stop  offset="0.42" style="stop-color:#FDFEFF"/>
		<stop  offset="0.4749" style="stop-color:#F7F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BBBBBB"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFEFF"/>
	</linearGradient>
	<linearGradient id="SVGID_1_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="0" y1="129.5449" x2="40" y2="129.5449"/>
	<linearGradient id="SVGID_2_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="-24" y1="105.6797" x2="62.0103" y2="105.6797"/>
	<linearGradient id="SVGID_3_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="6.6699" y1="75" x2="33.3301" y2="75"/>
	<linearGradient id="SVGID_4_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="-1" y1="44.3203" x2="40.7003" y2="44.3203"/>
	<linearGradient id="SVGID_5_" xlink:href="#base" gradientUnits="userSpaceOnUse" x1="13.3301" y1="20.4551" x2="26.6699" y2="20.4551"/>
</defs>

<rect y="109.09" fill="url(#SVGID_1_)"  />
<polygon fill="url(#SVGID_2_)" points="33.33,102.27 40,109.09 0,109.09 6.67,102.27 	"/>
<rect x="13.33" fill="url(#SVGID_5_)"  />
<rect x="6.67" y="47.73" fill="url(#SVGID_3_)"  />
<polygon fill="url(#SVGID_4_)" points="26.67,40.91 33.33,47.73 6.67,47.73 13.33,40.91 	"/>

<polygon class="color" points="33.33,102.27 33.33,47.73 26.67,40.91 26.67,0 13.33,0 13.33,40.91 6.67,47.73 6.67,102.27 0,109.09 0,150 40,150 40,109.09 	"/>

<g class="stroke">
	<polygon points="0,109.09 
		6.67,102.27 6.67,47.73 13.33,40.91 13.33,0 26.67,0 26.67,40.91 33.33,47.73 33.33,102.27 40,109.09 40,150 0,150 		"/>
	<line x1="13.33" y1="40.91" x2="26.67" y2="40.91"/>
	<line x1="6.67" y1="47.73" x2="33.33" y2="47.73"/>
	<line x1="0" y1="109.09" x2="40" y2="109.09"/>
	<line x1="6.67" y1="102.27" x2="33.33" y2="102.27"/>
</g>

</svg>
