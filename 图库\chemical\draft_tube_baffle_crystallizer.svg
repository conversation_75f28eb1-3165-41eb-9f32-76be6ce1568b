<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_01" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 84 202" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param  type="C" name="vapourColor" classes="vapour" description="Vapour Color" cssAttributes="fill"/>
	<agg:param  type="C" name="activeColor" classes="active" description="Active Volume Color" cssAttributes="fill"/>
	<agg:param  type="C" name="settlingColor" classes="settling" description="Settling Color" cssAttributes="fill"/>
</agg:params>

<style type="text/css" >
	<![CDATA[
		.stroke,.thin,.thick,.dash{
			stroke-linecap:butt;
			stroke-linejoin:round;
			stroke: black;
			fill:none;
		}

		.stroke,.dash{stroke-width:0.75;}

		.thin {stroke-width:0.35;}

		.thick {stroke-width:1;}
		
		.dash {
			stroke-dasharray:4,3;
		}
		
		.settling {
			fill:#EC6683;
		}
		
		.vapour {
			fill:#FFCE00;
		}
		
		.active {
			fill:#7FE57F;
		}
	]]>
</style>

<defs>
	<linearGradient id="baseGradient" gradientUnits="userSpaceOnUse" x1="10.5" y1="101" x2="83.0684" y2="101">
		<stop  offset="0" style="stop-color:#000000"/>
		<stop  offset="0.45" style="stop-color:#FFFFFF"/>
		<stop  offset="0.55" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#000000"/>
	</linearGradient>
</defs>
	 
<path class="active" d="M51.734,163.478c0,0.251,0.087,0.393,0.294,0.513c7.49,4.508,14.991,8.993,22.492,13.5
	l8.436-14.155H69.736c0-23.248,0-46.494,0-69.741c0-6.112,0-12.224,0-18.335h-0.239h-45.56v18.335v69.741H10.719l14.067,23.628
	h-5.533v4.366c2.707,0,5.424,0,8.131,0c0.805,1.342,1.609,2.696,2.414,4.038c10.131,7.509,23.949,7.509,34.08,0
	c1.87-3.143,3.75-6.286,5.62-9.43c-7.5-4.497-15.001-8.993-22.502-13.5c-3.142-1.888-5.055-5.282-5.055-8.96
	c0-0.044,0-0.098,0-0.142c0-2.369,0-4.737,0-7.105c3.261,0,6.533,0,9.794,0c0,2.368,0,4.736,0,7.105
	C51.734,163.379,51.734,163.434,51.734,163.478z"/>
<path class="settling" d="M79.999,180.776l-5.033,8.447c-1.827-1.091-3.642-2.193-5.468-3.285
	c-7.5-4.497-15.001-8.993-22.502-13.5c-3.142-1.888-5.055-5.282-5.055-8.96c0-0.044,0-0.098,0-0.142c0-2.369,0-4.737,0-7.105
	c3.261,0,6.533,0,9.794,0c0,2.368,0,4.736,0,7.105c0,0.043,0,0.098,0,0.142c0,0.251,0.087,0.393,0.294,0.513
	c7.49,4.508,14.991,8.993,22.492,13.5C76.346,178.583,78.172,179.685,79.999,180.776z M69.736,93.595c0,23.247,0,46.494,0,69.741
	h13.219v-58.194L69.736,93.595z M10.719,105.142v2.794H5.305v4.365h5.414v17.31H1v12.333h9.719v21.392h13.219V93.595
	L10.719,105.142z"/>
<path class="vapour" d="M51.734,7.396c0-2.128,0-4.256,0-6.396H41.94v6.396L23.938,32.858v42.401h45.56h0.239
	c0-14.134,0-28.268,0-42.401C63.736,24.367,57.735,15.887,51.734,7.396z"/>
	
<path fill="url(#baseGradient)" opacity="0.25" d="M51.734,163.479c0,0.25,0.086,0.393,0.293,0.513c7.49,4.508,14.992,8.993,22.492,13.5l8.437-14.155
	h-0.001v-58.193L69.736,93.595c0-5.113,0-47.589,0-60.737c-6-8.491-12.002-16.971-18.002-25.462c0-2.128,0-4.256,0-6.396H41.94
	v6.396L23.938,32.858V75.26l-0.001,18.335v0.001l-13.218,11.546v2.793H5.305v4.365h5.414v17.311H1v12.332h9.719v21.393
	l14.067,23.628h-5.533v4.366c2.707,0,5.424,0,8.131,0c0.805,1.342,1.609,2.695,2.414,4.038c10.131,7.509,23.949,7.509,34.08,0
	c1.87-3.144,3.75-6.286,5.62-9.431c-7.5-4.496-15.001-8.992-22.502-13.5c-3.143-1.888-5.055-5.281-5.055-8.959
	c0-0.045,0-0.099,0-0.143c0-2.369,0-4.736,0-7.105c3.261,0,6.534,0,9.793,0c0,2.369,0,4.736,0,7.105
	C51.734,163.379,51.734,163.434,51.734,163.479z"/>
	
<g class="dash">
	<polyline points="60.561,180.551 60.561,190.339 33.197,190.339 33.197,87.376 60.561,87.376 60.561,169.086"/>
	<path d="M55.133,180.679
		c1.833-0.601,3.717,0.771,3.717,2.707s-1.884,3.308-3.717,2.707c-5.503-1.805-11.005-3.609-16.507-5.414
		c-1.833-0.601-3.717,0.771-3.717,2.707s1.884,3.308,3.717,2.707C44.128,184.288,49.63,182.483,55.133,180.679z"/>
</g>

<g id="demister" class="thin">
	<polyline points="32.18,21.254 32.18,25.237 61.981,25.237 61.981,21.824"/>
	<line x1="32.998" y1="20.098" x2="38.116" y2="25.237"/>
	<line x1="55.359" y1="25.237" x2="60.643" y2="19.932"/>
	<line x1="58.67" y1="19.278" x2="61.981" y2="22.602"/>
	<line x1="55.359" y1="19.278" x2="61.295" y2="25.237"/>
	<line x1="52.047" y1="19.278" x2="57.983" y2="25.237"/>
	<line x1="48.736" y1="19.278" x2="54.672" y2="25.237"/>
	<line x1="45.425" y1="19.278" x2="51.361" y2="25.237"/>
	<line x1="42.114" y1="19.278" x2="48.05" y2="25.237"/>
	<line x1="38.803" y1="19.278" x2="44.739" y2="25.237"/>
	<line x1="35.491" y1="19.278" x2="41.428" y2="25.237"/>
	<line x1="32.18" y1="25.237" x2="38.116" y2="19.278"/>
	<line x1="35.491" y1="25.237" x2="41.428" y2="19.278"/>
	<line x1="38.803" y1="25.237" x2="44.739" y2="19.278"/>
	<line x1="42.114" y1="25.237" x2="48.05" y2="19.278"/>
	<line x1="45.425" y1="25.237" x2="51.361" y2="19.278"/>
	<line x1="48.736" y1="25.237" x2="54.672" y2="19.278"/>
	<line x1="52.047" y1="25.237" x2="57.983" y2="19.278"/>
	<line x1="58.67" y1="25.237" x2="61.981" y2="21.913"/>
	<line x1="33.578" y1="19.278" x2="60.18" y2="19.278"/>
</g>
	
<g class="stroke">
	<line x1="46.879" y1="183.386" x2="46.879" y2="200.997"/>
	<path d="M51.78,1
		c0,2.133,0,4.266,0,6.399c6,8.484,12,16.969,18,25.453c0,43.493,0,86.985,0,130.479"/>
	<polyline points="41.978,1 41.978,7.399 23.979,32.852 23.979,163.331"/>
	<polyline points="19.297,186.961 24.829,186.961 10.758,163.331 10.758,141.941 1.047,141.941"/>
	<polyline points="5.344,112.303 10.758,112.303 10.758,129.611 1.047,129.611"/>
	<path d="M69.537,185.939
		c-1.871,3.143-3.742,6.286-5.614,9.429c-10.133,7.504-23.955,7.504-34.088,0c-0.802-1.347-1.604-2.694-2.406-4.042
		c-2.71,0-5.421,0-8.131,0"/>
	<polyline points="5.344,107.938 10.758,107.938 10.758,105.14 23.979,93.589"/>
	<polyline points="69.78,93.589 83,105.14 83,163.331 74.567,177.493"/>
	<line x1="51.78" y1="163.331" x2="83" y2="163.331"/>
	<line x1="10.758" y1="163.331" x2="41.978" y2="163.331"/>
	<path d="M75.008,189.223
		c-9.323-5.596-18.647-11.192-27.97-16.788c-3.139-1.883-5.06-5.284-5.06-8.955c0-2.417,0-4.834,0-7.251c3.267,0,6.535,0,9.802,0
		c0,2.417,0,4.834,0,7.251c0,0.243,0.08,0.384,0.288,0.509c9.323,5.595,18.647,11.191,27.971,16.788"/>
	<line x1="41.978" y1="7.399" x2="51.78" y2="7.399"/>
	<line x1="23.979" y1="32.852" x2="69.78" y2="32.852"/>
	<line x1="23.974" y1="75.258" x2="69.78" y2="75.258"/>
</g>

<g id="flange" class="thick">
	<line x1="1.047" y1="127.592" x2="1.047" y2="143.96"/>
	<line x1="19.297" y1="184.942" x2="19.297" y2="193.346"/>
	<line x1="5.344" y1="105.918" x2="5.344" y2="114.322"/>
	<line x1="73.748" y1="191.34" x2="81.298" y2="178.66"/>
	<line x1="54.236" y1="1" x2="39.522" y2="1"/>
</g>

</svg>
