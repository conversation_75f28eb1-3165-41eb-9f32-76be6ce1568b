<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_01" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 91 202" xmlns:agg="http://www.example.com" xml:space="preserve">

<agg:params>
	<agg:param  type="C" name="vapourColor" classes="vapour" description="Vapour Color" cssAttributes="fill"/>
	<agg:param  type="C" name="crystalColor" classes="crystal" description="Crystal Bed Color" cssAttributes="fill"/>
	<agg:param  type="C" name="settlingColor" classes="settling" description="Settling Color" cssAttributes="fill"/>
</agg:params>

<style type="text/css" >
	<![CDATA[
		.stroke,.thin,.thick,.dash{
			stroke-linecap:butt;
			stroke-linejoin:round;
			stroke: black;
			fill:none;
		}

		.stroke,.dash{stroke-width:0.75;}

		.thin {stroke-width:0.35;}

		.thick {stroke-width:1;}
		
		.dash {
			stroke-dasharray:4,3;
		}
		
		.settling {
			fill:#EC6683;
		}
		
		.vapour {
			fill:#FFCE00;
		}
		
		.crystal {
			fill:#7FE57F;
		}
	]]>
</style>

<defs>
	<linearGradient id="baseGradient" gradientUnits="userSpaceOnUse" x1="7" y1="101" x2="89.3772" y2="101">
		<stop  offset="0" style="stop-color:#000000"/>
		<stop  offset="0.45" style="stop-color:#FFFFFF"/>
		<stop  offset="0.55" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#000000"/>
	</linearGradient>
</defs>
	 
<path class="settling" d="M90,103.186c-2.896,0-5.793,0-8.689,0c-3.883-5.008-7.774-10.008-11.657-15.007
	c0-5.524,0-11.057,0-16.582H20.888v4.919h6.33v11.662L7.339,113.772v7.366v3.798H1v9.838c2.113,0,4.226,0,6.339,0h5.511l2.298-6.823
	h29.317v58.632h7.942v-58.632h37.126v-14.18l-4.824-6.218H90V103.186z"/>
<path class="crystal" d="M89.533,127.952v27.826l-14.025,24.739h6.233v4.359c-2.905,0-5.811,0-8.708,0
	c-1.989,3.505-3.97,7.001-5.96,10.506c-11.322,7.49-25.954,7.49-37.276,0c-7.492-13.202-14.975-26.403-22.458-39.604
	c0-7.001,0-14.002,0-21.003h5.511l2.298-6.823h29.317v58.632h7.942v-58.632H89.533z"/>
<path class="vapour" d="M69.654,16.434c0,18.388,0,36.767,0,55.163H20.888v-4.919c2.113,0,4.217,0,6.33,0
	c0-16.751,0-33.501,0-50.244c0-2.438,1.584-4.59,3.9-5.284c4.147-1.254,8.293-2.5,12.449-3.754c0-2.135,0-4.27,0-6.396h9.737
	c0,2.126,0,4.261,0,6.396c4.156,1.254,8.302,2.5,12.449,3.754C68.069,11.844,69.654,13.997,69.654,16.434z"/>
	
<path fill="url(#baseGradient)" opacity="0.25" d="M90,103.186h-8.689L69.654,88.179c0,0,0-53.357,0-71.745c0-2.437-1.586-4.59-3.901-5.284
	c-4.147-1.254-8.293-2.5-12.448-3.754c0-2.135,0-4.27,0-6.396h-9.738c0,2.126,0,4.261,0,6.396c-4.156,1.254-8.302,2.5-12.449,3.754
	c-2.316,0.694-3.9,2.846-3.9,5.284c0,16.743,0,33.493,0,50.244c-2.113,0-4.217,0-6.33,0v9.838h6.33v11.662L7.339,113.771v11.164H1
	v9.838c2.113,0,4.226,0,6.339,0v0.002c0,7,0,14.002,0,21.002c7.483,13.201,14.966,26.402,22.458,39.604
	c11.322,7.49,25.954,7.49,37.276,0c1.989-3.505,3.971-7.001,5.96-10.506c2.896,0,5.803,0,8.708,0v-4.358h-6.233l14.025-24.74
	v-42.006l-4.824-6.219H90V103.186z"/>
	
<g class="stroke">
	<line x1="52.407" y1="127.952" x2="89.533" y2="127.952"/>
	<polyline points="15.139,127.952 15.148,127.952 44.465,127.952"/>
	<path d="M1,134.775
		c2.113,0,4.226,0,6.339,0c0,7.001,0,14.002,0,21.003c7.483,13.201,14.966,26.402,22.458,39.604c11.322,7.49,25.954,7.49,37.276,0
		c1.99-3.505,3.971-7.001,5.96-10.506c2.896,0,5.802,0,8.708,0"/>
	<polyline points="20.888,76.517 27.218,76.517 27.218,88.179 7.339,113.772 7.339,121.138 7.339,124.937 1,124.937 	"/>
	<path d="M53.305,1
		c0,2.126,0,4.261,0,6.396c4.156,1.254,8.302,2.5,12.449,3.754c2.316,0.693,3.9,2.846,3.9,5.284c0,18.388,0,36.767,0,55.163
		c0,5.524,0,11.058,0,16.582c3.882,5,7.774,9.999,11.657,15.007c2.896,0,5.793,0,8.689,0"/>
	<polyline points="90,107.554 84.709,107.554 89.533,113.772 89.533,127.952 89.533,155.778 75.508,180.518 81.742,180.518 	"/>
	<path d="M43.567,1
		c0,2.126,0,4.261,0,6.396c-4.155,1.254-8.302,2.5-12.449,3.754c-2.316,0.693-3.9,2.846-3.9,5.284c0,16.742,0,33.493,0,50.244
		c-2.113,0-4.217,0-6.33,0"/>
	<line x1="20.888" y1="71.597" x2="69.654" y2="71.597"/>
</g>

<g class="thick">
	<line x1="81.742" y1="186.896" x2="81.742" y2="178.498"/>
	<line x1="90" y1="101.167" x2="90" y2="109.574"/>
	<line x1="1" y1="122.473" x2="1" y2="137.239"/>
	<line x1="20.888" y1="64.213" x2="20.888" y2="78.981"/>
	<line x1="55.744" y1="1" x2="41.129" y2="1"/>
</g>

<g class="dash">
	<line x1="7.339" y1="124.937" x2="7.339" y2="134.775"/>
	<polyline points="27.218,88.179 44.465,109.289 44.465,186.584 52.407,186.584 52.407,109.289 69.654,88.179 	"/>
	<polyline points="7.339,134.775 12.85,134.775 17.437,121.138 7.339,121.138 	"/>
</g>

</svg>
