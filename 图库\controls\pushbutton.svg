<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 13.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 14948)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default"
     xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" 
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:agg="http://www.example.com"
     x="0px" y="0px"  
     viewBox="0 0 127.21 127.21" enable-background="new 0 0 127.21 127.21"
     xml:space="preserve">

	<agg:params>
		<agg:param name="onColor" description="On Color" type="C" cssAttributes="fill" classes="onColor"/>
		<agg:param name="offColor" description="Off Color" type="C" cssAttributes="fill" classes="offColor"/>
		<agg:param name="state" description="State" type="state" forceRepaint="true">
			<agg:state name="on" description="On">
				<agg:param attributes="visibility" ids="buttonOnState" value="visible"/>
				<agg:param attributes="visibility" ids="buttonOffState" value="hidden"/>
			</agg:state>
			<agg:state name="off" description="Off">
				<agg:param attributes="visibility" ids="buttonOnState" value="hidden"/>
				<agg:param attributes="visibility" ids="buttonOffState" value="visible"/>
			</agg:state>
		</agg:param>
	</agg:params>

<style type="text/css" >
   <![CDATA[

	.offColor {
		fill: blue;
	}

	.onColor {
		fill: red;
	}


      ]]>
</style>

<defs>
	<path id="buttonOff" d="M82.142,32.138c-16.049-8.136-38.202-5.357-50.156,8.98
	c-13.402,16.073-6.325,36.825,11.435,45.829c16.049,8.137,38.202,5.356,50.155-8.98C106.978,61.891,99.9,41.141,82.142,32.138z"/>
	<path id="buttonOn" d="M100.393,45.433c-0.029-0.06-0.06-0.12-0.1-0.18c0-0.01,0-0.01,0-0.02c-1.17-9.96-7.89-19.17-18.22-24.41
			c-16.05-8.14-38.21-5.36-50.16,8.98c-4.07,4.88-6.25,10.2-6.82,15.49c-0.02,0.05-0.05,0.09-0.07,0.14
			c-9.2,17.45-0.01,35.89,16.49,44.25c18.33,9.229,44.041,5.14,56.611-12.041C105.003,68.233,105.883,55.713,100.393,45.433z"/>
</defs>


<clipPath id="buttonOffMask">
	<use xlink:href="#buttonOff"  overflow="visible"/>
</clipPath>

<clipPath id="buttonOnMask">
	<use xlink:href="#buttonOn"  overflow="visible"/>
</clipPath>

<radialGradient id="radialMetallRing" cx="13" cy="6" r="140" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="0.73" style="stop-color:#C0C0C0"/>
	<stop  offset="0.75" style="stop-color:#F2F2F2"/>
	<stop  offset="1" style="stop-color:#9E9E9E"/>
</radialGradient>

<radialGradient id="hollowGloss" cx="40" cy="63.5" r="58" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#444444"/>
</radialGradient>

<linearGradient id="bodyGradient" gradientUnits="userSpaceOnUse" x1="25" y1="54" x2="100.5" y2="54">
	<stop  offset="0" style="stop-color:#878787"/>
	<stop  offset="1" style="stop-color:#FFFFFF"/>
</linearGradient>



<path fill="url(#radialMetallRing)" d="M95.048,19.523C68.3,5.962,31.378,10.594,11.456,34.489C-10.88,61.28,0.914,95.865,30.513,110.871
	c26.748,13.561,63.67,8.928,83.592-14.967C136.441,69.113,124.647,34.528,95.048,19.523z"/>

<path fill="#FFFFFF" d="M22.37,26.961c23.367-18.521,57.454-18.521,80.821,0C79.824,11.214,45.737,11.214,22.37,26.961z"/>
<path fill="#FFFFFF" d="M22.37,103.431c23.367,18.521,57.454,18.521,80.821,0C79.824,119.179,45.737,119.179,22.37,103.431z"/>
<path fill="#FFFFFF" d="M31.298,83.757c18.204,18.521,44.76,18.521,62.964,0C76.058,99.504,49.502,99.504,31.298,83.757z"/>

<g id="buttonOnState" visibility="visible">

<path class="onColor" d="M104.05,59.542c0,21.983-21.57,34.962-41.269,34.962c-19.701,0-41.271-12.977-41.271-34.962
	c0-21.982,21.572-34.96,41.271-34.96C82.482,24.582,104.05,37.557,104.05,59.542z"/> 



<path opacity="0.6" fill="#444444" d="M62.781,24.582c-19.698,0-41.271,12.978-41.271,34.96c0,21.985,21.569,34.962,41.271,34.962
	c19.699,0,41.269-12.979,41.269-34.962C104.05,37.557,82.482,24.582,62.781,24.582z M93.576,77.966
	c-11.953,14.337-34.106,17.117-50.155,8.98c-17.76-9.004-24.837-29.755-11.435-45.829c11.954-14.338,34.107-17.117,50.156-8.98
	C99.9,41.141,106.978,61.891,93.576,77.966z"/>

<path opacity="0.3" fill="url(#hollowGloss)" d="M89.742,70.736c10.072-14.259,2.297-30.927-12.729-37.395
	c-13.714-5.903-32.14-2.749-41.189,10.059c-10.073,14.26-2.298,30.929,12.728,37.396C62.265,86.699,80.693,83.544,89.742,70.736z"/>



<path d="M82.142,32.138c-16.049-8.136-38.202-5.357-50.156,8.98
	c-13.402,16.073-6.325,36.825,11.435,45.829c16.049,8.137,38.202,5.356,50.155-8.98C106.978,61.891,99.9,41.141,82.142,32.138z
	 M89.742,70.736c-9.049,12.809-27.477,15.963-41.19,10.061C33.526,74.33,25.751,57.661,35.824,43.4
	c9.049-12.808,27.475-15.962,41.189-10.059C92.039,39.809,99.814,56.477,89.742,70.736z" opacity="0.3" fill="url(#bodyGradient)"/>

<path opacity="0.3" fill="#FFFFFF"  clip-path="url(#buttonOffMask)" d="M95.31,41.318c0.426-1.449,0.652-2.946,0.652-4.479
	c0-12.667-15.211-22.937-33.975-22.937c-18.764,0-33.975,10.27-33.975,22.937c0,1.482,0.216,2.931,0.613,4.335h-2.681v7.54h6.97l0,0
	c5.955,6.63,16.746,11.061,29.072,11.061c12.366,0,24.28-4.428,30.223-11.097l5.877,0.18v-7.539H95.31z"/>

<path fill="#FFFFFF" d="M36.323,63.933c-4.578-19.457,11.027-31.016,26.074-32.76c-16.76,0.837-30.042,12.151-30.042,26.021
	c0,2.332,0.386,4.591,1.089,6.739H36.323z"/>
<path fill="#FFFFFF" d="M34.089,65.899c3.896,7.827,11.916,13.835,21.775,16.1c-7.229-1.952-16.848-9.584-19.013-16.118
	L34.089,65.899z"/>

</g>


<g id="buttonOffState" visibility="hidden">
<use xlink:href="#buttonOn" class="offColor"/>


<path opacity="0.6" fill="#444444" d="M100.394,45.433c5.49,10.28,4.601,22.8-2.28,32.2c-12.569,17.19-38.279,21.28-56.6,12.05
	c-16.5-8.369-25.69-26.8-16.49-44.25c0.02-0.05,0.05-0.09,0.07-0.14c-0.16,1.52-0.19,3.03-0.09,4.54c0,3.28,0,6.57,0,9.85
	c0,20.08,19.7,31.93,37.7,31.94c17.989,0,37.699-11.86,37.699-31.94c0-3.17,0-6.33,0-9.5c0.12-1.66,0.08-3.31-0.109-4.94
	C100.324,45.313,100.354,45.373,100.394,45.433z"/>



<path opacity="0.3" fill="url(#hollowGloss)" d="M89.74,59.273c10.072-14.259,2.297-30.927-12.729-37.395
	c-13.713-5.903-32.139-2.749-41.188,10.059c-10.074,14.26-2.298,30.929,12.728,37.396C62.263,75.236,80.69,72.082,89.74,59.273z"/>

<path opacity="0.3" fill="url(#bodyGradient)" d="M100.296,45.257c-0.002-0.002-0.002-0.003-0.003-0.004c0-0.01,0-0.01,0-0.02
	c-1.17-9.96-7.89-19.17-18.22-24.41c-16.05-8.14-38.21-5.36-50.16,8.98c-4.07,4.88-6.25,10.2-6.82,15.49
	c-0.02,0.05-0.05,0.09-0.07,0.14c0.02-0.05,0.05-0.09,0.07-0.14c-0.16,1.52-0.19,3.03-0.09,4.54c0,3.28,0,6.57,0,9.85
	c0,20.08,19.7,31.93,37.7,31.94c17.989,0,37.699-11.86,37.699-31.94c0-3.17,0-6.33,0-9.5
	C100.523,48.527,100.484,46.882,100.296,45.257z M89.74,59.273c-9.05,12.808-27.477,15.962-41.19,10.06
	c-15.026-6.466-22.801-23.135-12.728-37.396C44.871,19.13,63.297,15.976,77.01,21.879C92.037,28.346,99.812,45.014,89.74,59.273z"/>

<path opacity="0.3" clip-path="url(#buttonOnMask)" fill="#FFFFFF" d="M95.308,29.855
		c0.425-1.449,0.652-2.946,0.652-4.479c0-12.667-15.212-22.936-33.976-22.936S28.01,12.708,28.01,25.375
		c0,1.483,0.216,2.932,0.614,4.336h-2.681v7.54h6.97l0,0c5.956,6.63,16.747,11.061,29.072,11.061
		c12.366,0,24.28-4.428,30.223-11.097l5.878,0.18v-7.54H95.308z"/>



<path fill="#FFFFFF" opacity="0.5" d="M25.079,48.08c0.667,19.637,20.04,31.04,37.695,31.041c17.672,0.003,37.021-11.396,37.707-31.041
	c0.646,20.508-19.341,32.832-37.7,32.832C44.432,80.912,24.417,68.587,25.079,48.08z"/>
<path fill="#FFFFFF" d="M36.322,52.471C31.743,33.014,47.349,21.455,62.396,19.71c-16.76,0.837-30.042,12.152-30.042,26.021
	c0,2.332,0.386,4.591,1.089,6.739H36.322z"/>
<path fill="#FFFFFF" d="M34.086,54.437c3.897,7.827,11.917,13.835,21.776,16.1c-7.228-1.953-16.847-9.584-19.013-16.119
	L34.086,54.437z"/>
</g>

</svg>
