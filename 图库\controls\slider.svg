<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 13.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 14948)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default"
     xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" 
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:agg="http://www.example.com"
     x="0px" y="0px" 
      viewBox="32.349 -65.143 59.528 257.949" enable-background="new 32.349 -65.143 59.528 257.949"
     xml:space="preserve">

    <agg:params>
        <agg:param name="color" description="Color" type="C" cssAttributes="fill" classes="maincolor"/>
        <agg:param name="value" description="Value" type="level" attributes="transform" pattern="translate(0 {0})" min="0" max="185" ids="slider"/>
    </agg:params>

    <style type="text/css">
   <![CDATA[
        .maincolor {
            fill: #1767DD;
        }
        ]]>
</style>

    <defs>
        <filter id="Multiply">
            <feBlend mode="multiply" in2="BackgroundImage" in="SourceGraphic"/>
        </filter>

        <filter id="Screen">
            <feBlend mode="screen" in2="BackgroundImage" in="SourceGraphic"/>
        </filter>

        <path id="sliderBody" d="M83.372-14.417c3.915,0,7.088-3.173,7.088-7.087c0-4.725,0-9.448,0-14.173
				c0-3.913-3.173-7.087-7.088-7.087c-14.172,0-28.346,0-42.52,0c-3.913,0-7.086,3.174-7.086,7.087c0,4.725,0,9.448,0,14.173
				c0,3.914,3.173,7.087,7.086,7.087C55.026-14.417,69.2-14.417,83.372-14.417z"/>

        <path id="gauge" d="M69.2,184.304c0,3.913-3.174,7.086-7.087,7.086s-7.086-3.173-7.086-7.086c0-80.315,0-160.63,0-240.945
	c0-3.913,3.172-7.086,7.086-7.086s7.087,3.173,7.087,7.086C69.2,23.674,69.2,103.989,69.2,184.304z"/>

    </defs>

    <linearGradient id="sliderGradient" gradientUnits="userSpaceOnUse" x1="67.105" y1="63.8311" x2="57.1043"
                    y2="63.8311">
        <stop offset="0" style="stop-color:#231F20"/>
        <stop offset="1" style="stop-color:#D5D7D9"/>
    </linearGradient>

    <linearGradient id="shadowGradient" gradientUnits="userSpaceOnUse" x1="62.1133" y1="-10.4302" x2="62.1133"
                    y2="-14.9302">
        <stop offset="0" style="stop-color:#FFFFFF"/>
        <stop offset="1" style="stop-color:#737373"/>
    </linearGradient>

    <radialGradient id="sliderBodyGradient" cx="62.1128" cy="-28.5898" r="31.4922" gradientUnits="userSpaceOnUse">
        <stop offset="0" style="stop-color:#FFFFFF"/>
        <stop offset="1" style="stop-color:#000000"/>
    </radialGradient>

    <clipPath id="sliderMask">
        <use xlink:href="#sliderBody" overflow="visible"/>
    </clipPath>

    <use xlink:href="#gauge" fill="#6B6B6B"/>

    <path fill="url(#sliderGradient)" filter="url(#Screen)" d="M64.605-60.744c-0.729-0.444-1.577-0.715-2.492-0.715c-2.657,0-4.818,2.161-4.818,4.818v240.945
	c0,2.656,2.161,4.817,4.818,4.817c0.915,0,1.763-0.271,2.492-0.715V-60.744z"/>

    <g id="slider" transform="translate(0 185)">

        <rect x="55.026" y="-14.93" fill="url(#shadowGradient)" filter="url(#Multiply)"  />

        <rect x="33.766" y="-42.763" clip-path="url(#sliderMask)" class="maincolor"  />

        <rect x="33.766" y="-42.763" opacity="0.3" clip-path="url(#sliderMask)" fill="url(#sliderBodyGradient)"
               />

        <path opacity="0.25" clip-path="url(#sliderMask)" fill="#FFFFFF" d="M75.722-55.852c14.365,3.627,21.804,12.988,12.167,20.986
			c-8.651,7.192-26.267,8.961-39.381,5.651c-14.374-3.632-21.803-12.987-12.176-20.991C44.993-57.398,62.608-59.167,75.722-55.852z"
                />

        <path fill="#FFFFFF" d="M45.105-18.794c-0.392,0-0.709-0.317-0.709-0.709l0,0c0-0.392,0.317-0.709,0.709-0.709l0,0h34.016
			c0.392,0,0.709,0.317,0.709,0.709l0,0c0,0.392-0.317,0.709-0.709,0.709l0,0H45.105L45.105-18.794z"/>

        <path fill="#FFFFFF" d="M40.853-27.173c-0.782,0-1.417-0.634-1.417-1.416l0,0c0-0.784,0.635-1.418,1.417-1.418l0,0h42.52
			c0.783,0,1.417,0.634,1.417,1.418l0,0c0,0.782-0.634,1.416-1.417,1.416l0,0H40.853L40.853-27.173z"/>

        <path fill="#FFFFFF" d="M45.105-36.966c-0.392,0-0.709-0.318-0.709-0.71l0,0c0-0.392,0.317-0.709,0.709-0.709l0,0h34.016
			c0.392,0,0.709,0.317,0.709,0.709l0,0c0,0.392-0.317,0.71-0.709,0.71l0,0H45.105L45.105-36.966z"/>

    </g>
</svg>
