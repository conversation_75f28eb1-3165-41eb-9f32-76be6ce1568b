<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 13.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 14948)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default"
     xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" 
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:agg="http://www.example.com"
     x="0px" y="0px"  
     viewBox="0 0 127.21 127.21" enable-background="new 0 0 127.21 127.21"
     xml:space="preserve">
	 
    <agg:params>
        <agg:param name="color" description="Color" type="C" cssAttributes="fill" classes="maincolor"/>
		<agg:param name="state" description="State" type="state">
			<agg:state name="off" description="Off">
				<agg:param attributes="visibility" ids="off_state" value="visible"/>
				<agg:param attributes="visibility" ids="on_state" value="hidden"/>
			</agg:state>
			<agg:state name="on" description="On">
				<agg:param attributes="visibility" ids="off_state" value="hidden"/>
				<agg:param attributes="visibility" ids="on_state" value="visible"/>
			</agg:state>
		</agg:param>
	</agg:params>

<style type="text/css" >
   <![CDATA[

	.maincolor {
		fill: #E62222;

	}

      ]]>
</style>


<defs>
	<path id="switchContour" d="M78.811,19.659c-10.283,0-20.565,0-30.848,0c-1.17,0-2.123,0.953-2.123,2.13c0,12.225,0,24.457,0,36.683
	c0,12.043,0,24.09,0,36.135c0,3.727,0,7.447,0,11.174c0,1.178,0.953,2.123,2.123,2.123c10.282,0,20.564,0,30.848,0
	c1.178,0,2.131-0.945,2.131-2.123c0-3.727,0-7.447,0-11.174c0-12.045,0-24.092,0-36.135c0-12.226,0-24.458,0-36.683
	C80.942,20.611,79.989,19.659,78.811,19.659z"/>

	<filter id="Multiply">
		<feBlend mode="multiply" in2="BackgroundImage" in="SourceGraphic"/>
	</filter>

	<filter id="Screen">
		<feBlend mode="screen" in2="BackgroundImage" in="SourceGraphic"/>
	</filter>
</defs>

<linearGradient id="darkOnGradient" gradientUnits="userSpaceOnUse" x1="63.3916" y1="110.0229" x2="63.3916" y2="19.6657">
	<stop  offset="0" style="stop-color:#000000"/>
	<stop  offset="0.0318" style="stop-color:#272727"/>
	<stop  offset="0.1047" style="stop-color:#7B7B7B"/>
	<stop  offset="0.1563" style="stop-color:#B0B0B0"/>
	<stop  offset="0.1813" style="stop-color:#C4C4C4"/>
	<stop  offset="0.1923" style="stop-color:#E6E6E6"/>
	<stop  offset="0.5714" style="stop-color:#FFFFFF"/>
	<stop  offset="0.5879" style="stop-color:#A8A8A8"/>
	<stop  offset="1" style="stop-color:#FFFFFF"/>
</linearGradient>

<linearGradient id="lightOnGradient" gradientUnits="userSpaceOnUse" x1="63.3906" y1="56.9429" x2="63.3906" y2="93.4505">
	<stop  offset="0" style="stop-color:#000000"/>
	<stop  offset="0.0037" style="stop-color:#090909"/>
	<stop  offset="0.0244" style="stop-color:#373737"/>
	<stop  offset="0.0476" style="stop-color:#626262"/>
	<stop  offset="0.0727" style="stop-color:#888888"/>
	<stop  offset="0.0999" style="stop-color:#A8A8A8"/>
	<stop  offset="0.13" style="stop-color:#C4C4C4"/>
	<stop  offset="0.1639" style="stop-color:#DADADA"/>
	<stop  offset="0.2036" style="stop-color:#EAEAEA"/>
	<stop  offset="0.2525" style="stop-color:#F6F6F6"/>
	<stop  offset="0.3212" style="stop-color:#FDFDFD"/>
	<stop  offset="0.511" style="stop-color:#FFFFFF"/>
	<stop  offset="0.675" style="stop-color:#FDFDFD"/>
	<stop  offset="0.7417" style="stop-color:#F6F6F6"/>
	<stop  offset="0.7908" style="stop-color:#EAEAEA"/>
	<stop  offset="0.8312" style="stop-color:#D8D8D8"/>
	<stop  offset="0.8662" style="stop-color:#C1C1C1"/>
	<stop  offset="0.8976" style="stop-color:#A5A5A5"/>
	<stop  offset="0.9262" style="stop-color:#848484"/>
	<stop  offset="0.9527" style="stop-color:#5D5D5D"/>
	<stop  offset="0.9764" style="stop-color:#323232"/>
	<stop  offset="0.9987" style="stop-color:#030303"/>
	<stop  offset="1" style="stop-color:#000000"/>
</linearGradient>

<linearGradient id="darkOffGradient" gradientUnits="userSpaceOnUse" x1="63.3896" y1="19.4385" x2="63.3896" y2="107.6841">
	<stop  offset="0" style="stop-color:#1F1F1F"/>
	<stop  offset="0.0306" style="stop-color:#616161"/>
	<stop  offset="0.0659" style="stop-color:#A8A8A8"/>
	<stop  offset="0.0824" style="stop-color:#C4C4C4"/>
	<stop  offset="0.3297" style="stop-color:#E6E6E6"/>
	<stop  offset="0.5714" style="stop-color:#E6E6E6"/>
	<stop  offset="0.5879" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#D1D1D1"/>
</linearGradient>

<linearGradient id="lightOffGradient" gradientUnits="userSpaceOnUse" x1="63.3896" y1="19.6235" x2="63.3896" y2="34.124">
	<stop  offset="0" style="stop-color:#000000"/>
	<stop  offset="0.105" style="stop-color:#3E3E3E"/>
	<stop  offset="0.2531" style="stop-color:#909090"/>
	<stop  offset="0.3749" style="stop-color:#CCCCCC"/>
	<stop  offset="0.4639" style="stop-color:#F1F1F1"/>
	<stop  offset="0.511" style="stop-color:#FFFFFF"/>
	<stop  offset="0.675" style="stop-color:#FDFDFD"/>
	<stop  offset="0.7417" style="stop-color:#F6F6F6"/>
	<stop  offset="0.7908" style="stop-color:#EAEAEA"/>
	<stop  offset="0.8312" style="stop-color:#D8D8D8"/>
	<stop  offset="0.8662" style="stop-color:#C1C1C1"/>
	<stop  offset="0.8976" style="stop-color:#A5A5A5"/>
	<stop  offset="0.9262" style="stop-color:#848484"/>
	<stop  offset="0.9527" style="stop-color:#5D5D5D"/>
	<stop  offset="0.9764" style="stop-color:#323232"/>
	<stop  offset="0.9987" style="stop-color:#030303"/>
	<stop  offset="1" style="stop-color:#000000"/>
</linearGradient>

<linearGradient id="front_side" gradientUnits="userSpaceOnUse" x1="10.7959" y1="-28.9365" x2="99.6713" y2="124.0644">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#525252"/>
</linearGradient>

<linearGradient id="side_1" gradientUnits="userSpaceOnUse" x1="64.6558" y1="17.5024" x2="64.6558" y2="2.8776">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="0.1534" style="stop-color:#FBFBFB"/>
	<stop  offset="0.3057" style="stop-color:#EFEFEF"/>
	<stop  offset="0.4576" style="stop-color:#DBDBDB"/>
	<stop  offset="0.6095" style="stop-color:#BFBFBF"/>
	<stop  offset="0.7612" style="stop-color:#9B9B9B"/>
	<stop  offset="0.9109" style="stop-color:#707070"/>
	<stop  offset="1" style="stop-color:#525252"/>
</linearGradient>

<linearGradient id="side_2" gradientUnits="userSpaceOnUse" x1="83.6509" y1="63.7891" x2="98.2742" y2="63.7891">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#525252"/>
</linearGradient>

<linearGradient id="side_3" gradientUnits="userSpaceOnUse" x1="64.6558" y1="94.7563" x2="64.6558" y2="123.628">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#525252"/>
</linearGradient>	

<linearGradient id="side_4" gradientUnits="userSpaceOnUse" x1="47.2739" y1="63.7891" x2="30.3996" y2="63.7891">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#525252"/>
</linearGradient>

<clipPath id="switchMask">
	<use xlink:href="#switchContour"  overflow="visible"/>
</clipPath>

<path fill="#3D3D3D" d="M83.792,18.08v91.418H45.527V18.08H83.792z"/>
	
<path fill="url(#front_side)" d="M90.167,11.697v104.176H39.145V11.697h6.382h35.131H90.167z M83.792,109.499V18.08H45.527v91.418
		H83.792z"/>
	
<path fill="url(#side_2)" d="M98.671,7.445c0,37.56,0,75.12,0,112.681c0,1.178-0.473,2.236-1.244,3.008l-7.26-7.26V11.697
		l7.26-7.252C98.199,5.21,98.671,6.268,98.671,7.445z"/>
	
<path fill="url(#side_3)" d="M90.167,115.874l7.26,7.26c-0.771,0.766-1.838,1.244-3.008,1.244c-19.836,0-39.682,0-59.527,0
		c-1.178,0-2.235-0.479-3.007-1.244l7.26-7.26H90.167z"/>
	
<path fill="url(#side_4)" d="M39.145,11.697v104.176l-7.26,7.26c-0.765-0.771-1.245-1.83-1.245-3.008
		c0-37.561,0-75.121,0-112.681c0-1.177,0.48-2.235,1.245-3L39.145,11.697z"/>
	
<path fill="url(#side_1)" d="M97.427,4.445l-7.26,7.252H39.145l-7.26-7.252c0.772-0.772,1.83-1.245,3.007-1.245
		c19.845,0,39.691,0,59.527,0C95.589,3.2,96.656,3.672,97.427,4.445z"/>

<g id="on_state" transform="translate(1.25,0)" visibility="visible">
	<rect x="45.84" y="19.666" clip-path="url(#switchMask)" class="maincolor"  />
	<rect opacity="0.8" fill="url(#darkOnGradient)" filter="url(#Multiply)" clip-path="url(#switchMask)" x="45.84" y="19.659"   />
	<rect opacity="0.5" fill="url(#lightOnGradient)" filter="url(#Screen)" clip-path="url(#switchMask)" x="45.84" y="57.124"  />
	<rect x="61.261" y="26.582" fill="#FFFFFF"  />
	<path fill="#FFFFFF" d="M63.391,66.016c-6.458,0-11.692,4.712-11.692,10.523s5.234,10.523,11.692,10.523
		c6.457,0,11.693-4.712,11.693-10.523S69.847,66.016,63.391,66.016z M63.391,84.195c-4.695,0-8.505-3.43-8.505-7.655
		s3.81-7.655,8.505-7.655c4.695,0,8.505,3.43,8.505,7.655S68.085,84.195,63.391,84.195z"/>
</g>

<g id="off_state" visibility="hidden">
	<g transform="translate(1.25,0)">
		<rect x="45.84" y="19.666" clip-path="url(#switchMask)" class="maincolor"  />
		<rect x="45.84" y="19.666" opacity="0.8" clip-path="url(#switchMask)" fill="url(#darkOffGradient)"   filter="url(#Multiply)"/>
		<rect x="45.84" y="19.666" opacity="0.5" clip-path="url(#switchMask)" fill="url(#lightOffGradient)"   filter="url(#Screen)"/>
	</g>
	<rect x="62.531" y="39.342" fill="#FFFFFF"  />
	<path fill="#FFFFFF" d="M64.66,77.609c-6.457,0-11.691,5.232-11.691,11.691c0,6.457,5.234,11.693,11.691,11.693
		S76.353,95.757,76.353,89.3C76.353,82.841,71.117,77.609,64.66,77.609z M64.66,97.806c-4.694,0-8.504-3.811-8.504-8.506
		s3.81-8.504,8.504-8.504c4.695,0,8.506,3.809,8.506,8.504S69.355,97.806,64.66,97.806z"/>
</g>
		
</svg>
