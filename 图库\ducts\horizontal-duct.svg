<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 0 36.276 30.675" enable-background="new 0 0 36.276 30.675" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="2.9949" y1="687.8167" x2="38.5002" y2="652.3113" gradientTransform="matrix(1 0 0 -1 0 687.0337)">
	<stop  offset="0" style="stop-color:#99A3B2"/>
	<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
</linearGradient>
<polygon fill="url(#gradient)" points="34.244,30.175 34.244,28.29 0.568,28.29 0.568,2.385 34.244,2.385 34.244,0.5 35.776,0.5 35.776,30.175 "/>

<polygon class="color" points="34.244,0.5 34.244,2.385 0.568,2.385 0.568,28.29 34.244,28.29 34.244,30.175 35.776,30.175 35.776,0.5 "/>

<g class="stroke">
	<polyline fill="none" points="34.244,28.29 34.244,30.175 35.776,30.175 35.776,0.5 34.244,0.5 34.244,2.385"/>
	<rect x="0.568" y="2.385" fill="none"  />
	<rect x="2.123" y="3.94" fill="none"  />
	<line fill="none" x1="2.123" y1="26.736" x2="32.689" y2="3.94"/>
	<line fill="none" x1="2.123" y1="3.94" x2="32.689" y2="26.736"/>
</g>

<polygon opacity="0.1" points="17.404,15.34 2.123,3.94 2.123,26.736 "/>
<polygon opacity="0.15" fill="#FFFFFF" points="17.408,15.34 32.689,3.94 32.689,26.736 "/>
<polygon opacity="0.2" points="32.689,3.94 2.123,3.94 17.404,15.34 "/>
<polygon opacity="0.4" fill="#FFFFFF" points="32.689,26.736 17.404,15.34 2.123,26.736 "/>

</svg>
