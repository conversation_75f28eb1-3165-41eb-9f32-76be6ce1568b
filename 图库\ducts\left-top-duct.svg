<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 0 38.098 38.094" enable-background="new 0 0 38.098 38.094" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="-327.6316" y1="1287.719" x2="-292.1249" y2="1252.2123" gradientTransform="matrix(4.489659e-011 -1 1 4.489659e-011 -1251.6372 -291.5527)">
	<stop  offset="0" style="stop-color:#99A3B2"/>
	<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
</linearGradient>
<polygon fill="url(#gradient)" points="7.922,2.032 9.807,2.032 9.807,9.803 2.033,9.803 2.033,7.918 0.501,7.918 0.501,37.593 
	2.033,37.593 2.033,35.707 9.807,35.707 35.713,35.707 35.713,9.803 35.713,2.032 37.597,2.032 37.597,0.5 7.922,0.5 "/>

<polygon class="color" points="7.922,2.032 9.807,2.032 9.807,9.803 2.033,9.803 2.033,7.918 0.501,7.918 
	0.501,37.593 2.033,37.593 2.033,35.707 9.807,35.707 35.713,35.707 35.713,9.803 35.713,2.032 37.597,2.032 37.597,0.5 7.922,0.5"/>

<g class="stroke">
	<polyline fill="none" points="9.807,2.032 7.922,2.032 7.922,0.5 37.597,0.5 37.597,2.032 35.713,2.032"/>
	<polyline fill="none" points="35.713,9.803 35.713,2.032 9.807,2.032 9.807,9.803"/>
	<polyline fill="none" points="2.033,35.707 2.033,37.593 0.501,37.593 0.501,7.918 2.033,7.918 2.033,9.803"/>
	<polyline fill="none" points="9.807,35.707 2.033,35.707 2.033,9.803 9.807,9.803"/>
	<rect x="9.807" y="9.803" fill="none"  />
	<line fill="none" x1="11.362" y1="34.154" x2="34.158" y2="11.357"/>
	<line fill="none" x1="34.158" y1="34.154" x2="11.362" y2="11.357"/>
	<rect x="11.362" y="11.357" fill="none"  />
</g>

<polygon opacity="0.4" fill="#FFFFFF" points="22.758,22.757 34.158,34.154 11.362,34.154 "/>
<polygon opacity="0.2" points="22.758,22.753 34.158,11.357 11.362,11.357 "/>
<polygon opacity="0.15" fill="#FFFFFF" points="34.158,11.357 34.158,34.154 22.758,22.757 "/>
<polygon opacity="0.1" points="11.362,11.357 22.758,22.757 11.362,34.154 "/>


</svg>
