<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 0 38.098 38.094" enable-background="new 0 0 38.098 38.094"
	 xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="2.0183" y1="685.0188" x2="37.5255" y2="649.5116" gradientTransform="matrix(1 0 0 -1 0 687.0337)">
	<stop  offset="0" style="stop-color:#99A3B2"/>
	<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
</linearGradient>
<polygon fill="url(#gradient)" points="36.064,30.175 36.064,28.29 28.293,28.29 28.293,36.064 30.179,36.064 30.179,37.596 
	0.504,37.596 0.504,36.064 2.389,36.064 2.389,28.29 2.389,2.385 28.293,2.385 36.064,2.385 36.064,0.5 37.597,0.5 37.597,30.175 
	"/>
	
<polygon class="color" points="36.064,30.175 36.064,28.29 28.293,28.29 28.293,36.064 30.179,36.064 30.179,37.596 
	0.504,37.596 0.504,36.064 2.389,36.064 2.389,28.29 2.389,2.385 28.293,2.385 36.064,2.385 36.064,0.5 37.597,0.5 37.597,30.175 
	"/>
	
<g class="stroke">
	<polyline fill="none" points="36.064,28.29 36.064,30.175 37.597,30.175 37.597,0.5 36.064,0.5 36.064,2.385"/>
	<polyline fill="none" points="28.293,2.385 36.064,2.385 36.064,28.29 28.293,28.29"/>
	<polyline fill="none" points="2.389,36.064 0.504,36.064 0.504,37.596 30.179,37.596 30.179,36.064 28.293,36.064"/>
	<polyline fill="none" points="2.389,28.29 2.389,36.064 28.293,36.064 28.293,28.29"/>
	<rect fill="none" x="2.389" y="2.385"  />
	<line fill="none" x1="3.944" y1="26.736" x2="26.74" y2="3.94"/>
	<line fill="none" x1="3.944" y1="3.94" x2="26.74" y2="26.736"/>
	<rect x="3.944" y="3.94" fill="none"  />
</g>

<polygon opacity="0.1" fill="black" points="15.34,15.34 3.944,3.94 3.944,26.736 "/>
<polygon opacity="0.15" fill="white" points="15.344,15.34 26.74,3.94 26.74,26.736 "/>
<polygon opacity="0.2" fill="black" points="26.74,3.94 3.944,3.94 15.34,15.34 "/>
<polygon opacity="0.4" fill="white" points="26.74,26.736 15.34,15.34 3.944,26.736 "/>

</svg>
