<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 0 30.675 36.207" enable-background="new 0 0 30.675 36.207" xml:space="preserve">

	 <agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="-1316.5833" y1="1006.3547" x2="-1281.078" y2="970.8495" gradientTransform="matrix(-1.346898e-010 1 1 1.346898e-010 -974.897 1319.5093)">
	<stop  offset="0" style="stop-color:#99A3B2"/>
	<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
</linearGradient>
<polygon fill="url(#gradient)" points="0.5,34.174 2.385,34.174 2.385,0.499 28.29,0.499 28.29,34.174 30.175,34.174 30.175,35.707 0.5,35.707 "/>

<polygon class="color" points="30.175,34.174 28.29,34.174 28.29,0.499 2.385,0.499 2.385,34.174 0.5,34.174 0.5,35.707 30.175,35.707 "/>

<g class="stroke">
	<polyline fill="none" points="2.385,34.174 0.5,34.174 0.5,35.707 30.175,35.707 30.175,34.174 28.29,34.174"/>
	<rect x="2.385" y="0.499" fill="none"  />
	<rect x="3.939" y="2.053" fill="none"  />
	<line fill="none" x1="3.939" y1="2.053" x2="26.735" y2="32.62"/>
	<line fill="none" x1="26.735" y1="2.053" x2="3.939" y2="32.62"/>
</g>

<polygon opacity="0.2" points="15.335,17.334 26.735,2.053 3.939,2.053 "/>
<polygon opacity="0.4" fill="#FFFFFF" points="15.335,17.339 26.735,32.62 3.939,32.62 "/>
<polygon opacity="0.15" fill="#FFFFFF" points="26.735,32.62 26.735,2.053 15.335,17.334 "/>
<polygon opacity="0.1" points="3.939,32.62 15.335,17.334 3.939,2.053 "/>

</svg>
