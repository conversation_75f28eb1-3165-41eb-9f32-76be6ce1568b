<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 -8.417 41.68 22.214" enable-background="new 0 -8.417 41.68 22.214" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param  type="C" classes="color,stroked" name="strokeColor" description="Stroke Color" cssAttributes="stroke"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<circle cx="20.671" cy="-4.582" r="2.418"/>
	<circle cx="8.724" cy="0.188" r="2.418"/>
	<circle cx="3.964" cy="9.734" r="2.418"/>
	<circle cx="20.696" cy="9.734" r="2.418"/>
	<circle cx="32.773" cy="0.188" r="2.418"/>
	<circle cx="37.533" cy="9.734" r="2.418"/>
	<polyline points="18.344,9.656 6.367,9.656 7.496,10.876 	"/>
	<line x1="6.367" y1="9.656" x2="7.496" y2="8.438"/>
</g>
</svg>
