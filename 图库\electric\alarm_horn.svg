<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 4.273 39.063 19.141" enable-background="new 0 4.273 39.063 19.141" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<polygon class="color" points="31.518,5.691 7.446,5.691 12.695,12.901 7.446,12.901 7.446,21.998 31.518,21.998 31.518,12.901 26.271,12.901 "/>
<g class="stroked">
	<line x1="31.518" y1="17.449" x2="37.54" y2="17.449"/>
	<line x1="1.426" y1="17.449" x2="7.446" y2="17.449"/>
	<line x1="12.695" y1="12.901" x2="26.271" y2="12.901"/>
</g>
</svg>
