<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="4.347 0 35.336 43.986" enable-background="new 4.347 0 35.336 43.986" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<line x1="5.764" y1="14.764" x2="38.266" y2="14.764"/>
	<line x1="5.764" y1="25.093" x2="38.266" y2="25.093"/>
	<line x1="13.866" y1="20.427" x2="30.22" y2="20.427"/>
	<line x1="13.866" y1="29.714" x2="30.22" y2="29.714"/>
	<line x1="21.966" y1="14.764" x2="21.966" y2="1.417"/>
	<line x1="21.966" y1="42.567" x2="21.966" y2="29.714"/>
</g>
</svg>
