<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 -3.339 39.063 15.147" enable-background="new 0 -3.339 39.063 15.147" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<line x1="1.469" y1="4.318" x2="18.625" y2="4.318"/>
	<line x1="20.565" y1="4.318" x2="37.883" y2="4.318"/>
	<line x1="18.625" y1="-1.922" x2="18.625" y2="10.392"/>
	<line x1="20.565" y1="0.213" x2="20.565" y2="8.422"/>
	<line x1="16.52" y1="-0.772" x2="16.52" y2="1.033"/>
	<line x1="15.712" y1="0.213" x2="17.329" y2="0.213"/>
	<line x1="21.862" y1="0.213" x2="23.479" y2="0.213"/>
</g>
</svg>
