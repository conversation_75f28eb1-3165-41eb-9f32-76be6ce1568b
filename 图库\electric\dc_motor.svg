<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="-5.339 16.376 39.063 17.197" enable-background="new -5.339 16.376 39.063 17.197"
	 xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param  type="C" classes="color,stroked" name="strokeColor" description="Stroke Color" cssAttributes="stroke"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>

<g class="stroked">
	<line x1="6.004" y1="26.149" x2="-2.22" y2="26.149"/>
	<polyline points="8.633,24.1 6.004,24.1 6.004,28.199 8.633,28.199 "/>
	<line x1="22.449" y1="26.149" x2="30.672" y2="26.149"/>
	<polyline points="19.868,24.1 22.449,24.1 22.449,28.199 19.868,28.199 "/>
	<path d="M-3.702,18.903H9.733v0.01
		c0-0.618,0.501-1.119,1.119-1.119c0.618,0,1.119,0.501,1.119,1.119c0-0.618,0.501-1.119,1.119-1.119
		c0.618,0,1.119,0.501,1.119,1.119c0-0.618,0.5-1.119,1.119-1.119s1.119,0.501,1.119,1.119c0-0.618,0.5-1.119,1.119-1.119
		s1.119,0.501,1.119,1.119l0.037-0.01h13.436"/>
</g>	
<circle class="color" cx="14.322" cy="26.174" r="5.983"/>
</svg>
