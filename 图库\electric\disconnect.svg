<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 -3.156 40.865 19.421" enable-background="new 0 -3.156 40.865 19.421" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param  type="C" classes="color,stroked" name="strokeColor" description="Stroke Color" cssAttributes="stroke"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<circle cx="3.046" cy="-0.089" r="1.65"/>
	<circle cx="3.046" cy="13.211" r="1.65"/>
	<circle cx="15.728" cy="-0.089" r="1.65"/>
	<circle cx="15.728" cy="13.211" r="1.65"/>
	<circle cx="28.074" cy="-0.089" r="1.65"/>
	<circle cx="28.074" cy="13.211" r="1.65"/>
	<line x1="4.696" y1="13.211" x2="7.273" y2="-1.729"/>
	<line x1="17.378" y1="13.211" x2="20.001" y2="-1.729"/>
	<line x1="29.724" y1="13.211" x2="32.297" y2="-1.729"/>
	<polyline points="5.984,6.276 38.408,6.276 39.851,1.774 "/>
</g>
</svg>
