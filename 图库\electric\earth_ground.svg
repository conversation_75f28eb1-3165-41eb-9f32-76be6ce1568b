<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 8.61 43.984 27.977" enable-background="new 0 8.61 43.984 27.977" xml:space="preserve">
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>

<g class="stroked">
	<line x1="1.417" y1="21.021" x2="42.439" y2="21.021"/>
	<line x1="5.187" y1="26.028" x2="38.668" y2="26.028"/>
	<line x1="9.4" y1="30.601" x2="34.506" y2="30.601"/>
	<line x1="15.148" y1="35.17" x2="28.763" y2="35.17"/>
	<line x1="21.927" y1="21.021" x2="21.927" y2="10.027"/>
</g>

</svg>
