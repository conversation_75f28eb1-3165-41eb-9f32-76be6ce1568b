<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_2" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 18.01 43.984 10.616" enable-background="new 0 18.01 43.984 10.616"
	 xml:space="preserve" xmlns:agg="http://www.example.com">

<agg:params>
      <agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
      <agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>

<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
	 
<line class="stroked" x1="1.451" y1="23.345" x2="42.439" y2="23.345"/>
<rect class="color" x="9.267" y="19.427"  />
<line class="stroked" x1="13.201" y1="19.427" x2="13.201" y2="27.209"/>
<line class="stroked" x1="30.689" y1="19.427" x2="30.689" y2="27.209"/>
</svg>
