<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 0 39.646 43.986" enable-background="new 0 0 39.646 43.986" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<line x1="1.417" y1="26.699" x2="34.732" y2="26.699"/>
	<line x1="20.067" y1="24.135" x2="20.067" y2="29.416"/>
	<line x1="30.535" y1="20.876" x2="36.301" y2="13.695"/>
	<line x1="22.752" y1="11.221" x2="28.515" y2="4.04"/>
	<ellipse cx="17.968" cy="26.7" rx="12.567" ry="15.433"/>
</g>

<g class="color">
	<polygon points="34.91,12.578 37.515,14.67 38.228,11.028 	"/>
	<polygon points="27.125,2.923 29.73,5.015 30.443,1.373 	"/>
	<polygon points="15.842,26.699 15.842,24.135 20.067,26.699 15.842,29.416 15.842,26.699 	"/>
</g>

</svg>
