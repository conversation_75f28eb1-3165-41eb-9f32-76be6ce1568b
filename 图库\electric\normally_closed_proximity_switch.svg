<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 -17.155 41.68 41.681" enable-background="new 0 -17.155 41.68 41.681" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param  type="C" classes="color,stroked" name="strokeColor" description="Stroke Color" cssAttributes="stroke"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<rect class="color" x="7.106" y="-10.049" transform="matrix(0.7071 -0.7071 0.7071 0.7071 3.4979 15.8152)"  />

<polygon class="stroked" points="27.067,2.133 9.703,3.879 27.067,5.73 	"/>
<line class="stroked" x1="35.709" y1="1.254" x2="27.067" y2="2.133"/>
<circle class="stroked" cx="7.465" cy="3.879" r="2.238"/>
<circle class="stroked" cx="32.795" cy="3.879" r="2.238"/>

</svg>
