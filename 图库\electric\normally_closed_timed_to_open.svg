<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 -12.249 41.68 26.046" enable-background="new 0 -12.249 41.68 26.046" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>

<g class="stroked">
	<circle cx="3.544" cy="-8.645" r="1.998"/>
	<circle cx="37.926" cy="-8.645" r="1.998"/>
	<line x1="20.781" y1="-9.785" x2="20.781" y2="3.087"/>
	<line x1="15.136" y1="12.43" x2="20.781" y2="3.087"/>
	<line x1="20.781" y1="3.087" x2="26.369" y2="12.43"/>
	<line x1="5.576" y1="-8.81" x2="39.81" y2="-10.998"/>
</g>
</svg>
