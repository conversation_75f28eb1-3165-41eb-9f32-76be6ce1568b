<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="1.235 35.652 41.679 28.558" enable-background="new 1.235 35.652 41.679 28.558" xml:space="preserve"
	>
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>

<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>

<line class="stroked" x1="21.847" y1="48.449" x2="21.847" y2="42.302"/>
<line class="stroked" x1="6.591" y1="39.239" x2="40.786" y2="46.102"/>
<circle class="color" cx="21.847" cy="55.621" r="7.172"/>
<circle class="color" cx="4.571" cy="39.239" r="2.02"/>
<circle class="color" cx="39.146" cy="39.239" r="2.021"/>
</svg>
