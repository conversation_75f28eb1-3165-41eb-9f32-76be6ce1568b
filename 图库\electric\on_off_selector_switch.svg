<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 -1.039 41.68 14.836" enable-background="new 0 -1.039 41.68 14.836" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<line x1="11.114" y1="7.037" x2="30.195" y2="7.037"/>
	<polyline points="20.651,7.139 20.651,3.813 26.654,0.378 	"/>
	<line x1="30.347" y1="9.962" x2="39.89" y2="9.962"/>
	<line x1="10.858" y1="9.962" x2="1.418" y2="9.962"/>
	<circle cx="13.277" cy="9.962" r="2.418"/>
	<circle cx="27.929" cy="9.961" r="2.418"/>
</g>
</svg>
