<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="-8.119 0 21.803 43.986" enable-background="new -8.119 0 21.803 43.986" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>

<g class="stroked">
	<line x1="2.641" y1="23.274" x2="2.641" y2="39.614"/>
	<line x1="-4.437" y1="39.614" x2="9.754" y2="39.614"/>
	<line x1="-2.137" y1="42.569" x2="7.453" y2="42.569"/>
	<ellipse cx="2.782" cy="12.346" rx="9.484" ry="10.929"/>
	<ellipse cx="2.587" cy="12.382" rx="4.618" ry="5.411"/>
</g>
</svg>
