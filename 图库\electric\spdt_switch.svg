<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="0 5.559 40.865 10.707" enable-background="new 0 5.559 40.865 10.707" xml:space="preserve">
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
</agg:params>
<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
<g class="stroked">
	<line x1="24.435" y1="10.899" x2="14.274" y2="10.899"/>
	<line x1="1.49" y1="10.899" x2="12.823" y2="10.899"/>
	<line x1="39.449" y1="7.702" x2="27.472" y2="7.702"/>
	<line x1="39.449" y1="14.096" x2="27.472" y2="14.096"/>
	<circle cx="26.747" cy="14.096" r="0.726"/>
	<circle cx="26.747" cy="7.702" r="0.726"/>
	<circle cx="13.548" cy="10.898" r="0.726"/>
</g>
</svg>
