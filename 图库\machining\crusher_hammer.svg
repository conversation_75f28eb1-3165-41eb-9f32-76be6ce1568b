<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='97.889px'

    height='49.999px'

    viewBox='0 0 97.889 49.999'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='24.999,48.943 72.889,48.943 96.833,1.056 1.056,1.056  ' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='72.889'

      y1='48.943'

      x2='72.889'

      y2='1.056' />

  <line x1='24.999'

      y1='1.056'

      x2='24.999'

      y2='48.943' />

  <polygon points='24.999,48.943 72.889,48.943 96.833,1.056 1.056,1.056  ' />

  <line x1='36.972'

      y1='13.027'

      x2='60.915'

      y2='36.973' />

  <line x1='54.931'

      y1='7.043'

      x2='66.903'

      y2='19.014' />

  <line x1='66.903'

      y1='30.985'

      x2='54.931'

      y2='42.959' />

  <line x1='42.957'

      y1='42.959'

      x2='30.987'

      y2='30.985' />

  <line x1='30.987'

      y1='19.014'

      x2='42.957'

      y2='7.043' />

  <line x1='36.972'

      y1='36.973'

      x2='60.915'

      y2='13.027' />

 </g>

</svg>