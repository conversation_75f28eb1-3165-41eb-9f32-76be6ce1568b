<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='97.887px'

    height='50.001px'

    viewBox='0 0 97.887 50.001'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='24.998,48.945 72.888,48.945 96.831,1.056 1.056,1.056  ' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='33.978'

      y1='10.034'

      x2='44.709'

      y2='20.767' />

  <line x1='53.177'

      y1='20.767'

      x2='63.909'

      y2='10.034' />

  <line x1='53.177'

      y1='29.234'

      x2='63.909'

      y2='39.966' />

  <circle cx='48.943'

      cy='25'

      r='5.987' />

  <line x1='33.978'

      y1='39.966'

      x2='44.709'

      y2='29.234' />

  <line x1='72.888'

      y1='1.056'

      x2='84.859'

      y2='25' />

  <line x1='24.998'

      y1='1.056'

      x2='13.027'

      y2='25' />

  <polygon points='24.998,48.945 72.888,48.945 96.831,1.056 1.056,1.056  ' />

 </g>

</svg>