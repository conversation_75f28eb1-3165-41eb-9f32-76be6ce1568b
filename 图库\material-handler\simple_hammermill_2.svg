<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 100 114" xml:space="preserve" xmlns:agg="http://www.example.com">

	   <agg:params>
	   	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	   	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
	   </agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.color,.stroked,.dashed{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed {
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:4,4;
	}

      ]]>
</style>	 

<polygon class="color" points="
	40.2,1 30.513,27.656 1,49.608 1,78.952 20.6,93.512 20.6,113 79.4,113 79.4,93.512 99,78.952 99,49.608 69.713,27.656 59.8,1 
	40.2,1 "/>
<line class="stroked" x1="20.6" y1="93.512" x2="20.6" y2="35.048"/>
<line class="stroked" x1="79.4" y1="93.512" x2="79.4" y2="35.048"/>
<circle class="dashed stroked" cx="50" cy="61.929" r="36.834"/>
</svg>
