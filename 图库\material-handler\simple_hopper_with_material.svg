<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 82 100" xml:space="preserve" xmlns:agg="http://www.example.com">

	   <agg:params>
	   	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	   	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
	   </agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.color,.stroked,.dashed{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed {
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1,2;
	}

      ]]>
</style>
	 
<polygon class="color" points="49.16,90.83 49.16,90.37 81,58.24 81,1 80.9,1 1.03,1 1,1 1,58.24 32.77,90.3 32.77,90.83
	20.51,90.83 20.51,99 32.77,99 32.77,97.95 34.84,94.91 47.1,94.91 49.16,97.99 49.16,99 61.49,99 61.49,90.83 "/>
<g class="stroked">
	<polyline points="15.557,32.577 
		11.033,30.812 5.328,30.812 2.967,24.144 9.459,19.829 17.524,21.397 19.295,29.439 15.557,32.577 	"/>
	<polyline points="12.213,38.232 
		17.131,37.84 22.41,35.323 27.329,40.39 23.394,47.058 15.557,49.02 10.246,42.743 12.213,38.232 	"/>
	<polyline points="49.95,39.801 
		45.425,37.84 39.721,37.84 37.36,31.204 43.852,27.085 51.918,28.654 53.885,36.5 49.95,39.801 	"/>
	<polyline points="63.098,17.083 
		67.623,18.652 73.328,18.456 75.885,24.928 69.787,29.439 61.721,28.262 59.393,20.417 63.098,17.083 	"/>
	<polyline points="71.754,52.55 
		67.033,53.531 62.115,56.864 56.639,52.55 59.59,45.489 67.033,42.155 73.131,47.646 71.754,52.55 	"/>
	<polyline points="26.935,68.828 
		22.41,67.063 16.737,67.063 14.377,60.199 20.836,56.08 28.902,57.649 30.672,65.69 26.935,68.828 	"/>
	<polyline points="48.967,53.334 
		48.376,58.237 50.148,63.729 44.442,67.848 38.542,62.944 37.754,54.903 44.639,50.785 48.967,53.334 	"/>
	<polyline points="35.59,78.994 
		39.721,76.281 42.672,71.378 49.557,73.144 49.754,80.76 44.048,86.644 36.376,83.898 35.59,78.994 	"/>
	<polyline points="26.344,27.478 
		24.967,26.889 23.197,26.889 22.41,24.732 24.574,23.359 27.131,23.947 27.721,26.497 26.344,27.478 	"/>
	<polyline points="33.033,33.754 
		31.656,32.969 29.885,32.969 29.098,31.008 31.262,29.635 33.819,30.027 34.411,32.577 33.033,33.754 	"/>
	<polyline points="33.229,23.947 
		31.853,23.359 29.885,23.359 29.294,21.202 31.262,19.829 33.819,20.417 34.411,22.967 33.229,23.947 	"/>
	<polyline points="55.066,27.085 
		53.688,26.497 51.918,26.497 51.131,24.339 53.295,22.967 55.852,23.555 56.443,26.104 55.066,27.085 	"/>
	<polyline points="76.672,32.969 
		75.688,34.146 75.098,35.912 72.736,35.912 72.148,33.558 73.328,31.204 75.885,31.4 76.672,32.969 	"/>
	<polyline points="8.278,34.342 
		8.082,35.912 8.672,37.677 6.705,38.821 4.934,37.284 4.738,34.734 7.099,33.361 8.278,34.342 	"/>
	<polyline points="42.279,43.332 
		43.655,44.116 45.425,44.116 46.017,46.273 44.048,47.45 41.493,46.862 40.902,44.313 42.279,43.332 	"/>
	<polyline points="51.523,42.547 
		52.9,43.332 54.672,43.332 55.459,45.489 53.295,46.862 50.737,46.078 50.148,43.528 51.523,42.547 	"/>
	<polyline points="11.229,52.55 
		9.852,51.961 7.885,51.961 7.295,49.805 9.262,48.431 11.82,49.02 12.41,51.569 11.229,52.55 	"/>
	<polyline points="27.918,43.332 
		29.294,44.116 31.066,44.116 31.853,46.273 29.689,47.646 27.131,47.058 26.541,44.313 27.918,43.332 	"/>
	<polyline points="61.164,37.84 
		62.115,36.696 62.705,34.931 64.869,34.931 65.654,37.284 64.279,39.605 61.721,39.409 61.164,37.84 	"/>
	<polyline points="58.213,61.768 
		56.836,61.18 54.867,61.18 54.279,59.022 56.246,57.649 58.803,58.237 59.393,60.787 58.213,61.768 	"/>
	<polyline points="73.721,55.688 
		73.523,57.257 74.115,59.022 72.344,60.395 70.377,58.826 70.18,56.276 72.344,54.903 73.721,55.688 	"/>
	<polyline points="38.343,68.24 
		36.77,68.828 35.59,70.201 33.426,69.221 34.017,66.867 36.18,65.299 38.343,66.672 38.343,68.24 	"/>
	<polyline points="8.869,55.884 
		9.065,57.453 10.049,59.022 8.475,60.787 6.312,59.61 5.524,57.257 7.295,55.295 8.869,55.884 	"/>
	<polyline points="23.197,75.497 
		22.607,73.928 21.229,72.751 22.213,70.594 24.574,71.182 26.147,73.144 24.77,75.497 23.197,75.497 	"/>
	<polyline points="56.049,68.24 
		56.049,69.809 56.836,71.574 55.262,72.947 53.098,71.771 52.508,69.221 54.672,67.652 56.049,68.24 	"/>
	<polyline points="39.917,90.762 
		38.542,91.547 37.557,93.116 35.393,92.527 35.393,90.174 37.164,88.213 39.721,89.193 39.917,90.762 	"/>
	<line x1="32.77" y1="90.83" x2="34.84" y2="94.91"/>
	<line x1="49.16" y1="90.83" x2="47.1" y2="94.91"/>
	<line x1="32.77" y1="90.83" x2="32.77" y2="97.95"/>
	<line x1="49.16" y1="90.83" x2="49.16" y2="97.99"/>
	<line x1="32.77" y1="90.83" x2="20.51" y2="99"/>
	<line x1="20.51" y1="90.83" x2="32.77" y2="99"/>
	<line x1="49.16" y1="90.83" x2="61.49" y2="99"/>
	<line x1="61.49" y1="90.83" x2="49.16" y2="99"/>
</g>
</svg>
