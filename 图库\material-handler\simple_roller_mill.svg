<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 100 86" xml:space="preserve" xmlns:agg="http://www.example.com">

	 	   <agg:params>
	 	   	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	 	   	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
	 	   </agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.color,.stroked,.dashed{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;

	}
	
	.stroked,.dashed {
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:4,4;
	}

      ]]>
</style>
	 
<polygon class="color" points="1,30.41 19.424,30.41 37.848,21.95 39.808,1 60.192,1 62.349,21.95 80.576,30.41 99,30.41 99,85 1,85 1,30.41 "/>

<g class="stroked">
	<line x1="37.848" y1="21.95" x2="62.349" y2="21.95"/>
	<polyline points="19.425,85 19.425,30.41 80.576,30.41 80.576,85"/>
	<circle cx="38.418" cy="57.705" r="11.583"/>
	<circle cx="61.583" cy="57.705" r="11.583"/>
</g>

</svg>
