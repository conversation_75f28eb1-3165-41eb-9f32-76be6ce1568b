<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="make2d" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 152 137" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>	 

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	 <linearGradient id="grad" gradientUnits="userSpaceOnUse">
		<stop  offset="0.0051" style="stop-color:#4C5B75"/>
		<stop  offset="0.1372" style="stop-color:#5D6B83"/>
		<stop  offset="0.3988" style="stop-color:#8A95A6"/>
		<stop  offset="0.4788" style="stop-color:#99A3B2"/>
		<stop  offset="0.503" style="stop-color:#AEB7C4"/>
		<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
	</linearGradient>
	<linearGradient id="grad1" xlink:href="#grad" x1="76" y1="89.3599" x2="76" y2="143.8805"/>
	<linearGradient id="grad2" xlink:href="#grad" x1="137" y1="112.8799" x2="161.2539" y2="112.8799"/>
	<linearGradient id="grad3" xlink:href="#grad" x1="-168.6667" y1="132.1899" x2="150" y2="132.1899"/>
	<linearGradient id="grad4" xlink:href="#grad" x1="-168.6667" y1="89.8848" x2="150" y2="89.8848"/>
	<linearGradient id="grad5" xlink:href="#grad" x1="137" y1="74.1201" x2="161.2539" y2="74.1201"/>
	<linearGradient id="grad6" xlink:href="#grad" x1="-168.6667" y1="54.0996" x2="150" y2="54.0996"/>
	<linearGradient id="grad7" xlink:href="#grad" x1="-168.6667" y1="17.125" x2="150" y2="17.125"/>
	<linearGradient id="grad8" xlink:href="#grad" x1="137" y1="6.9502" x2="161.2539" y2="6.9502"/>
	<linearGradient id="grad9" xlink:href="#grad" x1="137" y1="41.2197" x2="161.2539" y2="41.2197"/>
	<linearGradient id="grad10" xlink:href="#grad" x1="16.6667" y1="100.5601" x2="269.3414" y2="100.5601"/>
	<linearGradient id="grad11" xlink:href="#grad" x1="16.6667" y1="65.2949" x2="269.3414" y2="65.2949"/>
	<linearGradient id="grad12" xlink:href="#grad" x1="16.6667" y1="35.5352" x2="269.3414" y2="35.5352"/>
	<linearGradient id="grad13" xlink:href="#grad" x1="16.6667" y1="4.5947" x2="269.3414" y2="4.5947"/>
	<linearGradient id="grad14" xlink:href="#grad" x1="17.5" y1="6.9497" x2="-11.4871" y2="6.9497"/>
	<linearGradient id="grad15" xlink:href="#grad" x1="17.5" y1="41.2197" x2="-11.4871" y2="41.2197"/>
	<linearGradient id="grad16" xlink:href="#grad" x1="17.5" y1="74.1196" x2="-11.4871" y2="74.1196"/>
	<linearGradient id="grad17" xlink:href="#grad" x1="17.5" y1="112.8799" x2="-11.4871" y2="112.8799"/>
</defs>

<g>
	<path fill="url(#grad1)" d="M147.95,128.38c-47.97,0-95.93,0-143.9,0c3.31-8.2,6.75-16.41,10.2-24.61c41.17,0,82.33,0,123.5,0
		C141.2,111.97,144.64,120.18,147.95,128.38z"/>
	<path fill="url(#grad2)" d="M151,127.85c0,0.18,0,0.35,0,0.53c-1.02,0-2.03,0-3.05,0c-3.31-8.2-6.75-16.41-10.2-24.61
		l-0.01-6.39C142.22,107.54,146.69,117.69,151,127.85z"/>
	<rect x="1" y="128.38" fill="url(#grad3)"  />
	<rect x="1" y="85.92" fill="url(#grad4)"  />
	<path fill="url(#grad5)" d="M151,85.34c0,0.19,0,0.38,0,0.58c-1.32,0-2.63,0-3.95,0c-3.13-5.68-6.25-11.38-9.34-17.06
		l-0.01-6.54C142.07,69.99,146.52,77.67,151,85.34z"/>
	<polygon fill="url(#grad6)" points="151,50.01 151,58.19 150.95,58.19 1.05,58.19 1,58.19 1,50.01 1.14,50.01 150.86,50.01 
		151,50.01 	"/>
	<path fill="url(#grad7)" d="M151,12.9v8.45h-0.15c-4.4,0-145.3,0-149.7,0H1V12.9H151z"/>
	<path fill="url(#grad8)" d="M151,12.9c-2.88,0-5.77,0-8.65,0c-0.06-0.06-0.11-0.12-0.17-0.17c-1.62-1.63-3.13-3.14-4.53-4.54
		L137.64,1C142.07,4.96,146.52,8.93,151,12.9z"/>
	<path fill="url(#grad9)" d="M150.86,50.01c-1.9,0-3.81,0-5.71,0c-2.52-3.57-5.01-7.14-7.47-10.71l-0.01-6.87
		C141.99,38.29,146.39,44.15,150.86,50.01z"/>
	<path fill="url(#grad10)" d="M137.74,97.38l0.01,6.39c-41.17,0-82.33,0-123.5,0l0.01-6.39c0-0.01,0.01-0.02,0.01-0.03
		c41.15,0,82.31,0,123.46,0C137.73,97.36,137.74,97.37,137.74,97.38z"/>
	<path fill="url(#grad11)" d="M137.71,68.86c-0.09-0.17-0.18-0.33-0.27-0.5c-40.96,0-81.92,0-122.88,0
		c-0.09,0.17-0.18,0.33-0.27,0.5l0.01-6.54c0.11-0.2,0.22-0.39,0.33-0.59c40.91,0,81.83,0,122.74,0c0.11,0.2,0.22,0.39,0.33,0.59
		L137.71,68.86z"/>
	<path fill="url(#grad12)" d="M137.67,32.43l0.01,6.87c-0.17-0.24-0.33-0.48-0.5-0.72c-40.79,0-81.57,0-122.36,0
		c-0.17,0.24-0.33,0.48-0.5,0.72l0.01-6.87c0.17-0.22,0.33-0.44,0.49-0.66c40.79,0,81.57,0,122.36,0
		C137.34,31.99,137.5,32.21,137.67,32.43z"/>
	<path fill="url(#grad13)" d="M137.64,1l0.01,7.19c-0.07-0.07-0.13-0.13-0.19-0.19C96.49,8,55.51,8,14.54,8
		c-0.06,0.06-0.12,0.12-0.19,0.19L14.36,1C55.45,1,96.55,1,137.64,1z"/>
	<path fill="url(#grad14)" d="M14.36,1l-0.01,7.19c-1.4,1.4-2.91,2.91-4.53,4.54c-0.06,0.05-0.11,0.11-0.17,0.17
		c-2.88,0-5.77,0-8.65,0C5.48,8.93,9.93,4.96,14.36,1z"/>
	<path fill="url(#grad15)" d="M14.33,32.43l-0.01,6.87c-2.46,3.57-4.95,7.14-7.47,10.71c-1.9,0-3.81,0-5.71,0
		C5.61,44.15,10.01,38.29,14.33,32.43z"/>
	<path fill="url(#grad16)" d="M14.3,62.32l-0.01,6.54c-3.09,5.68-6.21,11.38-9.34,17.06c-1.32,0-2.63,0-3.95,0
		c0-0.2,0-0.39,0-0.58C5.48,77.67,9.93,69.99,14.3,62.32z"/>
	<path fill="url(#grad17)" d="M14.26,97.38l-0.01,6.39c-3.45,8.2-6.89,16.41-10.2,24.61c-1.02,0-2.03,0-3.05,0
		c0-0.18,0-0.35,0-0.53C5.31,117.69,9.78,107.54,14.26,97.38z"/>
</g>

<path class="color" d="M147.95,128.38c-47.97,0-95.93,0-143.9,0c3.31-8.2,6.75-16.41,10.2-24.61c41.17,0,82.33,0,123.5,0
	C141.2,111.97,144.641,120.18,147.95,128.38z M151,127.85c0,0.181,0,0.351,0,0.53c-1.02,0-2.029,0-3.05,0
	c-3.31-8.2-6.75-16.41-10.2-24.61l-0.01-6.39C142.221,107.54,146.689,117.689,151,127.85z M1,128.38h150V136H1V128.38z M1,85.92h150
	v7.93H1V85.92z M151,85.34c0,0.19,0,0.38,0,0.58c-1.32,0-2.63,0-3.95,0c-3.13-5.68-6.25-11.38-9.34-17.06l-0.01-6.54
	C142.07,69.99,146.52,77.67,151,85.34z M151,50.01v8.18h-0.05H1.05H1v-8.18h0.14h149.719H151z M151,12.9v8.45h-0.15
	c-4.399,0-145.3,0-149.7,0H1V12.9H151z M151,12.9c-2.88,0-5.77,0-8.65,0c-0.06-0.06-0.109-0.12-0.17-0.17
	c-1.619-1.63-3.13-3.14-4.529-4.54L137.641,1C142.07,4.96,146.52,8.93,151,12.9z M150.859,50.01c-1.899,0-3.81,0-5.709,0
	c-2.521-3.57-5.01-7.14-7.471-10.71l-0.01-6.87C141.99,38.29,146.391,44.15,150.859,50.01z M137.74,97.38l0.01,6.39
	c-41.17,0-82.33,0-123.5,0l0.01-6.39c0-0.01,0.01-0.021,0.01-0.03c41.15,0,82.31,0,123.46,0
	C137.73,97.359,137.74,97.37,137.74,97.38z M137.71,68.86c-0.09-0.17-0.181-0.33-0.271-0.5c-40.959,0-81.919,0-122.879,0
	c-0.09,0.17-0.18,0.33-0.27,0.5l0.01-6.54c0.11-0.2,0.22-0.39,0.33-0.59c40.91,0,81.83,0,122.74,0c0.11,0.2,0.22,0.39,0.33,0.59
	L137.71,68.86z M137.67,32.43l0.01,6.87c-0.17-0.24-0.33-0.48-0.5-0.72c-40.789,0-81.57,0-122.36,0c-0.17,0.24-0.33,0.48-0.5,0.72
	l0.01-6.87c0.17-0.22,0.33-0.44,0.49-0.66c40.79,0,81.571,0,122.36,0C137.34,31.99,137.5,32.21,137.67,32.43z M137.641,1l0.01,7.19
	c-0.07-0.07-0.131-0.13-0.19-0.19C96.49,8,55.51,8,14.54,8c-0.06,0.06-0.12,0.12-0.19,0.19L14.36,1C55.45,1,96.55,1,137.641,1z
	 M14.36,1l-0.01,7.19c-1.4,1.4-2.91,2.91-4.53,4.54c-0.06,0.05-0.11,0.11-0.17,0.17c-2.88,0-5.77,0-8.65,0
	C5.48,8.93,9.93,4.96,14.36,1z M14.33,32.43l-0.01,6.87c-2.46,3.57-4.95,7.14-7.47,10.71c-1.9,0-3.81,0-5.71,0
	C5.61,44.15,10.01,38.29,14.33,32.43z M14.3,62.32l-0.01,6.54c-3.09,5.68-6.21,11.38-9.34,17.06c-1.32,0-2.63,0-3.95,0
	c0-0.2,0-0.39,0-0.58C5.48,77.67,9.93,69.99,14.3,62.32z M14.26,97.38l-0.01,6.39c-3.45,8.2-6.89,16.41-10.2,24.61
	c-1.02,0-2.03,0-3.05,0c0-0.18,0-0.35,0-0.53C5.31,117.689,9.78,107.54,14.26,97.38z"/>

<g class="stroke" opacity="1">
	<line x1="22.538" y1="21.353" x2="22.538" y2="50.007"/>
	<line x1="22.538" y1="93.848" x2="22.538" y2="128.38"/>
	<line x1="22.538" y1="58.191" x2="22.538" y2="85.925"/>
	<line x1="43.922" y1="93.848" x2="43.922" y2="128.38"/>
	<line x1="43.922" y1="21.353" x2="43.922" y2="50.007"/>
	<line x1="43.922" y1="58.191" x2="43.922" y2="85.925"/>
	<line x1="65.308" y1="93.848" x2="65.308" y2="128.38"/>
	<line x1="65.308" y1="21.353" x2="65.308" y2="50.007"/>
	<line x1="65.308" y1="58.191" x2="65.308" y2="85.925"/>
	<line x1="86.692" y1="93.848" x2="86.692" y2="128.38"/>
	<line x1="86.692" y1="21.353" x2="86.692" y2="50.007"/>
	<line x1="86.692" y1="58.191" x2="86.692" y2="85.925"/>
	<line x1="108.078" y1="93.848" x2="108.078" y2="128.38"/>
	<line x1="108.078" y1="21.353" x2="108.078" y2="50.007"/>
	<line x1="108.078" y1="58.191" x2="108.078" y2="85.925"/>
	<line x1="129.462" y1="93.848" x2="129.462" y2="128.38"/>
	<line x1="129.462" y1="21.353" x2="129.462" y2="50.007"/>
	<line x1="129.462" y1="58.191" x2="129.462" y2="85.925"/>
</g>

<g class="stroke" opacity="0.5">
	<path d="M84.748,21.353c-0.005,3.473-0.008,6.946-0.008,10.42"/>
	<path d="M67.26,38.583c-0.001,3.808-0.005,7.617-0.009,11.425"/>
	<path d="M67.252,21.353c0.005,3.473,0.007,6.946,0.008,10.42"/>
	<path d="M49.78,38.583c-0.004,3.808-0.015,7.617-0.028,11.425"/>
	<path d="M49.755,21.353c0.016,3.473,0.024,6.946,0.026,10.42"/>
	<path d="M119.904,7.998c-0.026,1.632-0.05,3.265-0.073,4.898"/>
	<path d="M120.08,93.848c0.005,1.167,0.01,2.335,0.015,3.503"/>
	<path d="M84.781,7.998c-0.005,1.632-0.01,3.265-0.015,4.898"/>
	<path d="M84.816,93.848c0.001,1.167,0.002,2.335,0.003,3.503"/>
	<path d="M102.448,93.848c0.003,1.167,0.006,2.335,0.009,3.503"/>
	<path d="M102.342,7.998c-0.015,1.632-0.03,3.265-0.043,4.898"/>
	<path d="M49.552,93.848c-0.003,1.167-0.006,2.335-0.009,3.503"/>
	<path d="M67.184,93.848c-0.001,1.167-0.002,2.335-0.003,3.503"/>
	<path d="M49.658,7.998c0.016,1.632,0.03,3.265,0.043,4.898"/>
	<path d="M67.219,7.998c0.005,1.632,0.01,3.265,0.015,4.898"/>
	<path d="M84.74,38.583c0.002,3.808,0.005,7.617,0.01,11.425"/>
	<path d="M102.245,21.353c-0.016,3.473-0.024,6.946-0.026,10.42"/>
	<path d="M102.22,38.583c0.004,3.808,0.015,7.617,0.028,11.425"/>
	<path d="M119.742,21.353c-0.026,3.473-0.041,6.946-0.044,10.42"/>
	<path d="M119.7,38.583c0.008,3.808,0.024,7.617,0.047,11.425"/>
	<path d="M102.283,58.191c0.005,1.178,0.011,2.357,0.017,3.535"/>
	<path d="M102.333,68.356c0.03,5.856,0.062,11.713,0.086,17.568"/>
	<path d="M119.804,58.191c0.009,1.178,0.019,2.357,0.028,3.535"/>
	<path d="M119.889,68.356c0.051,5.856,0.103,11.713,0.143,17.568"/>
	<path d="M84.761,58.191c0.002,1.178,0.004,2.357,0.006,3.535"/>
	<path d="M84.778,68.356c0.01,5.856,0.021,11.713,0.029,17.568"/>
	<path d="M49.717,58.191c-0.005,1.178-0.011,2.357-0.017,3.535"/>
	<path d="M49.667,68.356c-0.03,5.856-0.062,11.713-0.086,17.568"/>
	<path d="M67.239,58.191c-0.002,1.178-0.004,2.357-0.006,3.535"/>
	<path d="M67.222,68.356c-0.01,5.856-0.021,11.713-0.029,17.568"/>
	<path d="M31.92,93.848c-0.005,1.167-0.01,2.335-0.015,3.503"/>
	<path d="M142.551,93.848c0.035,4.852,0.053,9.704,0.044,14.556"/>
	<path d="M32.195,58.191c-0.009,1.178-0.019,2.357-0.028,3.535"/>
	<path d="M32.111,68.356c-0.051,5.856-0.103,11.713-0.143,17.568"/>
	<path d="M32.096,7.998c0.026,1.632,0.05,3.265,0.073,4.898"/>
	<path d="M32.299,38.583c-0.007,3.808-0.024,7.617-0.047,11.425"/>
	<path d="M32.258,21.353c0.026,3.473,0.04,6.946,0.044,10.42"/>
	<path d="M140.04,93.848c0.021,2.945,0.035,5.89,0.042,8.835"/>
	<path d="M139.734,10.276c-0.019,0.873-0.037,1.746-0.055,2.619"/>
	<path d="M148.215,93.848c0.072,9.116,0.078,18.232-0.056,27.349"/>
	<path d="M12.266,10.276c0.019,0.873,0.037,1.746,0.055,2.619"/>
	<path d="M6.732,93.848c-0.052,6.903-0.068,13.807-0.017,20.71"/>
	<path d="M9.449,93.848c-0.036,4.852-0.053,9.704-0.044,14.556"/>
	<path d="M11.96,93.848c-0.021,2.945-0.035,5.89-0.042,8.835"/>
	<path d="M145.268,93.848c0.052,6.903,0.068,13.807,0.017,20.71"/>
	<path d="M3.785,93.848c-0.072,9.116-0.078,18.232,0.056,27.349"/>
	<path d="M4.235,58.191c-0.094,7.373-0.208,14.745-0.305,22.118"/>
	<path d="M6.846,82.476c-0.014,1.15-0.027,2.299-0.04,3.449"/>
	<path d="M7.165,58.191c-0.069,5.645-0.149,11.29-0.225,16.935"/>
	<path d="M9.619,77.421c-0.036,2.835-0.069,5.669-0.099,8.504"/>
	<path d="M9.864,58.191c-0.047,4.045-0.101,8.091-0.154,12.137"/>
	<path d="M12.182,72.728c-0.055,4.399-0.109,8.799-0.153,13.197"/>
	<path d="M12.36,58.191c-0.029,2.56-0.06,5.121-0.092,7.682"/>
	<path d="M4.338,21.353c0.1,8.137,0.09,16.275,0.028,24.413"/>
	<path d="M7.259,49.43c-0.001,0.193-0.003,0.385-0.005,0.578"/>
	<path d="M7.263,21.353c0.081,6.834,0.087,13.669,0.052,20.504"/>
	<path d="M9.986,45.543c-0.01,1.488-0.022,2.976-0.036,4.464"/>
	<path d="M9.958,21.353c0.064,5.629,0.08,11.259,0.064,16.89"/>
	<path d="M12.498,41.937c-0.013,2.69-0.032,5.381-0.056,8.071"/>
	<path d="M12.45,21.353c0.049,4.512,0.069,9.025,0.067,13.538"/>
	<path d="M147.765,58.191c0.094,7.373,0.208,14.745,0.305,22.118"/>
	<path d="M145.154,82.476c0.014,1.15,0.027,2.299,0.04,3.449"/>
	<path d="M144.835,58.191c0.069,5.645,0.15,11.29,0.225,16.935"/>
	<path d="M142.381,77.421c0.035,2.835,0.069,5.669,0.099,8.504"/>
	<path d="M142.136,58.191c0.047,4.045,0.101,8.091,0.154,12.137"/>
	<path d="M139.818,72.728c0.055,4.399,0.109,8.799,0.153,13.197"/>
	<path d="M139.64,58.191c0.029,2.56,0.06,5.121,0.092,7.682"/>
	<path d="M147.662,21.353c-0.1,8.137-0.091,16.275-0.028,24.413"/>
	<path d="M144.741,49.43c0.001,0.193,0.003,0.385,0.005,0.578"/>
	<path d="M144.737,21.353c-0.081,6.834-0.087,13.669-0.052,20.504"/>
	<path d="M142.014,45.543c0.01,1.488,0.022,2.976,0.036,4.464"/>
	<path d="M142.042,21.353c-0.064,5.629-0.08,11.259-0.064,16.89"/>
	<path d="M139.502,41.937c0.013,2.69,0.032,5.381,0.056,8.071"/>
	<path d="M139.55,21.353c-0.049,4.512-0.069,9.025-0.067,13.538"/>
</g>

<g class="stroke">
	<line x1="14.308" y1="50.007" x2="14.34" y2="21.353"/>
	<line x1="14.35" y1="12.896" x2="14.363" y2="1"/>
	<path d="M1,12.896C1,53.93,1,94.965,1,136c50,0,100,0,150,0c0-41.035,0-82.07,0-123.104"/>
	<line x1="137.65" y1="12.896" x2="137.637" y2="1"/>
	<line x1="137.692" y1="50.007" x2="137.66" y2="21.353"/>
	<line x1="137.733" y1="85.925" x2="137.702" y2="58.191"/>
	<line x1="14.247" y1="103.771" x2="14.258" y2="93.848"/>
	<line x1="14.267" y1="85.925" x2="14.298" y2="58.191"/>
	<line x1="137.753" y1="103.771" x2="137.742" y2="93.848"/>
	<path d="M1,12.896
		c50,0,100,0,150,0C146.523,8.93,142.072,4.965,137.637,1C96.546,1,55.454,1,14.363,1C9.928,4.965,5.477,8.93,1,12.896z"/>
	<path d="M147.954,128.38
		c-3.319-8.204-6.75-16.407-10.201-24.61c-41.168,0-82.337,0-123.506,0c-3.451,8.203-6.882,16.406-10.201,24.61"/>
	<path d="M147.052,85.925
		c-3.227-5.855-6.436-11.712-9.608-17.568c-40.963,0-81.926,0-122.889,0c-3.171,5.856-6.381,11.713-9.607,17.568"/>
	<path d="M145.147,50.007
		c-2.683-3.808-5.339-7.617-7.967-11.425c-40.787,0-81.574,0-122.361,0c-2.628,3.808-5.284,7.617-7.967,11.425"/>
	<path d="M1.152,21.353
		c49.898,0,99.797,0,149.695,0"/>
	<path d="M142.346,12.896
		c-1.759-1.761-3.376-3.385-4.881-4.898c-40.977,0-81.953,0-122.93,0c-1.505,1.513-3.123,3.137-4.881,4.898"/>
	<path d="M1.046,58.191
		c49.97,0,99.938,0,149.908,0"/>
	<path d="M151,127.849
		c-4.307-10.166-8.788-20.332-13.268-30.498c-41.155,0-82.309,0-123.464,0C9.788,107.517,5.307,117.683,1,127.849"/>
	<path d="M1,93.848
		c50,0,100,0,150,0"/>
	<path d="M151,85.336
		c-4.597-7.87-9.158-15.739-13.634-23.609c-40.911,0-81.821,0-122.731,0C10.158,69.597,5.597,77.466,1,85.336"/>
	<path d="M1,85.925
		c50,0,100,0,150,0"/>
	<path d="M1,128.38
		c50,0,100,0,150,0"/>
	<path d="M1.143,50.007
		c49.905,0,99.809,0,149.714,0c-4.636-6.078-9.197-12.156-13.68-18.234c-40.785,0-81.569,0-122.354,0
		C10.339,37.851,5.779,43.929,1.143,50.007z"/>
</g>
</svg>
