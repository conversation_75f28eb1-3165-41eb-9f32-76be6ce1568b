<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 152 117.451" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param name="state" description="State" type="state">
		<agg:state name="loaded" description="Loaded">
			<agg:param attributes="visibility" ids="cargo" value="visible"/>
		</agg:state>
		<agg:state name="empty" description="Empty">
			<agg:param attributes="visibility" ids="cargo" value="hidden"/>
		</agg:state>
	</agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill: #FFCC00;

	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>
	 
<path fill="#333333" id="cargo" d="M18.373,22.855C61.748-5.85,109.236-9.341,146.219,30.947C103.604,28.25,60.988,25.552,18.373,22.855L18.373,22.855z"/>
<polygon class="color" points="151,48.917 149.207,28.264 131.791,28.264
		123.382,21.365 78.468,21.365 72.713,19.875 65.2,19.875 64.998,21.365 8.47,21.365 8.47,33.033 15.05,38.144 8.47,38.144 
		8.47,40.528 12.696,40.528 12.696,61.48 1,61.48 1,97.976 6.677,97.976 8.101,94.697 103.277,94.697 141.101,49.793 145.28,50.706"/>
<polygon opacity="0.7" points="140.631,49.687 54.431,67.147 46.022,33.037 8.5,33.037 
		15.08,38.147 45.125,38.147 45.125,40.532 45.125,61.484 45.125,64.763 21.135,64.763 8.131,94.701 103.307,94.701 141.131,49.796 
			"/>
			
<g id="wheel">
	<polygon points="58.799,92.606 52.018,92.606 58.536,90.883 57.688,86.927 50.728,88.764 56.84,85.24 54.853,81.558 
		48.834,85.062 53.733,80.174 50.625,77.329 45.764,82.188 49.202,76.218 45.793,74.221 42.233,80.296 44.107,73.374 
		40.152,72.262 38.192,79.496 38.192,71.951 34.237,71.951 34.237,79.627 32.24,72.262 28.294,73.374 30.206,80.428 26.598,74.221 
		22.917,76.218 26.391,82.18 21.503,77.329 18.697,80.174 23.528,85.043 17.547,81.558 15.588,85.24 21.739,88.781 14.741,86.927 
		13.592,90.883 20.156,92.606 13.592,92.606 13.592,96.834 20.025,96.834 13.592,98.52 14.741,102.476 22.54,100.414 
		15.588,104.473 17.547,107.854 23.594,104.379 18.697,109.267 21.503,112.384 26.127,107.76 22.917,113.222 26.598,115.492 
		30.083,109.437 28.294,116.067 32.24,117.178 34.237,109.813 34.237,117.451 38.192,117.451 38.192,109.945 40.152,117.178 
		44.107,116.067 42.346,109.568 45.793,115.492 49.202,113.222 46.029,107.751 50.625,112.384 53.733,109.267 48.768,104.351 
		54.853,107.854 56.84,104.473 49.918,100.433 57.688,102.476 58.536,98.52 52.151,96.834 58.799,96.834 		"/>
	<circle fill="#333333" cx="36.195" cy="94.701" r="21.679"/>
	<circle opacity="0.3" fill="#FFFFFF" cx="36.195" cy="94.701" r="9.243"/>
	<circle class="color" cx="36.195" cy="94.701" r="7.282"/>
	<path d="M33.443,89.323l1.482,4.084l-0.023,0.022l-4.083-1.481L33.443,89.323z M41.574,97.454l-4.084-1.482l-0.022,0.022
		l1.481,4.084L41.574,97.454z M30.818,97.454l4.084-1.482l0.022,0.022l-1.481,4.084L30.818,97.454z M38.948,89.323l-1.481,4.084
		l0.022,0.022l4.084-1.481L38.948,89.323z"/>
</g>

<use xlink:href="#wheel" transform="translate(66 0)"/>

<polygon opacity="0.3" points="12.696,40.528 45.095,40.528 45.095,61.48 12.696,61.48 12.696,40.528 	"/>
<path fill="#333333" d="M26.782,44.702h4.226v10.178h-4.226V44.702L26.782,44.702z
	 M13.592,44.702v10.178h3.885l1.835-10.178H13.592L13.592,44.702z M21.105,44.702l-1.792,10.178h5.677V44.702H21.105L21.105,44.702z"/>
<g class="stroked" opacity="0.3">
	<line x1="61.303" y1="65.746" x2="50.873" y2="21.365"/>
	<line x1="53.268" y1="31.542" x2="75.427" y2="31.542"/>
	<line x1="55.94" y1="43.211" x2="110.979" y2="43.211"/>
	<line x1="58.267" y1="53.091" x2="123.78" y2="53.091"/>
	<polyline points="131.791,28.264 105.112,28.264 78.468,21.365 		"/>
	<polyline points="64.998,21.365 64.304,26.475 91.879,39.038 140.603,49.684 		"/>
</g>

</svg>
