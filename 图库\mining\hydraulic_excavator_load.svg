<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  x="0px" y="0px"
       viewBox="0 0 102 105.802" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param  type="C" name="loadColor" classes="load" description="Load Color" cssAttributes="fill"/>
	<agg:param type="C" name="strokeColor" description="Stroke Color" cssAttributes="stroke" classes="color,stroked"/>
	<agg:param name="state" description="State" type="state">
		<agg:state name="loaded" description="Loaded">
			<agg:param attributes="visibility" ids="load" value="visible"/>
		</agg:state>
		<agg:state name="empty" description="Empty">
			<agg:param attributes="visibility" ids="load" value="hidden"/>
		</agg:state>
	</agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.color,.stroked,.dashed{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
	}
	
	.color {
		fill:   #A1BFE2;
	}
	
	.load {
		fill:   #996633;
	}
	
	.stroked,.dashed{
		fill:none;
	}
	
	.dashed {
		stroke-dasharray:1.4173,1.4173;
	}

      ]]>
</style>

<g id="load">
  <polygon class="load" points="83.246,10.486 79.479,8.705 75.743,1 68.057,1 60.554,2.965 54.858,1 41.629,4.96 33.943,4.96 24.48,10.486 22.704,18.191
      15.018,19.971 11.282,27.677 7.515,29.457 5.555,44.684 13.058,46.648 32.167,46.648 41.629,48.429 54.858,46.648 64.321,46.648
      75.743,48.429 90.932,46.648 96.444,42.903 94.668,33.417 88.942,23.932 85.206,19.971 83.246,10.486 "/>
  <path opacity="0.3" d="M88.942,42.903l-13.199-3.96l9.462-3.745l1.776-3.776l1.96,3.776
      l-3.736,3.745L88.942,42.903L88.942,42.903z M64.321,6.741l7.472,5.71v5.74l1.99-5.74l3.736-3.746h-5.726L64.321,6.741L64.321,6.741
      z M24.48,18.191l1.991,1.78l7.472-3.745l7.686,3.745l7.503-5.74L47.141,4.96v7.491l-11.208,1.78l-1.99-3.745l-3.736,5.74
      L24.48,18.191L24.48,18.191z M51.092,18.191l5.726,5.741l-1.959,5.525l-7.717,1.965l-1.746-3.745l-7.717,1.78l-3.736-3.745
      l1.99,5.71l7.472,3.776h5.727l5.727,7.705h3.736v-7.705l3.736-3.776l11.453,1.996l7.472-5.741l-7.472,3.745l-5.727-1.965
      l-1.776-3.745l3.736-3.776l-5.696,1.996l-9.462-5.741v-5.74L51.092,18.191L51.092,18.191z M32.167,44.684l1.776-3.776l-3.736-5.71
      l-7.502-1.78l-3.951,5.525h-7.472v-5.525l-1.991,5.525l1.991,3.96l9.462-1.995l3.736-3.746l5.727,3.746L32.167,44.684L32.167,44.684
      z"/>
</g>
<path class="color" d="M96.787,19.151l-1.971,10.429l-2.209,4.227c0,0-6.874,5.243-16.615,8.418S42.59,46.248,42.59,46.248l-2.005-4.227l-2.175,2.215
	L15.611,38L1,25.354v18.882l25.009,22.87v18.848l14.577,2.215l4.213,4.022l-2.208,4.192l4.213,8.419l10.397-2.011l4.213-4.192
	l14.577-12.645c0,0,9.296-4.625,12.402-8.214s5.404-10.39,6.422-14.656s1.971-14.656,1.971-14.656L101,42.226V29.581L96.787,19.151z
	 M30.188,83.943c-1.107,0-2.005-0.938-2.005-2.096s0.897-2.096,2.005-2.096s2.005,0.938,2.005,2.096S31.295,83.943,30.188,83.943z
	 M48.91,100.61c-1.163,0-2.106-0.946-2.106-2.113s0.943-2.113,2.106-2.113c1.164,0,2.107,0.946,2.107,2.113
	S50.074,100.61,48.91,100.61z"/>
<g class="stroked">
	<path d="M42.59,50.439v-4.192c0,0-3.965-0.779-6.184,0s-6.375,4.368-6.375,4.368"/>
	<path d="M63.386,79.751c1.443-12.109,29.792-10.539,23.003-25.085c-6.419-13.754-33.712-0.844-43.799-4.227"/>
	<path d="M75.992,85.954c0,0-6.424-0.75-10.397-4.226S15.611,38,15.611,38s-4.995,0.616-8.427,2.011C3.789,41.39,1,44.236,1,44.236"/>
	<polyline points="96.787,48.429 92.607,48.429 92.607,33.807 	"/>
	<line x1="94.816" y1="29.581" x2="101" y2="29.581"/>
	<line x1="42.59" y1="50.439" x2="44.799" y2="63.535"/>
	<path d="M36.525,56.297c0,0,0.671-2.388,1.885-3.642s4.18-2.216,4.18-2.216"/>
	<line x1="26.009" y1="67.106" x2="61.415" y2="98.599"/>
</g>
</svg>
