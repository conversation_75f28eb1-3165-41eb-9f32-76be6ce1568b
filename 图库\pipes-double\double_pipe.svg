<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="0 0 20 20" enable-background="new 0 0 20 20" xml:space="preserve">
	 
<agg:params>
	<agg:param  type="C" name="color" classes="color" attribute="fill" description="Main Color"></agg:param>
	<agg:param  type="F" name="opacity" min="0" max="1" classes="color" attribute="opacity" description="Color Intesity"></agg:param>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:white;
		opacity:1;
	}

      ]]>
</style>

<g>
	<rect y="5" class="color"  />
	<line class="stroke" x1="0" y1="5" x2="20" y2="5"/>
	<line class="stroke" x1="20" y1="15" x2="0" y2="15"/>
</g>
</svg>
