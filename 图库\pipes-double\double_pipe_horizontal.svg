<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
     xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" 
      viewBox="1.416 0 41.156 30.186" enable-background="new 1.416 0 41.156 30.186"
     xml:space="preserve">

    <agg:params>
        <agg:param type="C" name="color" classes="color" description="Color" cssAttributes="fill"/>
        <agg:param type="C" name="strokeColor" classes="stroke" description="Stroke Color" cssAttributes="stroke"/>
        <agg:param type="F" name="strokeWidth" classes="stroke" description="Stroke Width" cssAttributes="stroke-width" min="0"/>
    </agg:params>

    <style type="text/css">
        <![CDATA[

	.stroke{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:white;
		opacity:1;
	}

      ]]>
    </style>

    <polyline class="color" points="1.416,1.416 42.572,1.416 42.572,28.771 1.416,28.771"/>
    <line class="stroke" x1="1.416" y1="1.416" x2="42.572" y2="1.416"/>
    <line class="stroke" x1="42.572" y1="28.771" x2="1.416" y2="28.771"/>

</svg>
