<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="0 0 20 20" enable-background="new 0 0 20 20" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:white;
		opacity:1;
	}

      ]]>
</style>
	 
<polygon class="color" points="20,5 15,5 15,0 5,0 5,20 15,20 15,15 20,15 "/>

<g class="stroke">
	<polyline points="15,0 15,5 20,5 	"/>
	<polyline points="20,15 15,15 15,20 	"/>
	<line x1="5" y1="20" x2="5" y2="0"/>
	<polyline points="15,5 10,10 15,15 	"/>
</g>
</svg>
