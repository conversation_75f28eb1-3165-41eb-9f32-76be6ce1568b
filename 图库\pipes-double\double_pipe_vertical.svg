<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
     xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" 
      viewBox="5.485 -5.485 30.186 41.156" enable-background="new 5.485 -5.485 30.186 41.156"
     xml:space="preserve"
        >

    <agg:params>
        <agg:param type="C" name="color" classes="color" description="Color" cssAttributes="fill"/>
        <agg:param type="C" name="strokeColor" classes="stroke" description="Stroke Color" cssAttributes="stroke"/>
        <agg:param type="F" name="strokeWidth" classes="stroke" description="Stroke Width" cssAttributes="stroke-width" min="0"/>
    </agg:params>

    <style type="text/css">
        <![CDATA[

	.stroke{
		stroke-width:1;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:white;
		opacity:1;
	}

      ]]>
    </style>

    <rect x="6.901" y="-5.485" class="color"  />
    <line class="stroke" x1="6.901" y1="35.671" x2="6.901" y2="-5.485"/>
    <line class="stroke" x1="34.256" y1="-5.485" x2="34.256" y2="35.671"/>
</svg>
