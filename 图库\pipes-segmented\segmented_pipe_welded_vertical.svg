<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px" 
	  viewBox="5.485 -6.901 30.186 43.988" enable-background="new 5.485 -6.901 30.186 43.988" xml:space="preserve"
	>

<agg:params>
	<agg:param name="color" description="Color" type="C" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>	

<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-2218.7515" y1="841.1399" x2="-2218.7515" y2="813.9788" gradientTransform="matrix(4.489659e-011 -1 1 4.489659e-011 -806.8293 -2203.6589)">
	<stop  offset="0" style="stop-color:#4D5C75"/>
	<stop  offset="0.0313" style="stop-color:#616F85"/>
	<stop  offset="0.1108" style="stop-color:#8E99A9"/>
	<stop  offset="0.1908" style="stop-color:#B4BCC7"/>
	<stop  offset="0.27" style="stop-color:#D1D7DE"/>
	<stop  offset="0.3485" style="stop-color:#E5EAEF"/>
	<stop  offset="0.4256" style="stop-color:#F2F6F9"/>
	<stop  offset="0.5" style="stop-color:#F6FAFC"/>
	<stop  offset="0.5744" style="stop-color:#F2F6F9"/>
	<stop  offset="0.6515" style="stop-color:#E5EAEF"/>
	<stop  offset="0.73" style="stop-color:#D1D7DE"/>
	<stop  offset="0.8092" style="stop-color:#B4BCC7"/>
	<stop  offset="0.8892" style="stop-color:#8E99A9"/>
	<stop  offset="0.9687" style="stop-color:#616F85"/>
	<stop  offset="1" style="stop-color:#4D5C75"/>
</linearGradient>
<rect x="6.901" y="-5.485" fill="url(#SVGID_1_)"  />
<rect x="6.901" y="-5.485" class="color"  />
<g class="stroke">
	<rect x="6.901" y="-5.485"  />
	<line x1="34.256" y1="15.094" x2="6.901" y2="15.094"/>
	<line x1="26.687" y1="35.671" x2="26.687" y2="15.094"/>
	<line x1="14.469" y1="15.094" x2="14.469" y2="-5.485"/>
</g>
</svg>
