<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" 
	  viewBox="0 0 40 40" xml:space="preserve">
	 
<agg:params>
	<agg:param name="color" description="Color" type="C" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="hor" gradientUnits="userSpaceOnUse" x1="0" y1="10" x2="0" y2="30">
		<stop  offset="0" style="stop-color:#4D5C75"/>
		<stop  offset="0.0313" style="stop-color:#616F85"/>
		<stop  offset="0.1108" style="stop-color:#8E99A9"/>
		<stop  offset="0.1908" style="stop-color:#B4BCC7"/>
		<stop  offset="0.27" style="stop-color:#D1D7DE"/>
		<stop  offset="0.3485" style="stop-color:#E5EAEF"/>
		<stop  offset="0.4256" style="stop-color:#F2F6F9"/>
		<stop  offset="0.5" style="stop-color:#F6FAFC"/>
		<stop  offset="0.5744" style="stop-color:#F2F6F9"/>
		<stop  offset="0.6515" style="stop-color:#E5EAEF"/>
		<stop  offset="0.73" style="stop-color:#D1D7DE"/>
		<stop  offset="0.8092" style="stop-color:#B4BCC7"/>
		<stop  offset="0.8892" style="stop-color:#8E99A9"/>
		<stop  offset="0.9687" style="stop-color:#616F85"/>
		<stop  offset="1" style="stop-color:#4D5C75"/>
	</linearGradient>

	<linearGradient id="vert" xlink:href="#hor" gradientUnits="userSpaceOnUse" x1="10" y1="0" x2="30" y2="0"></linearGradient>
</defs>

<rect x="10" y="0" fill="url(#vert)"  />

<polygon fill="url(#hor)" points="20,20 30,30 40,30 40,10 30,10"/>
<polygon fill="url(#hor)" points="20,20 10,30 00,30 00,10 10,10"/>

<polygon class="color" points="10,0 30,0 30,10 40,10 40,30 30,30 30,40 10,40 10,30 0,30 0,10 10,10"/>

<g class="stroke">
	<polyline points="10,0 10,10 0,10"/>
	<polyline points="30,0 30,10 40,10"/>
	<polyline  points="0,30 10,30 10,40"/>
	<polyline points="40,30 30,30 30,40"/>
</g>

</svg>
