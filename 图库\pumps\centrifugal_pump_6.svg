<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">

<svg version="1.1" id="Layer_01" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="-140.5 205 152 103" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>	 
	
<defs>
	<linearGradient id="case" gradientUnits="userSpaceOnUse" x1="-86.9644" y1="217.1494" x2="-86.9644" y2="255.7384">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.06" style="stop-color:#959595"/>
		<stop  offset="0.13" style="stop-color:#BABABA"/>
		<stop  offset="0.21" style="stop-color:#D7D7D7"/>
		<stop  offset="0.29" style="stop-color:#EBEBEB"/>
		<stop  offset="0.35" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.47" style="stop-color:#F8F8F8"/>
		<stop  offset="0.53" style="stop-color:#EBEBEB"/>
		<stop  offset="0.59" style="stop-color:#D7D7D7"/>
		<stop  offset="0.73" style="stop-color:#BABABA"/>
		<stop  offset="1" style="stop-color:#BFBFBF"/>
	</linearGradient>
	
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BABABA"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.4749" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BABABA"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="-29.7363" y1="217.1982" x2="-14.7744" y2="217.1982"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="1.0693" y1="239.6494" x2="1.0693" y2="251.1486"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="-19.5942" y1="228.1143" x2="-19.5942" y2="262.2305"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="-32.6294" y1="216.958" x2="-32.6294" y2="273.4844"/>
	
	<polygon id="boundary" points="-8.361,239.811 -11.966,228.482 
		-14.774,228.482 -14.774,206.088 -29.736,206.088 -29.736,228.482 -30.827,228.482 -30.827,221.006 -34.429,217.415 
		-34.429,217.413 -34.43,217.413 -34.432,217.412 -34.432,217.413 -139.5,217.413 -139.5,307 -34.429,307 -34.429,273.235 
		-30.827,269.686 -30.827,262.207 -11.966,262.207 -8.361,250.881 10.5,250.881 10.5,239.811 	"/>
</defs>
	
<polygon fill="url(#case)" points="-139.5,217.325 -34.429,217.325 -34.429,306.911 -139.5,306.911 -139.5,217.325 	"/>
		
<g id="nut" fill="#606060">
	<polygon points="-72.834,302.728 -73.767,300.91 -72.834,299.432 -71.054,299.432 -70.164,300.91 -71.054,302.728 -72.834,302.728"/>
	<polygon points="-42.837,302.728 -43.77,300.91 -42.837,299.432 -41.057,299.432 -40.167,300.91 -41.057,302.728 -42.837,302.728"/>
	<polygon points="-102.832,302.728 -103.765,300.91 -102.832,299.432 -101.051,299.432 -100.162,300.91 -101.051,302.728 -102.832,302.728"/>
	<polygon points="-132.829,302.728 -133.762,300.91 -132.829,299.432 -131.049,299.432 -130.159,300.91 -131.049,302.728 -132.829,302.728"/>
</g>
	
<g id="cyl">
	<polygon fill="url(#cyl_1)" points="-14.774,206 -14.774,228.396 -29.736,228.396 -29.736,206 -14.774,206"/>
	<rect x="-8.361" y="239.722" fill="url(#cyl_2)"  />
	<polygon fill="url(#cyl_3)" points="-8.361,239.722 -8.361,250.792 -11.966,262.119 -30.827,262.119 -30.827,228.395 -11.966,228.395 		"/>
	<polygon fill="url(#cyl_4)" points="-30.827,262.119 -30.827,269.597 -34.432,273.15 -34.432,217.324 -30.827,220.917 -30.827,228.395 		"/>
</g>

<use xlink:href="#boundary" class="color"/>

<g class="stroke">
	<use xlink:href="#boundary"/>
	<line x1="-11.966" y1="228.395" x2="-11.966" y2="262.119"/>
	<line x1="-34.429" y1="217.325" x2="-34.429" y2="273.15"/>
	<line x1="-30.827" y1="228.395" x2="-30.827" y2="262.119"/>
	<line x1="-29.736" y1="228.396" x2="-14.774" y2="228.396"/>
	<line x1="-8.361" y1="239.811" x2="-8.361" y2="250.881"/>
</g>

</svg>
