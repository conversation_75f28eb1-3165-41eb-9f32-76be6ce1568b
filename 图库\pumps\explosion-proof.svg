<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_01" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 112 152" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>	 	 
	 
<defs>
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BABABA"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.4749" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BABABA"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="29.4895" y1="97.8149" x2="96.0039" y2="97.8149"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="26.05" y1="106.7002" x2="99.48" y2="106.7002"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="32.53" y1="58.8047" x2="93" y2="58.8047"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="54.6799" y1="7.585" x2="70.8599" y2="7.585"/>
	<linearGradient id="cyl_5" xlink:href="#cyl" x1="56" y1="2.6348" x2="69.5298" y2="2.6348"/>
	<linearGradient id="cyl_6" xlink:href="#cyl" x1="36.3301" y1="20.3652" x2="89.2002" y2="20.3652"/>
	<linearGradient id="cyl_7" xlink:href="#cyl" x1="26.02" y1="88.9297" x2="99.4399" y2="88.9297"/>
	
	<linearGradient id="cyl_rib" xlink:href="#cyl" x1="41.9299" y1="62.5898" x2="45.0901" y2="62.5898"/>
	<linearGradient id="cyl_bolt" xlink:href="#cyl" x1="35.1953" y1="97.8149" x2="38.9893" y2="97.8149"/>
	
	<polygon id="rib" fill="url(#cyl_rib)" points="45.09,37.4 45.09,87.78 41.93,87.78 41.93,39.36 	"/>
	<rect id="bolt" x="35.2" y="90.08" fill="url(#cyl_bolt)"  />
	
	<linearGradient id="bodyGradient" gradientUnits="userSpaceOnUse" x1="55.9998" y1="107.5278" x2="55.9998" y2="133.1449">
		<stop  offset="0" style="stop-color:#767677"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BBBBBB"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F7F8F8"/>
		<stop  offset="0.42" style="stop-color:#FDFEFF"/>
		<stop  offset="0.4749" style="stop-color:#F7F8F8"/>
		<stop  offset="0.6606" style="stop-color:#EBEBEB"/>
		<stop  offset="0.7515" style="stop-color:#D7D7D7"/>
		<stop  offset="0.8727" style="stop-color:#BBBBBB"/>
		<stop  offset="1" style="stop-color:#8B8B8B"/>
	</linearGradient>
	
	<linearGradient id="legGradient" gradientUnits="userSpaceOnUse" x1="12.0164" y1="141.9351" x2="18.5026" y2="141.9351">
		<stop  offset="0" style="stop-color:#959595"/>
		<stop  offset="0.0837" style="stop-color:#9B9B9B"/>
		<stop  offset="0.1985" style="stop-color:#ADADAD"/>
		<stop  offset="0.3313" style="stop-color:#CACACA"/>
		<stop  offset="0.4766" style="stop-color:#F2F2F2"/>
		<stop  offset="0.497" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5587" style="stop-color:#E9E9E9"/>
		<stop  offset="0.754" style="stop-color:#C0C0C0"/>
		<stop  offset="0.9077" style="stop-color:#A6A6A6"/>
		<stop  offset="1" style="stop-color:#9D9D9D"/>
	</linearGradient>
	
	<linearGradient id="hookGradient" xlink:href="#legGradient" x1="42.877" y1="7.395" x2="52.347" y2="7.395"/>
	<linearGradient id="recessGradient" xlink:href="#legGradient" x1="58.9678" y1="24.1484" x2="66.5679" y2="24.1484"/>
	
	<path id="leg" fill="url(#legGradient)" d="M18.28,132.87c-0.53,6.05-1.07,12.09-1.61,18.13c-1.11,0-2.23,0-3.34,0
		c-0.42-6.04-0.84-12.08-1.26-18.13H18.28z"/>
		
	<path id="boundary" d="
		M42.814,10.899c0-0.811,0-1.628,0-2.438c0-2.527,2.059-4.573,4.594-4.573c2.529,0,4.582,2.052,4.582,4.573c0,0.811,0,1.627,0,2.438
		c0.896,0,1.792,0,2.688,0c0-2.21,0-4.415,0-6.625c0.438,0,0.884,0,1.322,0C56,3.185,56,2.089,56,1c4.512,0,9.017,0,13.528,0
		c0,1.089,0,2.185,0,3.274c0.438,0,0.883,0,1.328,0c0,2.21,0,4.415,0,6.625c4.849,0,9.697,0,14.545,0
		c1.265,1.26,2.53,2.521,3.793,3.787c0,5.048,0,10.096,0,15.143c1.271,1.26,2.536,2.527,3.8,3.787c0,18.057,0,36.107,0,54.163
		c2.161,0,4.321,0,6.481,0c0,0.767,0,1.533,0,2.299c-1.328,0-2.649,0-3.978,0c0,5.155,0,10.317,0,15.472c1.328,0,2.65,0,3.978,0
		c0,0.767,0,1.533,0,2.299c0.68,0,1.36,0,2.04,0c2.167,0,4.232,0.925,5.675,2.54c5.077,5.688,5.077,14.263,0,19.95
		c-1.442,1.615-3.508,2.534-5.675,2.534c-0.527,0-1.061,0-1.589,0c-0.419,6.042-0.838,12.084-1.251,18.126c-1.119,0-2.236,0-3.349,0
		c-0.534-6.042-1.068-12.084-1.602-18.126c-11.54,0-23.086,0-34.626,0c-0.534,6.042-1.067,12.084-1.602,18.126
		c-1.111,0-2.23,0-3.349,0c-0.413-6.042-0.832-12.084-1.251-18.126c-11.54,0-23.08,0-34.62,0c-0.534,6.042-1.068,12.084-1.608,18.126
		c-1.112,0-2.23,0-3.342,0c-0.419-6.042-0.839-12.084-1.258-18.126c-0.527,0-1.055,0-1.588,0c-2.167,0-4.232-0.918-5.675-2.534
		c-5.078-5.687-5.078-14.263,0-19.95c1.442-1.615,3.508-2.54,5.675-2.54c5.191,0,10.383,0,15.568,0c0-0.767,0-1.533,0-2.299
		c1.303,0,2.605,0,3.908,0c0-5.155,0-10.317,0-15.472c-1.303,0-2.605,0-3.908,0c0-0.766,0-1.533,0-2.299c2.16,0,4.321,0,6.481,0
		c0-18.056,0-36.106,0-54.163c1.265-1.26,2.536-2.527,3.8-3.787c0-5.047,0-10.095,0-15.143c1.264-1.267,2.529-2.527,3.8-3.787
		C41.029,10.899,41.918,10.899,42.814,10.899z M44.714,10.899
		c0.896,0,1.792,0,2.688,0s1.792,0,2.688,0c0-0.811,0-1.628,0-2.438c0-1.476-1.201-2.679-2.688-2.679
		c-1.48,0-2.688,1.204-2.688,2.679C44.714,9.271,44.714,10.088,44.714,10.899z"/>
</defs>
	 
<g id="cyl">
	<path fill="url(#cyl_1)" d="M95.5,90.08v15.47c-25.517,0-47.656,0-65.541,0c0-5.15,0-10.32,0-15.47
		C47.844,90.08,69.983,90.08,95.5,90.08z"/>
	<path fill="url(#cyl_2)" d="M99.48,105.55c0,0.77,0,1.53,0,2.3H26.05c0-0.77,0-1.53,0-2.3c0.17,0,1.59,0,3.91,0
		c1.43,0,3.2,0,5.24,0h3.79c2.75,0,5.8,0,9.04,0h3.8c2.94,0,5.98,0,9.04,0h3.8c3.06,0,6.09,0,9.03,0h3.8c3.24,0,6.29,0,9.04,0h3.8
		c2,0,3.74,0,5.16,0C97.86,105.55,99.3,105.55,99.48,105.55z"/>
	<path fill="url(#cyl_3)" d="M93,33.62c0,18.05,0,36.1,0,54.16H32.53V33.62c0,0,2.54-2.53,3.8-3.79H89.2
		C90.47,31.09,91.73,32.36,93,33.62z"/>
	<path fill="url(#cyl_4)" d="M70.86,4.27c0,2.21,0,4.42,0,6.63c-5.2,0-11.01,0-16.18,0c0-2.21,0-4.42,0-6.63
		c0.07,0,0.56,0,1.32,0c3.04,0,10.47,0,13.53,0C70.3,4.27,70.79,4.27,70.86,4.27z"/>
	<path fill="url(#cyl_5)" d="M69.53,1c0,1.09,0,2.19,0,3.27c-3.06,0-10.49,0-13.53,0c0-1.08,0-2.18,0-3.27
		C60.51,1,65.02,1,69.53,1z"/>
	<path fill="url(#cyl_6)" d="M54.68,10.9c5.17,0,10.98,0,16.18,0c6.98,0,12.86,0,14.54,0c1.27,1.26,2.53,2.52,3.8,3.79
		c0,5.04,0,10.09,0,15.14H36.33c0-5.05,0-10.1,0-15.14c1.27-1.27,2.53-2.53,3.8-3.79c0.13,0,1.1,0,2.68,0c0.56,0,1.2,0,1.9,0
		c1.52,0,3.35,0,5.38,0c0.62,0,1.25,0,1.9,0C52.86,10.9,53.76,10.9,54.68,10.9z"/>
	<path fill="url(#cyl_7)" d="M93,87.78c3.69,0,6.07,0,6.44,0c0,0.77,0,1.53,0,2.3c-0.18,0-1.6,0-3.94,0c-1.41,0-3.16,0-5.16,0
		h-3.8c-2.74,0-5.8,0-9.04,0h-3.8c-2.94,0-5.97,0-9.03,0h-3.8c-3.06,0-6.1,0-9.04,0h-3.8c-3.24,0-6.29,0-9.04,0H35.2
		c-2.04,0-3.81,0-5.24,0c-2.34,0-3.77,0-3.94,0c0-0.77,0-1.53,0-2.3c0.38,0,2.78,0,6.51,0h9.4h3.16h9.68h3.16h9.68h3.15h9.69h3.15
		H93z"/>
		
	<use xlink:href="#bolt"/>
	<use xlink:href="#bolt" transform="translate(12.83 0)"/>
	<use xlink:href="#bolt" transform="translate(25.668 0)"/>
	<use xlink:href="#bolt" transform="translate(38.504 0)"/>
	<use xlink:href="#bolt" transform="translate(51.34 0)"/>
	
	<use xlink:href="#rib"/>
	<use xlink:href="#rib" transform="translate(12.844 0)"/>
	<use xlink:href="#rib" transform="translate(25.68 0)"/>
	<use xlink:href="#rib" transform="translate(38.516 0)"/>
</g>


<path id="body" fill="url(#bodyGradient)" d="M101.52,107.85c2.16,0,4.23,0.93,5.67,2.54c5.08,5.69,5.08,14.26,0,19.95
	c-1.44,1.62-3.51,2.53-5.67,2.53c-0.53,0-90.5,0-91.04,0c-2.16,0-4.23-0.91-5.67-2.53c-5.08-5.69-5.08-14.26,0-19.95
	c1.44-1.61,3.51-2.54,5.67-2.54C15.67,107.85,100.84,107.85,101.52,107.85z"/>
	
<use xlink:href="#leg"/>
<use xlink:href="#leg" transform="translate(40.829 0)"/>
<use xlink:href="#leg" transform="translate(81.657 0)"/>

<path id="hook" fill="url(#hookGradient)" d="M47.41,3.89c2.53,0,4.58,2.05,4.58,4.57c0,0.81,0,1.63,0,2.44c-0.65,0-1.28,0-1.9,0
	c0-0.81,0-1.63,0-2.44c0-1.47-1.2-2.68-2.69-2.68c-1.48,0-2.69,1.21-2.69,2.68c0,0.81,0,1.63,0,2.44c-0.7,0-1.34,0-1.9,0
	c0-0.81,0-1.63,0-2.44C42.81,5.93,44.87,3.89,47.41,3.89z"/>

<path fill="url(#recessGradient)" d="M66.568,29.83c0-2.527,0-5.047,0-7.575c0-2.09-1.704-3.788-3.8-3.788
	c-2.103,0-3.8,1.698-3.8,3.788c0,2.527,0,5.048,0,7.575H66.568z"/>	
	
<use xlink:href="#boundary" class="color"/>
	
<g class="stroke">
	<use xlink:href="#boundary"/>
	<path d="M66.568,29.83c0-2.527,0-5.047,0-7.575c0-2.09-1.704-3.788-3.8-3.788c-2.103,0-3.8,1.698-3.8,3.788c0,2.527,0,5.048,0,7.575"/>
	<polyline points="83.598,87.78 83.598,37.404 80.446,39.361 80.446,87.78 	"/>
	<polyline points="70.762,87.78 70.762,37.404 67.61,39.361 67.61,87.78 	"/>
	<polyline points="57.925,87.78 57.925,37.404 54.774,39.361 54.774,87.78 	"/>
	<polyline points="45.09,87.78 45.09,37.404 41.931,39.361 41.931,87.78 	"/>
	<line x1="35.195" y1="90.079" x2="35.195" y2="105.551"/>
	<line x1="38.989" y1="90.079" x2="38.989" y2="105.551"/>
	<line x1="48.032" y1="90.079" x2="48.032" y2="105.551"/>
	<line x1="51.832" y1="90.079" x2="51.832" y2="105.551"/>
	<line x1="60.868" y1="90.079" x2="60.868" y2="105.551"/>
	<line x1="73.704" y1="90.079" x2="73.704" y2="105.551"/>
	<line x1="77.503" y1="90.079" x2="77.503" y2="105.551"/>
	<line x1="86.54" y1="90.079" x2="86.54" y2="105.551"/>
	<line x1="90.34" y1="90.079" x2="90.34" y2="105.551"/>
	<line x1="64.668" y1="90.079" x2="64.668" y2="105.551"/>
	<line x1="59.101" y1="132.874" x2="52.899" y2="132.874"/>
	<line x1="99.929" y1="132.874" x2="93.727" y2="132.874"/>
	<line x1="26.051" y1="107.851" x2="99.478" y2="107.851"/>
	<polyline points="95.5,105.551 90.34,105.551 86.54,105.551 77.503,105.551 73.704,105.551 64.668,105.551 60.868,105.551 51.832,105.551 
		48.032,105.551 38.989,105.551 35.195,105.551 29.959,105.551 	"/>
	<polyline points="29.959,90.079 35.195,90.079 38.989,90.079 48.032,90.079 51.832,90.079 60.868,90.079 64.668,90.079 73.704,90.079 77.503,90.079 
		86.54,90.079 90.34,90.079 95.5,90.079 	"/>
	<line x1="92.996" y1="87.78" x2="32.533" y2="87.78"/>
	<line x1="54.678" y1="10.899" x2="70.857" y2="10.899"/>
	<line x1="18.28" y1="132.874" x2="12.071" y2="132.874"/>
	<line x1="56" y1="4.274" x2="69.529" y2="4.274"/>
	<line x1="36.333" y1="14.687" x2="89.196" y2="14.687"/>
	<polyline points="36.333,29.83 58.968,29.83 66.568,29.83 89.196,29.83 	"/>
	<line x1="92.996" y1="33.617" x2="32.533" y2="33.617"/>
	<line x1="42.814" y1="10.899" x2="44.714" y2="10.899"/>
	<line x1="50.09" y1="10.899" x2="51.991" y2="10.899"/>
</g>


</svg>
