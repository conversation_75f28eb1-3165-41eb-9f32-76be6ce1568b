<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 202 145" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BABABA"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.4749" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BABABA"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="197.1943" y1="36.9941" x2="197.1943" y2="103.8831"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="189.1587" y1="54.6792" x2="189.1587" y2="85.7429"/>	
	<linearGradient id="cyl_3" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="182.2515" y1="50.7075" x2="182.2515" y2="90.1254"/>	
	<linearGradient id="cyl_4" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="120.6895" y1="4.0801" x2="183.4004" y2="4.0801"/>
	<linearGradient id="cyl_5" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="137.3496" y1="14.2632" x2="166.7793" y2="14.2632"/>
	<linearGradient id="cyl_6" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="155.314" y1="21.2437" x2="155.314" y2="119.2671"/>	
	<linearGradient id="cyl_7" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="127.5894" y1="17.792" x2="127.5893" y2="123.5244"/>	
	<linearGradient id="cyl_8" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="112.1553" y1="32.8921" x2="112.1553" y2="108.3984"/>
	<linearGradient id="cyl_9" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="91.731" y1="11.752" x2="91.7309" y2="129.1024"/>
	<linearGradient id="cyl_10" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="15.2456" y1="59.9487" x2="20.4819" y2="59.9487"/>
	<linearGradient id="cyl_11" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="10.9868" y1="45.7471" x2="24.7407" y2="45.7471"/>	
	<linearGradient id="cyl_12" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="18.5503" y1="63.0923" x2="18.5503" y2="111.4146"/>	
	<linearGradient id="cyl_13" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="55.9497" y1="22.5376" x2="55.9497" y2="117.4526"/>

	<linearGradient id="bolt_1" xlink:href="#cyl" gradientUnits="userSpaceOnUse" x1="141.3062" y1="29.8086" x2="141.3062" y2="38.175"/>
	
	<linearGradient id="flat" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.1931" style="stop-color:#868686"/>
		<stop  offset="0.5576" style="stop-color:#B2B2B2"/>
		<stop  offset="0.5636" style="stop-color:#C4C4C4"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
	
	<linearGradient id="nut_1" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="44.999" y1="125.6641" x2="41.3487" y2="119.2902"/>

	<linearGradient id="flat_1" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="128.0161" y1="109.4009" x2="157.609" y2="147.7172"/>
	<linearGradient id="flat_2" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="128.8159" y1="115.4834" x2="157.8133" y2="154.555"/>
	<linearGradient id="flat_3" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="138.0171" y1="101.043" x2="176.9626" y2="155.368"/>
	<linearGradient id="flat_4" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="147.397" y1="101.6934" x2="176.3958" y2="140.7669"/>
	<linearGradient id="flat_5" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="35.1499" y1="95.9888" x2="68.3747" y2="134.8228"/>
	<linearGradient id="flat_6" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="12.3975" y1="115.4536" x2="45.6207" y2="154.2859"/>
	
	<linearGradient id="button_1" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="7.0815" y1="89.9175" x2="24.5561" y2="106.7448"/>
	<linearGradient id="button_2" xlink:href="#flat" gradientUnits="userSpaceOnUse" x1="20.2402" y1="103.2852" x2="11.8262" y2="93.9003"/>
	
	<polygon id="font-nut" points="46.938,122.499 45.062,119.25 41.31,119.25 39.435,122.499 41.31,125.747 45.062,125.747 "/>
	<rect id="bolt" x="132.186" y="30.344"  />
	
	
</defs>	
	 
<g id="cylParts">
	<rect x="193.389" y="36.994" fill="url(#cyl_1)"  />
	<path fill="url(#cyl_2)" d="M193.389,54.992v30.772h-8.46c0.485-1.611,0.809-3.249,1.061-4.918
		c0.531-3.481,0.732-6.95,0.732-10.471c0-3.514-0.201-6.983-0.732-10.464c-0.251-1.67-0.575-3.307-1.061-4.918H193.389z"/>
	<path fill="url(#cyl_3)" d="M185.989,59.91c0.531,3.481,0.732,6.95,0.732,10.464c0,3.521-0.201,6.989-0.732,10.471
		c-0.251,1.67-0.575,3.307-1.061,4.918c-0.02,0.071-0.045,0.142-0.064,0.213c-0.35,1.1-0.719,2.149-1.425,3.1
		c-0.395,0.531-0.705,0.971-1.469,0.971h-4.188c0.519-6.549,0.732-13.092,0.732-19.673c0-6.575-0.214-13.124-0.732-19.667h4.188
		c0.764,0,1.074,0.44,1.469,0.964c0.706,0.958,1.075,2.006,1.425,3.106c0.019,0.071,0.044,0.143,0.064,0.214
		C185.414,56.603,185.738,58.24,185.989,59.91z"/>
		
	<polygon fill="url(#cyl_4)" points="183.4,1 183.4,7.161 120.689,7.161 120.689,1 	"/>
	
	<path fill="url(#cyl_6)" d="M177.782,50.708c0.519,6.543,0.732,13.092,0.732,19.667c0,6.581-0.214,13.124-0.732,19.673
		c-0.155,1.941-0.336,3.876-0.543,5.818c-0.453,4.239-1.042,8.439-1.954,12.607c-0.635,2.873-1.295,5.668-2.687,8.322
		c-0.498,0.952-1.384,2.595-2.796,2.595c-3.378,0-34.045,0-37.688,0V21.366c1.747,0,36.68,0,37.688,0
		c1.412,0,2.298,1.644,2.796,2.595c1.392,2.66,2.052,5.449,2.687,8.329c0.913,4.167,1.501,8.361,1.954,12.6
		C177.446,46.831,177.627,48.766,177.782,50.708z"/>
	<rect x="123.065" y="18.02" fill="url(#cyl_7)"  />
	<polygon fill="url(#cyl_8)" points="123.065,38.386 123.065,101.393 101.246,114.621 101.246,25.158 	"/>
	<rect x="82.217" y="11.891" fill="url(#cyl_9)"  />
	<path fill="url(#cyl_10)" d="M20.482,56.868v6.161c-1.748,0-3.488,0-5.236,0v-6.161H20.482z"/>
	<rect x="10.987" y="34.626" fill="url(#cyl_11)"  />
	<path fill="url(#cyl_12)" d="M34.585,107.437c0.453,1.346,0.926,2.66,1.515,3.915c-0.771,0-27.34,0-28.919,0
		c-0.945,0-1.385-0.518-1.896-1.165c-0.906-1.139-1.392-2.407-1.838-3.747c-0.699-2.064-1.139-4.167-1.488-6.31
		C1.259,95.827,1,91.542,1,87.194c0-4.349,0.259-8.64,0.958-12.937c0.35-2.148,0.79-4.252,1.488-6.316
		c0.447-1.34,0.932-2.608,1.838-3.747c0.511-0.647,0.952-1.165,1.896-1.165c2.686,0,19.495,0,22.589,0
		c-0.071,2.291-0.097,4.595-0.084,6.905c0.045,8.464,0.595,16.819,1.974,25.193C32.353,99.309,33.22,103.405,34.585,107.437z"/>
	<path fill="url(#cyl_13)" d="M36.1,111.352c-0.589-1.255-1.062-2.569-1.515-3.915c-1.366-4.031-2.233-8.128-2.925-12.309
		c-1.379-8.374-1.929-16.729-1.974-25.193c-0.013-2.311,0.013-4.615,0.084-6.905c0.194-6.128,0.693-12.218,1.638-18.314
		c0.647-4.181,1.476-8.283,2.796-12.322c0.861-2.627,1.773-5.112,3.515-7.352c0.854-1.094,2.071-2.297,3.657-2.291
		c13.612,0,27.23,0,40.842,0v94.27c-13.462,0-26.926,0-40.382,0c-1.599,0-2.809-1.177-3.669-2.271
		C37.317,113.668,36.657,112.536,36.1,111.352z"/>
		
	<path fill="url(#cyl_5)" d="M137.35,7.161v14.205c0,1.585,6.588,3.871,14.715,3.871s14.715-2.286,14.715-3.871V7.161
	H137.35z"/>
</g>

<g id="button">
	<rect x="7.473" y="89.993" fill="url(#button_1)"  />
	<circle fill="url(#button_2)" cx="15.674" cy="98.192" r="5.327"/>
</g>

<g id="bolts">
	<use xlink:href="#bolt" fill="url(#bolt_1)"/>
	<use xlink:href="#bolt" fill="url(#bolt_1)" transform="translate(0 34.566)"/>
	<use xlink:href="#bolt" fill="url(#bolt_1)" transform="translate(0 71.049)"/>
</g>

<g id="planar">
	<rect x="113.57" y="136.448" fill="url(#flat_1)"  />
	<path fill="url(#flat_2)" d="M154.909,119.389v17.059h-33.728l21.857-17.059C146.993,119.389,150.955,119.389,154.909,119.389z"
		/>
	<path fill="url(#flat_3)" d="M159.66,119.389v17.059h-4.751v-17.059C156.495,119.389,158.074,119.389,159.66,119.389z"/>
	<path fill="url(#flat_4)" d="M169.802,119.389l16.919,17.059H159.66v-17.059C163.039,119.389,166.424,119.389,169.802,119.389z"
		/>
	<path fill="url(#flat_5)" d="M82.217,117.021v11.352H33.783v-8.983v-8.038c0.77,0,1.546,0,2.317,0
		c0.557,1.185,1.217,2.317,2.065,3.398c0.861,1.094,2.07,2.271,3.669,2.271C55.291,117.021,68.754,117.021,82.217,117.021z"/>
	<path fill="url(#flat_6)" d="M21.42,136.448v-17.059h12.363v-8.038c-7.288,0-14.576,0-21.858,0v8.038h1.431v17.059h-1.431V144
		h36.104v-7.552H21.42z"/>
</g>

<use xlink:href="#font-nut" fill="url(#nut_1)"/>
<use xlink:href="#font-nut" fill="url(#nut_1)" transform="translate(12.5 0)"/>
<use xlink:href="#font-nut" fill="url(#nut_1)" transform="translate(25 0)"/>

<path id="boundary" class="color" d="M193.389,36.994v17.998h-8.46c-0.02-0.071-0.045-0.143-0.064-0.214
	c-0.35-1.1-0.719-2.148-1.425-3.106c-0.395-0.524-0.705-0.964-1.469-0.964h-4.188c-0.155-1.941-0.336-3.876-0.543-5.818
	c-0.453-4.239-1.042-8.433-1.954-12.6c-0.635-2.88-1.295-5.669-2.687-8.329c-0.498-0.952-1.384-2.595-2.796-2.595
	c-0.168,0-1.288,0-3.022,0V7.161H183.4V1h-62.711v6.161h16.66v14.205c-2.87,0-4.853,0-5.236,0V18.02h-9.048v20.366l-21.819-13.228
	V11.891H82.217V22.75c-13.612,0-27.23,0-40.842,0c-1.585-0.006-2.803,1.197-3.657,2.291c-1.741,2.239-2.654,4.724-3.515,7.352
	c-1.32,4.039-2.148,8.141-2.796,12.322c-0.945,6.096-1.443,12.186-1.638,18.314c-1.32,0-5.137,0-9.288,0v-6.161h4.259V34.626H10.987
	v22.242h4.259v6.161c-3.746,0-7.005,0-8.064,0c-0.945,0-1.385,0.518-1.896,1.165c-0.906,1.139-1.392,2.407-1.838,3.747
	c-0.699,2.064-1.139,4.167-1.488,6.316C1.259,78.554,1,82.845,1,87.194c0,4.349,0.259,8.633,0.958,12.937
	c0.35,2.142,0.79,4.246,1.488,6.31c0.447,1.34,0.932,2.608,1.838,3.747c0.511,0.647,0.952,1.165,1.896,1.165
	c0.378,0,2.198,0,4.744,0v8.038h1.431v17.059h-1.431V144h36.104v-7.552H21.42v-17.059h12.363v8.983h48.434h19.029v-13.752
	l21.819-13.228v20.857h9.048v-2.86c1.236,0,5.587,0,10.925,0l-21.857,17.059h-7.611V144h76.504v-7.552h-3.353l-16.919-17.059
	c1.412,0,2.298-1.643,2.796-2.595c1.392-2.653,2.052-5.449,2.687-8.322c0.913-4.168,1.501-8.368,1.954-12.607
	c0.208-1.941,0.388-3.876,0.543-5.818h4.188c0.764,0,1.074-0.44,1.469-0.971c0.706-0.951,1.075-2,1.425-3.1
	c0.019-0.071,0.044-0.142,0.064-0.213h8.46v17.997H201V36.994H193.389z"/>

<g id="lines" class="stroke">

	<use xlink:href="#font-nut"/>
	<use xlink:href="#font-nut" transform="translate(12.5 0)"/>
	<use xlink:href="#font-nut" transform="translate(25 0)"/>

	<path d="M82.217,22.75
		c-13.612,0-27.23,0-40.842,0c-1.585-0.006-2.803,1.197-3.657,2.291c-1.741,2.239-2.654,4.724-3.515,7.352
		c-1.32,4.039-2.148,8.141-2.796,12.322c-0.945,6.096-1.443,12.186-1.638,18.314c-0.071,2.291-0.097,4.595-0.084,6.905
		c0.045,8.464,0.595,16.819,1.974,25.193c0.692,4.181,1.56,8.278,2.925,12.309c0.453,1.346,0.926,2.66,1.515,3.915
		c0.557,1.185,1.217,2.317,2.065,3.398c0.861,1.094,2.07,2.271,3.669,2.271c13.456,0,26.919,0,40.382,0"/>
	<path d="M132.11,119.39h0.008c3.643,0,7.281,0,10.924,0c3.955,0,7.917,0,11.871,0c1.586,0,3.166,0,4.751,0c3.379,0,6.765,0,10.142,0
		c1.412,0,2.299-1.643,2.797-2.595c1.392-2.653,2.052-5.449,2.687-8.322c0.913-4.168,1.501-8.368,1.954-12.607
		c0.207-1.941,0.389-3.876,0.543-5.818c0.52-6.549,0.732-13.092,0.732-19.673c0-6.575-0.213-13.124-0.732-19.667
		c-0.154-1.941-0.336-3.876-0.543-5.818c-0.453-4.239-1.041-8.433-1.954-12.6c-0.635-2.88-1.295-5.669-2.687-8.329
		c-0.498-0.952-1.385-2.595-2.797-2.595c-1.008,0-2.012,0-3.021,0c0,1.585-6.588,3.871-14.715,3.871s-14.715-2.286-14.715-3.871
		c-1.749,0-3.49,0-5.236,0h-0.008"/>
	<path d="M36.139,111.352
		c-0.012,0-0.025,0-0.039,0c-0.771,0-1.547,0-2.317,0c-7.288,0-14.576,0-21.858,0c-1.579,0-3.165,0-4.744,0
		c-0.945,0-1.385-0.518-1.896-1.165c-0.906-1.139-1.392-2.407-1.838-3.747c-0.699-2.064-1.139-4.167-1.488-6.31
		C1.259,95.827,1,91.542,1,87.194c0-4.349,0.259-8.64,0.958-12.937c0.35-2.148,0.79-4.252,1.488-6.316
		c0.447-1.34,0.932-2.608,1.838-3.747c0.511-0.647,0.952-1.165,1.896-1.165c2.686,0,5.378,0,8.064,0c1.748,0,3.489,0,5.236,0
		c3.094,0,6.194,0,9.288,0c0.007,0,0.007,0,0.013,0"/>
	
	<line x1="13.356" y1="136.448" x2="21.42" y2="136.448"/>
	<line x1="13.356" y1="119.389" x2="21.42" y2="119.389"/>
	<line x1="101.246" y1="25.158" x2="123.065" y2="38.386"/>
	<line x1="101.246" y1="114.621" x2="123.065" y2="101.393"/>
	<polygon points="166.779,7.161 183.4,7.161 183.4,1 120.689,1 120.689,7.161 137.35,7.161 	"/>
	<line x1="137.35" y1="7.161" x2="137.35" y2="21.366"/>
	<line x1="166.779" y1="7.161" x2="166.779" y2="21.366"/>
	<polygon points="193.389,85.764 193.389,103.76 201,103.76 201,36.994 193.389,36.994 193.389,54.992 	"/>
	<path d="M181.971,90.048
		c0.764,0,1.074-0.44,1.469-0.971c0.706-0.951,1.075-2,1.425-3.1c0.019-0.071,0.044-0.142,0.064-0.213
		c0.485-1.611,0.809-3.249,1.061-4.918c0.531-3.481,0.732-6.95,0.732-10.471c0-3.514-0.201-6.983-0.732-10.464
		c-0.251-1.67-0.575-3.307-1.061-4.918c-0.02-0.071-0.045-0.143-0.064-0.214c-0.35-1.1-0.719-2.148-1.425-3.106
		c-0.395-0.524-0.705-0.964-1.469-0.964"/>
	<line x1="181.971" y1="50.708" x2="177.782" y2="50.708"/>
	<polygon points="82.217,128.373 91.731,128.373 101.246,128.373 101.246,114.621 101.246,25.158 101.246,11.891 91.731,11.891 82.217,11.891 
		82.217,22.75 82.217,117.021 	"/>
	<line x1="181.971" y1="90.048" x2="177.782" y2="90.048"/>
	<line x1="193.389" y1="54.992" x2="184.929" y2="54.992"/>
	<line x1="193.389" y1="85.764" x2="184.929" y2="85.764"/>
	<polygon points="121.181,136.448 113.57,136.448 113.57,144 190.074,144 190.074,136.448 186.721,136.448 159.66,136.448 154.909,136.448 	"/>
	
	<line x1="154.909" y1="119.389" x2="154.909" y2="136.448"/>
	<line x1="159.66" y1="119.389" x2="159.66" y2="136.448"/>
	<line x1="143.038" y1="119.389" x2="121.181" y2="136.448"/>
	<line x1="169.802" y1="119.389" x2="186.721" y2="136.448"/>
	<polygon points="20.482,56.868 24.741,56.868 24.741,34.626 10.987,34.626 10.987,56.868 15.246,56.868 	"/>
	<line x1="15.246" y1="56.868" x2="15.246" y2="63.029"/>
	<line x1="20.482" y1="56.868" x2="20.482" y2="63.029"/>
	<polyline points="11.925,111.352 11.925,119.389 13.356,119.389 13.356,136.448 11.925,136.448 11.925,144 48.029,144 48.029,136.448 21.42,136.448 
		21.42,119.389 33.783,119.389 	"/>
	<polyline points="33.783,111.352 33.783,119.389 33.783,128.373 82.217,128.373 	"/>
	<line x1="91.731" y1="11.891" x2="91.731" y2="128.373"/>
	<polygon points="123.065,101.393 123.065,122.25 132.113,122.25 132.113,119.389 132.113,21.366 132.113,18.02 123.065,18.02 123.065,38.386 	"/>
	<rect x="7.473" y="89.993"  />
	<circle cx="15.674" cy="98.192" r="5.327"/>
	
	<polyline points="132.186,30.344 150.427,30.344 150.427,37.935 132.186,37.935 	"/>
	<polyline points="132.186,64.91 150.427,64.91 150.427,72.5 132.186,72.5 	"/>
	<polyline points="132.186,101.393 150.427,101.393 150.427,108.984 132.186,108.984 	"/>
</g>

<g id="nuts" fill="#606060">
	<polyline points="123.065,27.108 119.298,27.108 119.298,34.699 123.065,34.699 	"/>
	<polyline points="123.065,64.91 119.298,64.91 119.298,72.5 123.065,72.5 	"/>
	<polyline points="123.065,105.188 119.298,105.188 119.298,112.779 123.065,112.779 	"/>
	
	<rect x="78.445" y="12.527"  />
	<rect x="78.445" y="25.229"  />
	<rect x="78.445" y="44.047"  />
	<rect x="78.445" y="66.094"  />
	<rect x="78.445" y="88.629"  />
	<rect x="78.445" y="107.557"  />
	<rect x="78.445" y="120.147"  />
</g>
</svg>
