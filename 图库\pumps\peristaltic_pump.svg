<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 111 102" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="inside" gradientUnits="userSpaceOnUse" x1="50.9583" y1="5.5435" x2="50.9582" y2="96.8193">
		<stop  offset="0" style="stop-color:#7D7D7D"/>
		<stop  offset="0.0308" style="stop-color:#8B8B8B"/>
		<stop  offset="0.1265" style="stop-color:#B0B0B0"/>
		<stop  offset="0.2222" style="stop-color:#CDCDCD"/>
		<stop  offset="0.3169" style="stop-color:#E1E1E1"/>
		<stop  offset="0.4101" style="stop-color:#EEEEEE"/>
		<stop  offset="0.5" style="stop-color:#F2F2F2"/>
		<stop  offset="0.6012" style="stop-color:#EEEEEE"/>
		<stop  offset="0.706" style="stop-color:#E1E1E1"/>
		<stop  offset="0.8125" style="stop-color:#CDCDCD"/>
		<stop  offset="0.9196" style="stop-color:#B0B0B0"/>
		<stop  offset="1" style="stop-color:#959595"/>
	</linearGradient>
	
	<linearGradient id="tube" gradientUnits="userSpaceOnUse" x1="50.9583" y1="5.5435" x2="50.9582" y2="96.8193">
		<stop  offset="0" style="stop-color:#959595"/>
		<stop  offset="0.5" style="stop-color:#F2F2F2"/>
		<stop  offset="1" style="stop-color:#959595"/>
	</linearGradient>

	<linearGradient id="tube_1" xlink:href="#tube" x1="73.667" y1="73.728" x2="73.667" y2="91.909"/>
	<linearGradient id="tube_2" xlink:href="#tube" x1="73.667" y1="28.273" x2="73.667" y2="10.091"/>
	
	<radialGradient id="tube_radial" cx="46.417" cy="51" r="40.909" gradientUnits="userSpaceOnUse">
		<stop  offset="0.56" style="stop-color:#959595"/>
		<stop  offset="0.78" style="stop-color:#F2F2F2"/>
		<stop  offset="1" style="stop-color:#959595"/>
	</radialGradient>
	
	<linearGradient id="motorGradient" gradientUnits="userSpaceOnUse" x1="22.947" y1="27.8496" x2="70.0901" y2="74.3512">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0748" style="stop-color:#7E7E7E"/>
		<stop  offset="0.1968" style="stop-color:#969696"/>
		<stop  offset="0.3497" style="stop-color:#BEBEBE"/>
		<stop  offset="0.5" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5584" style="stop-color:#DEDEDE"/>
		<stop  offset="0.7517" style="stop-color:#B6B6B6"/>
		<stop  offset="0.9056" style="stop-color:#9E9E9E"/>
		<stop  offset="1" style="stop-color:#959595"/>
	</linearGradient>
	
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BABABA"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.4749" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BABABA"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
		
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="105.4585" y1="64.3911" x2="105.4585" y2="101.3477"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="105.4585" y1="0.8652" x2="105.4585" y2="37.4534"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="32.53" y1="58.8047" x2="93" y2="58.8047"/>
</defs>

<path fill="url(#inside)" d="M46.417,5.543C21.33,5.543,1,25.896,1,50.998C1,76.1,21.33,96.452,46.417,96.452
	c18.166,0,36.333,0,54.5,0v-4.543V73.728V69.18h-9.083V32.816h9.083v-4.543V10.091V5.543C82.75,5.543,64.583,5.543,46.417,5.543z"/>
<path id="motor" fill="url(#motorGradient)" d="M70.811,61.556c2.616,3.961,2.086,9.226-1.274,12.585
	c-3.356,3.359-8.612,3.889-12.574,1.271c-6.184-4.091-12.373-8.186-18.561-12.277c-1.678-1.116-3-2.435-4.111-4.115
	c-4.087-6.193-8.18-12.387-12.267-18.576c-2.616-3.966-2.087-9.226,1.27-12.59c3.356-3.359,8.612-3.888,12.574-1.271
	c6.188,4.096,12.377,8.187,18.561,12.282c1.683,1.112,3,2.43,4.111,4.115C62.631,49.169,66.718,55.363,70.811,61.556z M55.5,50.998
	c0-5.019-4.068-9.091-9.083-9.091c-5.021,0-9.083,4.072-9.083,9.091c0,5.02,4.063,9.091,9.083,9.091
	C51.432,60.089,55.5,56.017,55.5,50.998z"/>
<polygon fill="url(#cyl_1)" points="110,64.636 110,101 100.917,101 100.917,96.452 100.917,91.909 100.917,73.728 
	100.917,69.18 100.917,64.636 	"/>
<polygon fill="url(#cyl_2)" points="110,1 110,37.364 100.917,37.364 100.917,32.816 100.917,28.273 100.917,10.091 
	100.917,5.543 100.917,1 	"/>

<g id="tube">
	<path fill="url(#tube_1)" d="M100.917,73.728v18.182c-18.167,0-36.334,0-54.5,0V73.728c0.658,0,1.312,0,1.971,0
		c1.89,0,3.433,0.462,5.011,1.506c0.855,0.563,1.707,1.131,2.563,1.694c4.434,2.931,10.276,2.541,14.282-0.958
		c1.654-1.449,3.774-2.243,5.972-2.243C84.448,73.728,92.685,73.728,100.917,73.728z"/>
	<path fill="url(#tube_2)" d="M100.917,10.091v18.182c-18.167,0-36.334,0-54.5,0V10.091
		C64.583,10.091,82.75,10.091,100.917,10.091z"/>
	<path fill="url(#tube_radial)" d="M46.417,73.728v18.182c-22.577,0-40.878-18.316-40.878-40.912c0-22.595,18.301-40.907,40.878-40.907
		v18.182c-0.453,0-0.899,0.01-1.352,0.038c-2.102,0.125-3.79-0.327-5.549-1.492c-0.879-0.582-1.765-1.169-2.649-1.751
		c-4.679-3.095-10.892-2.469-14.859,1.501c-3.967,3.975-4.597,10.188-1.5,14.875c0.582,0.88,1.164,1.766,1.75,2.652
		c1.159,1.756,1.615,3.446,1.49,5.549C22.97,62.707,33.342,73.728,46.417,73.728z"/>
</g>

<path class="color" d="M110,37.364V1h-9.083v4.543c-18.167,0-36.334,0-54.5,0C21.33,5.543,1,25.896,1,50.998
	C1,76.1,21.33,96.452,46.417,96.452c18.166,0,36.333,0,54.5,0V101H110V64.636h-9.083v4.543h-9.083V32.816h9.083v4.548H110z"/>

<g class="stroke">
	<polyline points="100.917,32.816 91.833,32.816 91.833,69.18 100.917,69.18 	"/>
	<path d="M100.917,10.091c-18.167,0-36.334,0-54.5,0c-22.577,0-40.878,18.312-40.878,40.907c0,22.595,18.301,40.912,40.878,40.912c18.166,0,36.333,0,54.5,0
		"/>
	<path d="M100.917,5.543c-18.167,0-36.334,0-54.5,0C21.33,5.543,1,25.896,1,50.998C1,76.1,21.33,96.452,46.417,96.452c18.166,0,36.333,0,54.5,0"/>
	<path d="M100.917,28.273
		c-18.167,0-36.334,0-54.5,0c-0.453,0-0.899,0.01-1.352,0.038c-2.102,0.125-3.79-0.327-5.549-1.492
		c-0.879-0.582-1.765-1.169-2.649-1.751c-4.679-3.095-10.892-2.469-14.859,1.501c-3.967,3.975-4.597,10.188-1.5,14.875
		c0.582,0.88,1.164,1.766,1.75,2.652c1.159,1.756,1.615,3.446,1.49,5.549c-0.779,13.061,9.593,24.082,22.668,24.082
		c0.658,0,1.312,0,1.971,0c1.89,0,3.433,0.462,5.011,1.506c0.855,0.563,1.707,1.131,2.563,1.694
		c4.434,2.931,10.276,2.541,14.282-0.958c1.654-1.449,3.774-2.243,5.972-2.243c8.232,0,16.469,0,24.702,0"/>
	<path d="M56.962,75.412
		c3.962,2.618,9.218,2.089,12.574-1.271c3.361-3.359,3.89-8.624,1.274-12.585c-4.092-6.193-8.179-12.387-12.271-18.576
		c-1.111-1.685-2.428-3.003-4.111-4.115c-6.184-4.095-12.373-8.186-18.561-12.282c-3.962-2.618-9.218-2.088-12.574,1.271
		c-3.356,3.364-3.885,8.624-1.27,12.59c4.087,6.189,8.179,12.383,12.267,18.576c1.111,1.68,2.433,2.999,4.111,4.115
		C44.589,67.226,50.778,71.321,56.962,75.412z"/>
	<path d="M46.417,41.907
		c-5.021,0-9.083,4.072-9.083,9.091c0,5.02,4.063,9.091,9.083,9.091c5.015,0,9.083-4.072,9.083-9.091
		C55.5,45.979,51.432,41.907,46.417,41.907z"/>
	<rect x="100.917" y="1"  />
	<rect x="100.917" y="64.636"  />
</g>
</svg>
