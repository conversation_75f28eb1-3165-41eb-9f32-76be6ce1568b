<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='138.735px'

    height='102.819px'

    viewBox='0 0 138.735 102.819'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='114.787,47.891 114.787,83.801 19.017,101.761 19.017,29.931 36.977,33.301 96.837,44.521  ' />

 </g>

 <g id='contours'

     class='contours'>

  <polygon points='114.791,47.889 114.791,83.806 19.014,101.764 19.014,29.931  ' />

  <polyline points='96.833,44.521 96.833,5.986 126.763,5.986  ' />

  <polyline points='36.972,33.298 36.972,5.986 1.056,5.986  ' />

 </g>

 <g id='fill'

     class='fill'>

  <polygon points='126.763,0 126.763,11.974 138.735,5.986  ' />

 </g>

</svg>