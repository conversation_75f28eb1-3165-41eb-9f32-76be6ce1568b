<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='79.931px'

    height='50px'

    viewBox='0 0 79.931 50'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M54.94,1.063c-9.988,0-19.956,0-29.944,0c-13.219,0-23.947,10.707-23.947,23.927   c0,13.24,10.729,23.947,23.947,23.947c9.988,0,19.956,0,29.944,0c13.22,0,23.927-10.707,23.927-23.947   C78.867,11.771,68.16,1.063,54.94,1.063z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M11.437,12.369c0.238,0.003,0.477,0.083,0.713,0.243c0.238,0.16,0.477,0.404,0.713,0.719   c0.24,0.318,0.479,0.712,0.714,1.167c0.24,0.465,0.479,0.992,0.714,1.57c0.239,0.591,0.477,1.233,0.712,1.914   c0.24,0.693,0.478,1.427,0.714,2.185c0.239,0.768,0.477,1.562,0.713,2.369c0.238,0.813,0.476,1.639,0.713,2.464   c0.238,0.825,0.476,1.651,0.714,2.464c0.236,0.807,0.474,1.601,0.714,2.369c0.234,0.759,0.473,1.49,0.713,2.184   c0.235,0.681,0.472,1.323,0.713,1.914c0.234,0.578,0.473,1.106,0.713,1.57c0.235,0.455,0.474,0.848,0.714,1.167   c0.236,0.315,0.474,0.558,0.712,0.72c0.237,0.159,0.477,0.238,0.715,0.242c0.238-0.004,0.476-0.083,0.713-0.242   c0.239-0.162,0.477-0.404,0.713-0.72c0.24-0.319,0.479-0.712,0.713-1.167c0.24-0.464,0.479-0.992,0.714-1.57   c0.24-0.591,0.478-1.233,0.714-1.914c0.239-0.693,0.477-1.425,0.713-2.184c0.238-0.769,0.476-1.563,0.713-2.369   c0.238-0.813,0.477-1.639,0.713-2.464c0.238-0.825,0.476-1.651,0.714-2.464c0.236-0.808,0.474-1.602,0.713-2.369   c0.235-0.758,0.474-1.491,0.714-2.185c0.234-0.681,0.473-1.323,0.712-1.914c0.236-0.578,0.474-1.105,0.714-1.57   c0.236-0.455,0.473-0.849,0.713-1.167c0.237-0.314,0.475-0.559,0.714-0.719c0.237-0.16,0.476-0.24,0.713-0.243   c0.237,0.003,0.477,0.083,0.714,0.243c0.238,0.16,0.477,0.404,0.713,0.719c0.24,0.318,0.478,0.712,0.714,1.167   c0.24,0.465,0.478,0.992,0.712,1.57c0.24,0.591,0.479,1.233,0.714,1.914c0.239,0.693,0.478,1.427,0.714,2.185   c0.239,0.768,0.477,1.562,0.713,2.369c0.237,0.813,0.476,1.639,0.713,2.464c0.238,0.825,0.475,1.651,0.713,2.464   c0.236,0.807,0.476,1.601,0.713,2.369c0.236,0.759,0.475,1.49,0.714,2.184c0.235,0.681,0.474,1.323,0.714,1.914   c0.234,0.578,0.473,1.106,0.713,1.57c0.235,0.455,0.474,0.848,0.713,1.167c0.236,0.315,0.475,0.558,0.713,0.72   c0.237,0.159,0.476,0.238,0.714,0.242c0.238-0.004,0.477-0.083,0.714-0.242c0.238-0.162,0.477-0.404,0.713-0.72   c0.239-0.319,0.479-0.712,0.713-1.167c0.24-0.464,0.479-0.992,0.714-1.57c0.24-0.591,0.478-1.233,0.713-1.914   c0.239-0.693,0.478-1.425,0.713-2.184c0.239-0.769,0.477-1.563,0.714-2.369c0.238-0.813,0.475-1.639,0.713-2.464   s0.475-1.651,0.713-2.464c0.237-0.808,0.475-1.602,0.713-2.369c0.236-0.758,0.475-1.491,0.714-2.185   c0.235-0.681,0.473-1.323,0.713-1.914c0.236-0.578,0.474-1.105,0.714-1.57c0.235-0.455,0.473-0.849,0.713-1.167   c0.236-0.314,0.474-0.559,0.713-0.719c0.237-0.16,0.475-0.24,0.713-0.243c0.238,0.003,0.477,0.083,0.714,0.243   c0.239,0.16,0.478,0.404,0.714,0.719c0.239,0.318,0.478,0.712,0.713,1.167c0.239,0.465,0.479,0.992,0.713,1.57   c0.24,0.591,0.478,1.233,0.714,1.914c0.239,0.693,0.478,1.427,0.712,2.185c0.24,0.768,0.479,1.562,0.714,2.369   c0.238,0.813,0.477,1.639,0.714,2.464s0.475,1.651,0.713,2.464c0.236,0.807,0.474,1.601,0.713,2.369   c0.236,0.759,0.473,1.49,0.714,2.184c0.235,0.681,0.474,1.323,0.713,1.914c0.235,0.579,0.474,1.106,0.713,1.57   c0.236,0.455,0.474,0.848,0.714,1.167c0.235,0.315,0.475,0.558,0.713,0.72c0.237,0.159,0.476,0.238,0.714,0.242' />

  <path d='M54.931,48.944c13.225,0,23.944-10.721,23.944-23.944c0-13.225-10.72-23.944-23.944-23.944   c-9.977,0-19.953,0-29.93,0C11.777,1.056,1.056,11.775,1.056,25c0,13.224,10.722,23.944,23.945,23.944   C34.978,48.944,44.954,48.944,54.931,48.944z' />

 </g>

</svg>