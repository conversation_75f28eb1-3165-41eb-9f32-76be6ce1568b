<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='62.02px'

    height='55.986px'

    viewBox='0 0 62.02 55.986'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M10.081,43.697L1.105,54.932H49l-8.995-11.234c0.084-0.063,0.189-0.147,0.273-0.231   c0.295-0.233,0.592-0.486,0.865-0.74c0.57-0.527,1.121-1.055,1.648-1.625C46.635,36.855,49,31.196,49,25.009   c0-1.161-0.084-2.302-0.253-3.422c-0.043-0.358-0.105-0.738-0.17-1.098c-0.084-0.401-0.168-0.781-0.273-1.183   c-0.021-0.105-0.043-0.19-0.063-0.295c4.244,0,8.489,0,12.733,0c0-5.977,0-11.974,0-17.95c-11.974,0-23.947,0-35.92,0   c-0.233,0-0.465,0-0.677,0.021c-0.231,0-0.464,0-0.675,0.021c-0.148,0-0.297,0-0.444,0.021C15.339,1.717,8.476,6.15,4.569,12.592   c-0.212,0.316-0.401,0.675-0.592,1.013c-0.168,0.339-0.338,0.677-0.507,1.015c-0.169,0.315-0.316,0.654-0.443,0.992   c-0.655,1.542-1.161,3.126-1.479,4.752c-0.063,0.337-0.126,0.676-0.168,0.992c-0.19,1.204-0.275,2.407-0.275,3.653   c0,7.031,3.02,13.347,7.856,17.717c0.273,0.254,0.57,0.507,0.865,0.74c0.295,0.254,0.592,0.485,0.908,0.718   c3.991,2.978,8.954,4.751,14.318,4.751c3.294,0,6.462-0.676,9.313-1.88c-7.266,3.147-16.071,2.809-23.631-2.871   c-0.127-0.063-0.232-0.17-0.359-0.254C10.271,43.846,10.165,43.761,10.081,43.697z' />

 </g>

 <g id='contours'

     class='contours'>

  <circle cx='25.047'

      cy='25'

      r='8.979' />

  <polyline points='10.09,43.697 1.104,54.931 48.992,54.931 40.006,43.697  ' />

  <circle cx='25.048'

      cy='25'

      r='23.945' />

  <path d='M48.231,19.015c5.706,22.097-19.805,38.891-37.848,24.915C-7.658,29.954,2.225,1.056,25.048,1.056   c11.972,0,23.944,0,35.916,0c0,5.986,0,11.973,0,17.959C56.72,19.015,52.475,19.015,48.231,19.015z' />

 </g>

</svg>