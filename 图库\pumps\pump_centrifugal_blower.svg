<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='80.498px'

    height='214.71px'

    viewBox='0 0 80.498 214.71'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M16.304,111.895c0,16.43,0,32.859,0,49.289c0,11.5,0,23.01,0,34.51c2.39,1.27,4.859,2.34,7.37,3.211   l-7.37,14.75h47.89l-7.37-14.75c2.521-0.871,4.98-1.941,7.37-3.211c0-11.5,0-23.01,0-34.51c0-16.43,0-32.859,0-49.289   c-7.479-3.99-15.72-5.99-23.939-5.99C32.014,105.904,23.794,107.904,16.304,111.895z' />

  <path d='M22.294,23.564c0,18.97,0,37.93,0,56.899c0,4.23,5.09,6.531,8.159,7.531c3.351,1.08,6.54,1.57,9.801,1.539   c2.18-0.02,4.38-0.279,6.68-0.74c3.439-0.689,8.16-2.189,10.36-5.5c0.619-0.949,0.859-1.889,0.939-2.83   c0.08-1.039-0.03-2.08-0.03-3.148c0-3.432,0-6.861,0-10.291c0-18.97,0-37.93,0-56.9c0-4.23-5.1-6.53-8.159-7.52   c-5.591-1.82-10.771-1.96-16.48-0.81c-3.58,0.72-9.71,2.55-11.06,6.97c-0.141,0.46-0.23,0.91-0.271,1.36   c-0.13,1.29,0.061,2.56,0.061,3.85C22.294,17.174,22.294,20.364,22.294,23.564z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M64.192,195.695c-14.965,7.98-32.923,7.98-47.888,0c0-27.936,0-55.871,0-83.805   c14.965-7.982,32.923-7.982,47.888,0C64.192,139.824,64.192,167.76,64.192,195.695z' />

  <circle cx='40.249'

      cy='177.736'

      r='17.959' />

  <polyline points='23.677,198.908 16.305,213.654 64.192,213.654 56.819,198.908  ' />

  <circle cx='40.249'

      cy='129.849'

      r='17.958' />

  <line x1='22.29'

      y1='80.463'

      x2='58.206'

      y2='80.463' />

  <path d='M58.206,10.126c0,18.966,0,37.932,0,56.897c0,3.43,0,6.857,0,10.285c0,2.064,0.4,4.004-0.913,5.982   c-2.2,3.314-6.92,4.816-10.36,5.506c-5.714,1.146-10.889,1.01-16.477-0.807c-3.068-0.998-8.166-3.293-8.166-7.527   c0-18.965,0-37.93,0-56.897c0-3.197,0-6.396,0-9.594c0-1.74-0.335-3.439,0.209-5.207c1.354-4.418,7.484-6.254,11.066-6.973   c5.713-1.146,10.887-1.011,16.476,0.807C53.108,3.597,58.206,5.892,58.206,10.126z' />

  <line x1='22.29'

      y1='10.126'

      x2='58.206'

      y2='10.126' />

  <line x1='64.192'

      y1='161.18'

      x2='79.442'

      y2='161.18' />

  <line x1='1.056'

      y1='170.125'

      x2='1.056'

      y2='152.232' />

  <line x1='79.442'

      y1='170.125'

      x2='79.442'

      y2='152.232' />

  <line x1='16.305'

      y1='161.18'

      x2='1.056'

      y2='161.18' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='40.249'

      y1='89.443'

      x2='40.249'

      y2='105.904' />

 </g>

</svg>