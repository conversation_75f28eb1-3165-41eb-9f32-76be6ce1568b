<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='64.791px'

    height='64.79px'

    viewBox='0 0 64.791 64.79'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <circle cx='32.396'

      cy='32.395'

      r='31.339' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M35.229,16.378c0,0.161-0.031,0.321-0.093,0.48c-0.062,0.161-0.155,0.321-0.274,0.48   c-0.122,0.161-0.272,0.321-0.445,0.48c-0.178,0.161-0.38,0.321-0.601,0.48c-0.225,0.161-0.47,0.321-0.729,0.479   c-0.267,0.163-0.545,0.321-0.835,0.482c-0.293,0.161-0.597,0.319-0.903,0.479c-0.31,0.161-0.627,0.321-0.94,0.48   c-0.315,0.159-0.632,0.319-0.94,0.48c-0.31,0.159-0.613,0.317-0.905,0.479c-0.291,0.161-0.569,0.319-0.836,0.482   c-0.26,0.157-0.503,0.317-0.729,0.479c-0.221,0.159-0.423,0.317-0.599,0.48c-0.175,0.159-0.325,0.319-0.447,0.48   c-0.119,0.159-0.212,0.317-0.274,0.479c-0.062,0.161-0.091,0.321-0.093,0.482c0.002,0.159,0.031,0.32,0.093,0.479   c0.063,0.161,0.155,0.322,0.274,0.48c0.122,0.163,0.272,0.322,0.447,0.48c0.176,0.161,0.378,0.322,0.599,0.48   c0.227,0.161,0.47,0.32,0.729,0.48c0.267,0.161,0.545,0.322,0.836,0.479c0.292,0.161,0.596,0.322,0.905,0.48   c0.309,0.161,0.625,0.32,0.94,0.48c0.313,0.161,0.631,0.32,0.94,0.48c0.307,0.159,0.61,0.32,0.903,0.48   c0.29,0.157,0.568,0.318,0.835,0.479c0.26,0.161,0.505,0.32,0.729,0.482c0.221,0.157,0.423,0.318,0.601,0.479   c0.173,0.159,0.323,0.318,0.445,0.48c0.119,0.159,0.213,0.32,0.274,0.48c0.062,0.159,0.093,0.32,0.093,0.479   c0,0.161-0.031,0.322-0.093,0.483c-0.062,0.16-0.155,0.319-0.274,0.478c-0.122,0.161-0.272,0.322-0.445,0.48   c-0.178,0.163-0.38,0.322-0.601,0.481c-0.225,0.16-0.47,0.321-0.729,0.478c-0.267,0.163-0.545,0.322-0.835,0.483   c-0.293,0.16-0.597,0.319-0.903,0.479c-0.31,0.16-0.627,0.321-0.94,0.48c-0.315,0.158-0.632,0.319-0.94,0.48   c-0.31,0.158-0.613,0.317-0.905,0.479c-0.291,0.16-0.569,0.319-0.836,0.482c-0.26,0.156-0.503,0.317-0.729,0.479   c-0.221,0.158-0.423,0.319-0.599,0.48c-0.175,0.158-0.325,0.319-0.447,0.48c-0.119,0.158-0.212,0.319-0.274,0.48   c-0.062,0.158-0.091,0.319-0.093,0.48c0.002,0.16,0.031,0.319,0.093,0.479c0.063,0.16,0.155,0.321,0.274,0.482   c0.122,0.161,0.272,0.319,0.447,0.479c0.176,0.16,0.378,0.321,0.599,0.48c0.227,0.163,0.47,0.321,0.729,0.48   c0.267,0.161,0.545,0.321,0.836,0.479c0.292,0.161,0.596,0.321,0.905,0.482c0.309,0.159,0.625,0.317,0.94,0.479   c0.313,0.161,0.631,0.319,0.94,0.48c0.307,0.159,0.61,0.319,0.903,0.48c0.29,0.159,0.568,0.317,0.835,0.479   c0.26,0.161,0.505,0.319,0.729,0.482c0.221,0.157,0.423,0.317,0.601,0.479c0.173,0.159,0.323,0.319,0.445,0.48   c0.119,0.159,0.213,0.319,0.274,0.48s0.093,0.319,0.093,0.48' />

  <circle cx='32.396'

      cy='32.395'

      r='31.339' />

  <polyline points='63.734,32.396 32.396,1.058 1.058,32.396  ' />

 </g>

</svg>