<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='176.547px'

    height='100.225px'

    viewBox='0 0 176.547 100.225'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M94.715,72.683c0.466,0.148,0.929,0.233,1.372,0.275c1.289,0.126,2.556-0.063,3.845-0.063   c3.188,0,6.397,0,9.587,0c18.964,0,37.928,0,56.912,0c4.223,0,6.525-5.111,7.518-8.174c1.815-5.596,1.942-10.769,0.802-16.471   c-0.676-3.442-2.196-8.172-5.511-10.369c-0.951-0.613-1.858-0.865-2.809-0.929c-1.035-0.084-2.091,0.021-3.169,0.021   c-3.421,0-6.841,0-10.284,0c-18.963,0-37.927,0-56.891,0c-4.244,0-6.524,5.089-7.538,8.172c-1.816,5.575-1.942,10.75-0.802,16.473   C88.464,65.187,90.302,71.333,94.715,72.683z' />

  <path d='M46.694,76.655c0-18.964,0-37.928,0-56.892c0-4.244-5.09-6.525-8.152-7.517   c-3.357-1.098-6.545-1.584-9.798-1.563c-2.176,0.021-4.393,0.274-6.695,0.739c-3.568,0.718-9.692,2.555-11.065,6.968   c-0.527,1.774-0.21,3.484-0.21,5.216c0,3.21,0,6.398,0,9.587c0,7.243,0,14.487,0,21.73c0,11.72,0,23.461,0,35.181   c0,4.224,5.109,6.526,8.172,7.518c5.597,1.817,10.771,1.965,16.472,0.803c3.443-0.675,8.172-2.175,10.369-5.49   c1.309-1.984,0.908-3.928,0.908-5.997C46.694,83.517,46.694,80.096,46.694,76.655z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='1.056'

      y1='60.917'

      x2='1.056'

      y2='48.945' />

  <line x1='166.421'

      y1='72.89'

      x2='166.421'

      y2='36.972' />

  <path d='M46.699,19.763c0,18.965,0,37.932,0,56.897c0,3.429,0,6.857,0,10.286c0,2.063,0.399,4.003-0.913,5.981   c-2.2,3.314-6.921,4.815-10.361,5.506c-5.714,1.146-10.888,1.012-16.477-0.808c-3.067-0.997-8.165-3.292-8.165-7.526   c0-18.966,0-37.932,0-56.897c0-3.198,0-6.396,0-9.595c0-1.739-0.335-3.438,0.208-5.207c1.355-4.416,7.484-6.254,11.066-6.971   c5.713-1.146,10.888-1.011,16.477,0.807C41.603,13.233,46.699,15.529,46.699,19.763z' />

  <path d='M96.085,36.972c18.966,0,37.932,0,56.897,0c3.429,0,6.856,0,10.285,0c2.062,0,4.003-0.398,5.98,0.915   c3.316,2.199,4.817,6.92,5.507,10.361c1.146,5.713,1.011,10.887-0.807,16.477c-0.999,3.067-3.293,8.166-7.527,8.166   c-18.966,0-37.932,0-56.897,0c-3.196,0-6.396,0-9.595,0c-1.738,0-3.438,0.335-5.207-0.209c-4.416-1.355-6.254-7.485-6.971-11.066   c-1.146-5.713-1.011-10.887,0.807-16.476C89.556,42.07,91.851,36.972,96.085,36.972z' />

  <line x1='10.783'

      y1='54.931'

      x2='1.056'

      y2='54.931' />

  <line x1='96.085'

      y1='72.89'

      x2='96.085'

      y2='36.972' />

  <line x1='28.741'

      y1='10.784'

      x2='28.741'

      y2='1.056' />

  <line x1='22.755'

      y1='1.056'

      x2='34.728'

      y2='1.056' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='87.104'

      y1='54.931'

      x2='46.699'

      y2='54.931' />

 </g>

</svg>