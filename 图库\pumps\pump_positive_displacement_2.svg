<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='85.916px'

    height='240.148px'

    viewBox='0 0 85.916 240.148'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M66.897,179.225c0,13.97,0,27.94,0,41.91c-2.391,1.27-4.85,2.34-7.37,3.21c-10.729,3.699-22.409,3.699-33.14,0   c-2.52-0.87-4.98-1.94-7.37-3.21c0-13.97,0-27.931,0-41.9s0-27.93,0-41.899c7.479-3.99,15.71-5.99,23.939-5.99   c8.23,0,16.46,2,23.94,5.99C66.897,151.294,66.897,165.265,66.897,179.225z M60.917,155.285c0-9.911-8.039-17.95-17.96-17.95   c-9.919,0-17.96,8.039-17.96,17.95c0,9.919,8.041,17.959,17.96,17.959C52.878,173.244,60.917,165.204,60.917,155.285z    M60.917,203.174c0-9.919-8.039-17.959-17.96-17.959c-9.919,0-17.96,8.04-17.96,17.959c0,9.921,8.041,17.961,17.96,17.961   C52.878,221.135,60.917,213.095,60.917,203.174z' />

  <path d='M59.527,224.345l7.37,14.75h-47.88l7.37-14.75C37.118,228.044,48.798,228.044,59.527,224.345z' />

  <path d='M60.917,77.305c0,1.08,0.11,2.119,0.021,3.16h-0.021h-35.92c0-18.971,0-37.93,0-56.9c0-3.199,0-6.399,0-9.59   c0-1.29-0.18-2.56-0.049-3.851h0.049h35.92c0,18.971,0,37.931,0,56.9C60.917,70.454,60.917,73.885,60.917,77.305z' />

  <path d='M42.957,89.535c-3.25,0.029-6.439-0.461-9.79-1.551c-3.069-0.99-8.17-3.29-8.17-7.52h35.92h0.021   c-0.069,0.95-0.31,1.88-0.94,2.829c-2.199,3.311-6.92,4.811-10.359,5.5C47.348,89.255,45.138,89.515,42.957,89.535z' />

  <path d='M60.917,10.124h-35.92h-0.049c0.039-0.45,0.119-0.899,0.259-1.359c1.36-4.42,7.48-6.25,11.07-6.97   c5.71-1.15,10.88-1.011,16.47,0.8C55.817,3.595,60.917,5.895,60.917,10.124z' />

  <path d='M42.957,137.335c9.921,0,17.96,8.039,17.96,17.95c0,9.919-8.039,17.959-17.96,17.959   c-9.919,0-17.96-8.04-17.96-17.959C24.997,145.374,33.038,137.335,42.957,137.335z' />

  <circle cx='42.957'

      cy='203.175'

      r='17.96' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='66.901'

      y1='179.229'

      x2='84.86'

      y2='179.229' />

  <line x1='84.86'

      y1='185.215'

      x2='84.86'

      y2='173.244' />

  <path d='M60.915,10.127c0,18.965,0,37.931,0,56.896c0,3.429,0,6.856,0,10.285c0,2.063,0.399,4.004-0.913,5.981   c-2.2,3.315-6.92,4.816-10.36,5.507c-5.714,1.146-10.889,1.01-16.477-0.808c-3.068-0.999-8.166-3.292-8.166-7.527   c0-18.965,0-37.931,0-56.896c0-3.197,0-6.396,0-9.594c0-1.74-0.335-3.439,0.208-5.207c1.356-4.418,7.485-6.254,11.066-6.973   C41.987,0.646,47.161,0.781,52.75,2.6C55.818,3.597,60.915,5.893,60.915,10.127z' />

  <line x1='24.999'

      y1='80.462'

      x2='60.915'

      y2='80.462' />

  <line x1='19.013'

      y1='179.232'

      x2='1.056'

      y2='179.232' />

  <line x1='24.999'

      y1='10.127'

      x2='60.915'

      y2='10.127' />

  <line x1='1.056'

      y1='185.219'

      x2='1.056'

      y2='173.246' />

  <polyline points='26.386,224.347 19.013,239.093 66.901,239.093 59.528,224.347  ' />

  <path d='M66.901,221.135c-14.965,7.981-32.924,7.981-47.889,0c0-27.935,0-55.87,0-83.806   c14.965-7.98,32.924-7.98,47.889,0C66.901,165.265,66.901,193.2,66.901,221.135z' />

  <circle cx='42.957'

      cy='155.288'

      r='17.958' />

  <circle cx='42.957'

      cy='203.177'

      r='17.958' />

  <line x1='42.957'

      y1='89.442'

      x2='42.957'

      y2='131.344' />

 </g>

</svg>