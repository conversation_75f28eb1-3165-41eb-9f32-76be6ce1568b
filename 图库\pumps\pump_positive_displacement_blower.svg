<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='114.803px'

    height='160.833px'

    viewBox='0 0 114.803 160.833'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M62.588,105.9c-14.58,0-23.88,11.791-23.99,23.949c-0.069,6.781,2.721,13.67,9.03,18.701l-8.99,11.23h47.891   l-8.99-11.23v-0.01c6.85-5.43,10.859-14.49,8.229-24.68c4.24,0,8.49,0,12.73,0c0-3,0-5.99,0-8.98s0-5.98,0-8.98   C86.528,105.9,74.558,105.9,62.588,105.9z' />

  <path d='M44.628,23.571c0,18.96,0,37.929,0,56.89c0,4.24,5.09,6.529,8.16,7.529c3.35,1.09,6.55,1.57,9.8,1.541   c2.18-0.021,4.38-0.271,6.68-0.73c3.44-0.689,8.16-2.201,10.36-5.51c0.62-0.951,0.86-1.891,0.94-2.83   c0.079-1.041-0.03-2.08-0.03-3.15c0-3.43,0-6.859,0-10.291c0-18.959,0-37.93,0-56.889c0-4.24-5.09-6.529-8.16-7.529   c-5.59-1.82-10.77-1.951-16.48-0.811c-3.579,0.72-9.71,2.561-11.069,6.97c-0.141,0.46-0.22,0.91-0.271,1.36   c-0.12,1.29,0.07,2.561,0.07,3.85C44.628,17.171,44.628,20.371,44.628,23.571z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='44.624'

      y1='10.127'

      x2='80.541'

      y2='10.127' />

  <path d='M80.541,10.127c0,18.965,0,37.93,0,56.896c0,3.43,0,6.857,0,10.285c0,2.063,0.397,4.004-0.915,5.98   c-2.199,3.316-6.92,4.816-10.359,5.508c-5.713,1.145-10.888,1.01-16.478-0.809c-3.067-0.996-8.165-3.293-8.165-7.525   c0-18.967,0-37.932,0-56.896c0-3.199,0-6.396,0-9.596c0-1.738-0.336-3.438,0.208-5.207c1.356-4.417,7.485-6.254,11.065-6.971   C61.611,0.646,66.786,0.781,72.374,2.6C75.443,3.598,80.541,5.893,80.541,10.127z' />

  <line x1='98.499'

      y1='114.883'

      x2='113.747'

      y2='114.883' />

  <line x1='1.056'

      y1='138.793'

      x2='1.056'

      y2='120.902' />

  <line x1='44.624'

      y1='80.463'

      x2='80.541'

      y2='80.463' />

  <line x1='14.608'

      y1='129.848'

      x2='1.056'

      y2='129.848' />

  <line x1='113.747'

      y1='105.938'

      x2='113.747'

      y2='123.828' />

  <path d='M85.767,123.861c5.705,22.098-19.804,38.891-37.848,24.916c-18.043-13.977-8.159-42.873,14.664-42.873   c11.971,0,23.943,0,35.916,0c0,5.984,0,11.971,0,17.957C94.255,123.861,90.011,123.861,85.767,123.861z' />

  <polyline points='47.624,148.545 38.638,159.777 86.526,159.777 77.54,148.545  ' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='62.583'

      y1='89.441'

      x2='62.583'

      y2='105.904' />

  <line x1='62.583'

      y1='129.848'

      x2='14.608'

      y2='129.848' />

 </g>

</svg>