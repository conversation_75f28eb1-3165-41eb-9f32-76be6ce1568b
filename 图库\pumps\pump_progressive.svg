<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='285.044px'

    height='44.116px'

    viewBox='0 0 285.044 44.116'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='266.027,13.038 134.34,13.038 116.368,13.038 116.368,36.964 255.047,36.964 283.978,36.964    283.978,13.038  ' />

  <path d='M83.298,7.948c-0.95-0.612-1.88-0.866-2.83-0.929C79.434,6.934,78.377,7.04,77.3,7.04   c-3.42,0-6.841,0-10.283,0c-18.964,0-37.927,0-56.892,0c-4.244,0-6.525,5.089-7.517,8.172C0.771,20.788,0.644,25.96,1.784,31.685   c0.718,3.589,2.555,9.713,6.969,11.064c0.465,0.147,0.929,0.232,1.372,0.275c1.289,0.126,2.556-0.063,3.844-0.063   c3.211,0,6.398,0,9.588,0c18.963,0,37.947,0,56.911,0c4.224,0,6.526-5.09,7.518-8.173c1.817-5.575,1.965-10.749,0.803-16.471   C88.113,14.874,86.613,10.165,83.298,7.948z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='266.029'

      y1='1.056'

      x2='266.029'

      y2='13.027' />

  <line x1='10.125'

      y1='42.958'

      x2='10.125'

      y2='7.042' />

  <path d='M10.125,7.042c18.966,0,37.932,0,56.898,0c3.427,0,6.856,0,10.285,0c2.063,0,4.002-0.399,5.981,0.914   c3.313,2.2,4.815,6.92,5.507,10.361c1.145,5.713,1.01,10.888-0.809,16.477c-0.998,3.068-3.293,8.166-7.527,8.166   c-18.965,0-37.931,0-56.896,0c-3.198,0-6.396,0-9.594,0c-1.74,0-3.439,0.335-5.207-0.207c-4.417-1.356-6.254-7.486-6.973-11.067   C0.646,25.97,0.781,20.797,2.6,15.207C3.596,12.139,5.891,7.042,10.125,7.042z' />

  <rect x='116.378'

      y='13.027'

      fill='none'

      width='167.61'

      height='23.945' />

  <line x1='80.461'

      y1='42.958'

      x2='80.461'

      y2='7.042' />

  <line x1='134.336'

      y1='1.056'

      x2='134.336'

      y2='13.027' />

  <line x1='128.351'

      y1='1.056'

      x2='140.322'

      y2='1.056' />

  <line x1='260.044'

      y1='1.056'

      x2='272.016'

      y2='1.056' />

  <path d='M135.072,18.111c3.732,0.019,6.572,3.683,8.77,6.214c2.297,2.647,4.828,6.265,8.374,7.329   c3.768,1.13,6.908-2.162,9.161-4.656c2.462-2.726,4.729-6.083,7.984-7.964c3.659-2.113,6.953,0.065,9.551,2.72   c2.586,2.644,4.718,5.756,7.597,8.117c3.271,2.68,6.613,2.4,9.745-0.332c2.78-2.426,4.884-5.492,7.494-8.081   c2.259-2.239,5.201-4.281,8.479-2.824c3.02,1.338,5.218,4.282,7.306,6.702c2.039,2.365,4.356,5.375,7.499,6.317   c3.471,1.041,6.42-1.714,8.574-4.018c3.685-3.938,8.807-12.468,15.197-8.365c2.801,1.8,4.847,4.622,7.015,7.073   c2.063,2.34,4.73,5.536,8.182,5.544' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='89.441'

      y1='25'

      x2='116.378'

      y2='25' />

 </g>

</svg>