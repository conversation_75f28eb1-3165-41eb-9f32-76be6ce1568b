<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='285.045px'

    height='49.999px'

    viewBox='0 0 285.045 49.999'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='283.991,13.027 283.991,36.968 248.071,36.968 248.071,24.997 248.071,13.027 266.031,13.027  ' />

  <polygon points='248.071,24.997 248.071,36.968 134.341,36.968 116.382,36.968 116.382,24.997  ' />

  <rect x='116.382'

      y='13.027'

      fill='none'

      width='131.689'

      height='11.97' />

  <path d='M89.531,24.997c0.03,3.25-0.45,6.45-1.54,9.8c-1,3.06-3.29,8.16-7.53,8.16V7.037v-0.01   c0.95,0.07,1.89,0.31,2.83,0.93c3.31,2.2,4.82,6.92,5.51,10.36C89.261,20.617,89.511,22.817,89.531,24.997z' />

  <path d='M80.461,7.037v35.92c-18.96,0-37.93,0-56.89,0c-3.2,0-6.4,0-9.601,0c-1.29,0-2.55,0.19-3.839,0.061v-0.061   V7.037c18.959,0,37.93,0,56.89,0c3.43,0,6.86,0,10.29,0c1.08,0,2.119-0.11,3.149-0.01V7.037z' />

  <path d='M10.132,42.957v0.061c-0.451-0.041-0.91-0.12-1.371-0.271c-4.41-1.35-6.25-7.479-6.97-11.061   c-1.149-5.719-1.01-10.89,0.81-16.479c1-3.07,3.291-8.17,7.531-8.17V42.957z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='80.463'

      y1='42.958'

      x2='80.463'

      y2='7.042' />

  <line x1='89.442'

      y1='25'

      x2='116.38'

      y2='25' />

  <line x1='134.338'

      y1='48.943'

      x2='134.338'

      y2='36.972' />

  <line x1='260.046'

      y1='1.056'

      x2='272.018'

      y2='1.056' />

  <line x1='266.031'

      y1='1.056'

      x2='266.031'

      y2='13.027' />

  <line x1='140.323'

      y1='48.943'

      x2='128.352'

      y2='48.943' />

  <rect x='116.38'

      y='13.027'

      fill='none'

      width='167.609'

      height='23.944' />

  <line x1='116.38'

      y1='25'

      x2='248.073'

      y2='25' />

  <line x1='248.073'

      y1='13.027'

      x2='248.073'

      y2='36.972' />

  <line x1='10.126'

      y1='42.958'

      x2='10.126'

      y2='7.042' />

  <path d='M10.126,7.042c18.967,0,37.932,0,56.898,0c3.428,0,6.857,0,10.284,0c2.063,0,4.004-0.399,5.983,0.914   c3.313,2.199,4.814,6.92,5.505,10.36c1.146,5.713,1.012,10.887-0.808,16.477c-0.997,3.067-3.293,8.165-7.526,8.165   c-18.966,0-37.931,0-56.896,0c-3.199,0-6.396,0-9.595,0c-1.74,0-3.439,0.335-5.207-0.208c-4.418-1.355-6.255-7.485-6.972-11.066   C0.646,25.97,0.782,20.796,2.6,15.207C3.597,12.139,5.893,7.042,10.126,7.042z' />

 </g>

</svg>