<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='122.402px'

    height='61.973px'

    viewBox='0 0 122.402 61.973'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='58.206,1.054 16.306,1.054 16.306,45.954 16.306,60.914 106.096,60.914 106.096,45.954    106.096,30.984 58.206,30.984  ' />

 </g>

 <g id='contours'

     class='contours'>

  <polygon points='58.209,1.056 16.306,1.056 16.306,60.917 106.097,60.917 106.097,30.985 58.209,30.985  ' />

  <line x1='16.306'

      y1='45.951'

      x2='1.056'

      y2='45.951' />

  <line x1='121.347'

      y1='54.895'

      x2='121.347'

      y2='37.007' />

  <line x1='1.056'

      y1='37.007'

      x2='1.056'

      y2='54.895' />

  <line x1='106.097'

      y1='45.951'

      x2='121.347'

      y2='45.951' />

 </g>

</svg>