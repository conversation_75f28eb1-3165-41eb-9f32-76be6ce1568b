<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='166.785px'

    height='120.046px'

    viewBox='0 0 166.785 120.046'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M165.63,67.02c0-18.961,0-37.931,0-56.891c0-4.24-5.1-6.53-8.17-7.53c-5.59-1.82-10.76-1.95-16.47-0.81   c-3.44,0.69-8.17,2.19-10.36,5.51c-1.32,1.98-0.92,3.92-0.92,5.98c0,3.43,0,6.86,0,10.29c0,14.31,0,28.63,0,42.951   c0,4.648,0,9.289,0,13.94c0,4.24,5.1,6.529,8.17,7.529c5.59,1.82,10.76,1.951,16.47,0.811c3.58-0.721,9.71-2.561,11.07-6.971   c0.54-1.77,0.21-3.469,0.21-5.21C165.63,73.417,165.63,70.218,165.63,67.02z' />

  <path d='M107.34,118.989l-7.38-14.74c2.52-0.869,4.99-1.939,7.38-3.209c0-11.51,0-23.02,0-34.52   c0-16.432,0-32.861,0-49.291c-14.97-7.98-32.93-7.98-47.89,0c0,16.43,0,32.859,0,49.291c0,11.5,0,23.01,0,34.52   c2.39,1.27,4.85,2.34,7.37,3.209l-7.37,14.74H107.34z' />

  <path d='M12.43,88.799c5.72,1.141,10.891,1.01,16.48-0.811c3.069-1,8.16-3.289,8.16-7.529c0-4.651,0-9.292,0-13.94   c0-14.32,0-28.641,0-42.951c0-3.43,0-6.86,0-10.29c0-2.06,0.399-4-0.91-5.98c-2.2-3.32-6.92-4.82-10.36-5.51   c-5.71-1.14-10.89-1.01-16.479,0.81c-3.061,1-8.16,3.29-8.16,7.53c0,18.959,0,37.93,0,56.891c0,3.199,0,6.398,0,9.599   c0,1.741-0.34,3.44,0.21,5.21C2.72,86.239,8.85,88.079,12.43,88.799z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M37.073,80.463c0-18.969,0-37.934,0-56.897c0-3.43,0-6.859,0-10.287c0-2.063,0.4-4.003-0.914-5.98   c-2.2-3.316-6.918-4.817-10.36-5.506C20.086,0.646,14.912,0.782,9.323,2.598c-3.068,0.998-8.166,3.294-8.166,7.527   c0,18.967,0,37.931,0,56.896c0,3.199,0,6.396,0,9.595c0,1.739-0.334,3.438,0.208,5.208c1.354,4.418,7.484,6.252,11.066,6.971   c5.714,1.146,10.887,1.01,16.475-0.807C31.976,86.991,37.073,84.696,37.073,80.463z' />

  <line x1='48.261'

      y1='75.462'

      x2='48.261'

      y2='57.572' />

  <path d='M83.392,17.228c-9.917,0-17.958,8.041-17.958,17.96c0,9.916,8.041,17.958,17.958,17.958   c9.92,0,17.958-8.043,17.958-17.958C101.35,25.269,93.312,17.228,83.392,17.228z' />

  <line x1='107.337'

      y1='66.518'

      x2='129.71'

      y2='66.518' />

  <path d='M129.71,80.463c0-18.969,0-37.934,0-56.897c0-3.43,0-6.859,0-10.287c0-2.063-0.398-4.003,0.916-5.98   c2.198-3.316,6.919-4.817,10.36-5.506c5.713-1.146,10.887-1.011,16.476,0.806c3.068,0.998,8.166,3.294,8.166,7.527   c0,18.967,0,37.931,0,56.896c0,3.199,0,6.396,0,9.595c0,1.739,0.334,3.438-0.208,5.208c-1.356,4.418-7.486,6.252-11.066,6.971   c-5.714,1.146-10.889,1.01-16.477-0.807C134.808,86.991,129.71,84.696,129.71,80.463z' />

  <polyline points='66.821,104.247 59.449,118.991 107.337,118.991 99.964,104.247  ' />

  <line x1='59.449'

      y1='66.518'

      x2='37.073'

      y2='66.518' />

  <circle cx='83.392'

      cy='83.075'

      r='17.96' />

  <line x1='118.524'

      y1='75.462'

      x2='118.524'

      y2='57.572' />

  <path d='M107.337,101.036c-14.966,7.98-32.925,7.98-47.888,0c0-27.937,0-55.872,0-83.808   c14.963-7.98,32.922-7.98,47.888,0C107.337,45.164,107.337,73.099,107.337,101.036z' />

 </g>

</svg>