<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='285.044px'

    height='44.116px'

    viewBox='0 0 285.044 44.116'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='266.024,13.035 134.335,13.035 116.386,13.035 116.386,36.962 266.024,36.962 283.995,36.962    283.995,13.035 272.021,13.035  ' />

  <path d='M83.294,7.947c-0.95-0.613-1.88-0.866-2.83-0.93c-1.034-0.084-2.069,0.021-3.146,0.021   c-3.442,0-6.862,0-10.284,0c-18.984,0-37.948,0-56.911,0c-4.224,0-6.526,5.11-7.519,8.173C0.788,20.785,0.64,25.96,1.802,31.683   c0.717,3.59,2.555,9.714,6.969,11.066c0.443,0.147,0.887,0.231,1.352,0.273c1.288,0.128,2.555-0.063,3.843-0.063   c3.21,0,6.398,0,9.608,0c18.964,0,37.928,0,56.891,0c4.224,0,6.525-5.088,7.519-8.171c1.815-5.576,1.964-10.75,0.823-16.474   C88.108,14.873,86.609,10.164,83.294,7.947z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='266.03'

      y1='1.056'

      x2='266.03'

      y2='13.027' />

  <line x1='128.351'

      y1='1.056'

      x2='140.322'

      y2='1.056' />

  <line x1='134.337'

      y1='1.056'

      x2='134.337'

      y2='13.027' />

  <line x1='260.044'

      y1='1.056'

      x2='272.016'

      y2='1.056' />

  <rect x='116.378'

      y='13.027'

      fill='none'

      width='167.61'

      height='23.944' />

  <polyline points='125.357,25 128.351,16.021 134.337,33.979 140.322,16.021 146.31,33.979 152.294,16.021    158.281,33.979 164.267,16.021 170.253,33.979 176.238,16.021 182.225,33.979 188.211,16.021 194.196,33.979 200.184,16.021    206.169,33.979 212.155,16.021 218.142,33.979 224.128,16.021 230.113,33.979 236.1,16.021 242.087,33.979 248.072,16.021    254.058,33.979 260.044,16.021 266.03,33.979 272.016,16.021 275.01,25  ' />

  <path d='M10.126,7.042c18.965,0,37.931,0,56.897,0c3.429,0,6.856,0,10.284,0c2.063,0,4.004-0.399,5.982,0.913   c3.314,2.2,4.816,6.921,5.506,10.359c1.145,5.716,1.012,10.889-0.808,16.479c-0.998,3.067-3.293,8.165-7.526,8.165   c-18.966,0-37.931,0-56.896,0c-3.199,0-6.396,0-9.595,0c-1.74,0-3.439,0.335-5.208-0.208c-4.417-1.356-6.253-7.485-6.971-11.066   c-1.146-5.713-1.011-10.888,0.806-16.477C3.596,12.139,5.892,7.042,10.126,7.042z' />

  <line x1='10.126'

      y1='42.958'

      x2='10.126'

      y2='7.042' />

  <line x1='80.462'

      y1='42.958'

      x2='80.462'

      y2='7.042' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='89.44'

      y1='25'

      x2='116.378'

      y2='25' />

 </g>

</svg>