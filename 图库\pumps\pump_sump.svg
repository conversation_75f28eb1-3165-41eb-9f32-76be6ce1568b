<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='118.184px'

    height='225.184px'

    viewBox='0 0 118.184 225.184'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='9.387,122.361 9.387,128.358 117.129,128.358 117.129,122.361 105.154,122.361  ' />

  <path d='M81.82,177.942c-1.774-0.548-3.464-0.211-5.194-0.211c-3.21,0-6.399,0-9.609,0c-18.964,0-37.928,0-56.891,0   c-4.224,0-6.524,5.111-7.518,8.173c-1.816,5.596-1.964,10.77-0.824,16.473c0.697,3.441,2.196,8.172,5.513,10.368   c1.984,1.309,3.927,0.908,5.976,0.908c3.042,0,6.061,0,9.081,0l-6.99,10.475h59.868l-6.99-10.475c4.076,0,8.151,0,12.228,0   c4.224,0,6.524-5.09,7.518-8.173c1.815-5.576,1.963-10.748,0.803-16.472C88.071,185.419,86.234,179.295,81.82,177.942z' />

  <path d='M27.337,80.465c0,4.224,5.089,6.525,8.172,7.518c5.575,1.816,10.749,1.964,16.472,0.823   c3.442-0.697,8.152-2.196,10.369-5.511c0.613-0.951,0.866-1.88,0.93-2.83c0.084-1.035-0.021-2.069-0.021-3.146   c0-3.442,0-6.863,0-10.306c0-18.964,0-37.927,0-56.89c0-4.224-5.11-6.527-8.173-7.519c-5.597-1.815-10.771-1.964-16.472-0.802   C35.023,2.521,28.9,4.336,27.548,8.771c-0.147,0.443-0.231,0.887-0.274,1.353c-0.127,1.287,0.063,2.555,0.063,3.843   c0,3.209,0,6.399,0,9.608C27.337,42.538,27.337,61.502,27.337,80.465z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='27.337'

      y1='80.462'

      x2='63.253'

      y2='80.462' />

  <polyline points='22.348,213.652 15.364,224.128 75.226,224.128 68.241,213.652  ' />

  <path d='M63.253,10.125c0,18.967,0,37.933,0,56.898c0,3.429,0,6.857,0,10.285c0,2.063,0.398,4.003-0.914,5.982   c-2.199,3.313-6.919,4.815-10.36,5.505c-5.713,1.146-10.888,1.012-16.477-0.807c-3.067-0.998-8.165-3.294-8.165-7.527   c0-18.966,0-37.931,0-56.896c0-3.198,0-6.396,0-9.595c0-1.74-0.335-3.438,0.208-5.208C28.9,4.346,35.029,2.51,38.61,1.792   C44.325,0.646,49.498,0.781,55.088,2.6C58.155,3.598,63.253,5.892,63.253,10.125z' />

  <line x1='27.337'

      y1='10.125'

      x2='63.253'

      y2='10.125' />

  <line x1='99.17'

      y1='104.407'

      x2='111.142'

      y2='104.407' />

  <path d='M80.463,213.652c-18.966,0-37.932,0-56.896,0c-3.43,0-6.857,0-10.286,0c-2.063,0-4.003,0.398-5.981-0.914   c-3.315-2.199-4.816-6.92-5.507-10.36c-1.146-5.713-1.011-10.887,0.808-16.477c0.998-3.068,3.292-8.165,7.527-8.165   c18.966,0,37.931,0,56.896,0c3.198,0,6.397,0,9.596,0c1.74,0,3.438-0.336,5.207,0.208c4.417,1.355,6.255,7.484,6.972,11.066   c1.146,5.713,1.011,10.888-0.808,16.476C86.991,208.555,84.697,213.652,80.463,213.652z' />

  <line x1='105.155'

      y1='122.365'

      x2='105.155'

      y2='104.407' />

  <rect x='9.379'

      y='122.365'

      fill='none'

      width='107.749'

      height='5.985' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='45.295'

      y1='129.607'

      x2='45.295'

      y2='178.21' />

  <line x1='45.295'

      y1='89.278'

      x2='45.295'

      y2='122.556' />

  <polyline points='89.442,195.694 105.155,195.694 105.155,128.351  ' />

 </g>

</svg>