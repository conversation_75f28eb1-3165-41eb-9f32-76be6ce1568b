<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='109.859px'

    height='154.904px'

    viewBox='0 0 109.859 154.904'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M91.461,118.088c-1.772-0.548-3.463-0.209-5.215-0.209c-3.188,0-6.399,0-9.587,0c-18.964,0-37.928,0-56.891,0   c-4.246,0-6.526,5.088-7.54,8.15c-1.077,3.357-1.562,6.547-1.542,9.799c0.021,2.195,0.274,4.392,0.74,6.694   c0.696,3.442,2.195,8.151,5.512,10.348c1.963,1.329,3.906,0.929,5.976,0.929c3.421,0,6.863,0,10.284,0c18.964,0,37.928,0,56.891,0   c4.244,0,6.547-5.11,7.539-8.173c1.099-3.357,1.563-6.547,1.541-9.798c-0.021-2.176-0.274-4.372-0.738-6.674   C97.712,125.564,95.876,119.44,91.461,118.088z' />

  <path d='M36.979,23.567c0,18.963,0,37.927,0,56.891c0,4.243,5.088,6.524,8.15,7.538   c5.596,1.816,10.77,1.944,16.492,0.804c3.422-0.697,8.151-2.196,10.348-5.513c0.612-0.95,0.866-1.879,0.93-2.829   c0.084-1.036-0.021-2.069-0.021-3.147c0-3.42,0-6.863,0-10.283c0-18.964,0-37.927,0-56.913c0-4.224-5.089-6.525-8.15-7.518   C59.13,0.781,53.957,0.654,48.255,1.794c-3.589,0.718-9.714,2.555-11.086,6.969c-0.127,0.465-0.212,0.908-0.254,1.352   c-0.127,1.288,0.064,2.555,0.064,3.865C36.979,17.168,36.979,20.357,36.979,23.567z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='1.056'

      y1='129.847'

      x2='1.056'

      y2='141.819' />

  <path d='M90.098,153.791c-18.966,0-37.931,0-56.897,0c-3.43,0-6.857,0-10.285,0c-2.063,0-4.004,0.399-5.981-0.913   c-3.315-2.2-4.816-6.921-5.507-10.361c-1.145-5.713-1.011-10.888,0.809-16.477c0.997-3.067,3.292-8.165,7.525-8.165   c18.966,0,37.932,0,56.896,0c3.199,0,6.396,0,9.596,0c1.738,0,3.438-0.336,5.207,0.208c4.417,1.355,6.254,7.484,6.971,11.065   c1.146,5.714,1.011,10.888-0.807,16.478C96.626,148.693,94.331,153.791,90.098,153.791z' />

  <line x1='99.077'

      y1='135.832'

      x2='108.804'

      y2='135.832' />

  <line x1='10.782'

      y1='135.832'

      x2='1.056'

      y2='135.832' />

  <line x1='108.804'

      y1='129.847'

      x2='108.804'

      y2='141.819' />

  <line x1='36.972'

      y1='10.125'

      x2='72.888'

      y2='10.125' />

  <line x1='36.972'

      y1='80.462'

      x2='72.888'

      y2='80.462' />

  <path d='M72.888,10.125c0,18.966,0,37.933,0,56.898c0,3.428,0,6.856,0,10.285c0,2.062,0.398,4.003-0.914,5.98   c-2.2,3.314-6.921,4.816-10.36,5.508c-5.714,1.145-10.888,1.009-16.478-0.81c-3.067-0.996-8.164-3.292-8.164-7.525   c0-18.966,0-37.932,0-56.897c0-3.198,0-6.396,0-9.595c0-1.739-0.336-3.438,0.207-5.207c1.355-4.417,7.484-6.254,11.067-6.972   c5.712-1.146,10.887-1.01,16.476,0.808C67.79,3.597,72.888,5.891,72.888,10.125z' />

 </g>

 <g id='thick'

     class='thick'>

  <line x1='54.93'

      y1='89.441'

      x2='54.93'

      y2='117.875' />

 </g>

</svg>