<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='55.985px'

    height='261.685px'

    viewBox='0 0 55.985 261.685'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M46.36,77.304c0,1.081,0.109,2.12,0.03,3.16h-0.03H10.451c0-18.97,0-37.93,0-56.9c0-3.2,0-6.4,0-9.59   c0-1.29-0.19-2.56-0.061-3.86l0.061,0.01H46.36c0,18.97,0,37.93,0,56.899C46.36,70.455,46.36,73.885,46.36,77.304z' />

  <path d='M28.4,89.535c-3.25,0.03-6.449-0.46-9.79-1.55c-3.07-0.99-8.159-3.29-8.159-7.521H46.36h0.03   c-0.08,0.939-0.32,1.88-0.939,2.83c-2.201,3.311-6.921,4.811-10.361,5.5C32.79,89.254,30.58,89.515,28.4,89.535z' />

  <path d='M46.36,10.125H10.451l-0.061-0.01c0.04-0.45,0.12-0.9,0.26-1.35c1.36-4.42,7.49-6.25,11.069-6.97   c5.711-1.15,10.891-1.01,16.481,0.8C41.261,3.595,46.36,5.895,46.36,10.125z' />

  <polygon points='42.961,128.935 42.961,260.625 13.03,260.625 13.03,128.935 19.011,128.935 36.97,128.935  ' />

  <polygon points='36.97,119.955 36.97,128.935 19.011,128.935 19.011,119.955 19.011,110.974 28.4,110.974    36.97,110.974  ' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='36.972'

      y1='119.957'

      x2='54.93'

      y2='119.957' />

  <polyline points='19.014,128.935 19.014,110.977 36.972,110.977 36.972,128.935  ' />

  <line x1='28.404'

      y1='89.442'

      x2='28.404'

      y2='110.977' />

  <line x1='19.014'

      y1='119.957'

      x2='1.055'

      y2='119.957' />

  <line x1='1.055'

      y1='125.942'

      x2='1.055'

      y2='113.97' />

  <line x1='54.93'

      y1='125.942'

      x2='54.93'

      y2='113.97' />

  <line x1='10.445'

      y1='80.461'

      x2='46.362'

      y2='80.461' />

  <path d='M46.362,10.126c0,18.965,0,37.931,0,56.896c0,3.429,0,6.857,0,10.285c0,2.064,0.398,4.004-0.914,5.982   c-2.199,3.315-6.919,4.816-10.36,5.506c-5.713,1.146-10.888,1.011-16.477-0.807c-3.068-0.999-8.166-3.293-8.166-7.528   c0-18.964,0-37.931,0-56.896c0-3.198,0-6.397,0-9.594c0-1.741-0.335-3.439,0.208-5.208C12.01,4.347,18.139,2.51,21.72,1.792   c5.714-1.146,10.888-1.01,16.478,0.808C41.265,3.597,46.362,5.893,46.362,10.126z' />

  <line x1='10.445'

      y1='10.126'

      x2='46.362'

      y2='10.126' />

  <rect x='13.027'

      y='128.935'

      fill='none'

      width='29.932'

      height='131.693' />

 </g>

</svg>