<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" 
	  viewBox="0 0 91 102" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>
	 
<defs>
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#4C5B75"/>
		<stop  offset="0.0323" style="stop-color:#616F86"/>
		<stop  offset="0.098" style="stop-color:#8E99AA"/>
		<stop  offset="0.1641" style="stop-color:#B4BCC7"/>
		<stop  offset="0.2298" style="stop-color:#D1D7DE"/>
		<stop  offset="0.2946" style="stop-color:#E5EAEF"/>
		<stop  offset="0.3584" style="stop-color:#F2F6F9"/>
		<stop  offset="0.42" style="stop-color:#F6FAFC"/>
		<stop  offset="0.4744" style="stop-color:#F2F6F8"/>
		<stop  offset="0.5308" style="stop-color:#E6E9EC"/>
		<stop  offset="0.5882" style="stop-color:#D3D5D7"/>
		<stop  offset="0.6461" style="stop-color:#B7B8BB"/>
		<stop  offset="0.704" style="stop-color:#949396"/>
		<stop  offset="0.72" style="stop-color:#89878B"/>
		<stop  offset="1" style="stop-color:#F6FAFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="10.9976" y1="0" x2="80.9985" y2="0"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="15.9849" y1="0" x2="75.9961" y2="0"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="17.4629" y1="0" x2="29.9614" y2="0"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="39.7485" y1="0" x2="52.2471" y2="0"/>
	<linearGradient id="cyl_5" xlink:href="#cyl" x1="62.0342" y1="0" x2="74.5327" y2="0"/>
	<linearGradient id="cyl_6" xlink:href="#cyl" x1="0" y1="68.2964" x2="0" y2="83.78"/>
	<linearGradient id="cyl_7" xlink:href="#cyl" x1="0" y1="70.9487" x2="0" y2="80.8908"/>
	<linearGradient id="cyl_8" xlink:href="#cyl" x1="0" y1="43.478" x2="0" y2="58.4771"/>
	<linearGradient id="cyl_9" xlink:href="#cyl" x1="0" y1="45.7051" x2="0" y2="56.3202"/>
	<linearGradient id="cyl_10" xlink:href="#cyl" x1="15.999" y1="0" x2="75.9961" y2="0"/>
	<linearGradient id="cyl_11" xlink:href="#cyl" x1="0" y1="0.8408" x2="0" y2="16.0314"/>
	<linearGradient id="cyl_12" xlink:href="#cyl" x1="31" y1="0" x2="21" y2="0"/>
	<linearGradient id="cyl_13" xlink:href="#cyl" x1="0" y1="3.5" x2="0" y2="13.5"/>
	<linearGradient id="cyl_14" xlink:href="#cyl" x1="36" y1="0" x2="46" y2="0"/>
	
	<radialGradient id="radial_1" cx="18.22" cy="16.152" r="12.645" gradientUnits="userSpaceOnUse">
		<stop offset='0.20933' style='stop-color:#F6FAFC' />
		<stop offset='0.430718' style='stop-color:#89878B' />
		<stop offset='0.443368' style='stop-color:#949396' />
		<stop offset='0.489148' style='stop-color:#B7B8BB' />
		<stop offset='0.534928' style='stop-color:#D3D5D7' />
		<stop offset='0.580312' style='stop-color:#E6E9EC' />
		<stop offset='0.624906' style='stop-color:#F2F6F8' />
		<stop offset='0.667919' style='stop-color:#F6FAFC' />
		<stop offset='0.716624' style='stop-color:#F2F6F9' />
		<stop offset='0.767069' style='stop-color:#E5EAEF' />
		<stop offset='0.818304' style='stop-color:#D1D7DE' />
		<stop offset='0.870251' style='stop-color:#B4BCC7' />
		<stop offset='0.922514' style='stop-color:#8E99AA' />
		<stop offset='0.974461' style='stop-color:#616F86' />
		<stop offset='1.' style='stop-color:#4C5B75' />
	</radialGradient>
	
	<radialGradient id="radial_2" xlink:href="#radial_1" cx="48.391"/>
	
	<path id="boundary" d="M90,15.999C90,10.998,90,6.001,90,1c-1.667,0-3.335,0-5.002,0
		c0,0.204,0,1.163,0,2.501c0,0-36.187-0.007-36.301-0.007c-0.102,0-0.204,0-0.306,0.007c-0.07,0-0.134,0.006-0.204,0.006
		c-0.433,0.013-0.872,0.038-1.298,0.095c-0.401,0.051-0.789,0.121-1.177,0.21c-0.84,0.197-1.661,0.471-2.444,0.833
		c-1.4,0.643-2.679,1.552-3.754,2.66c-1.069,1.107-1.935,2.418-2.533,3.837c-0.496,1.17-0.815,2.424-0.929,3.691
		C36.02,15.198,36,15.56,36,15.923c-0.007,0.07,0,0.14,0,0.209c0,0.006,0,0.013,0,0.019c0,0.115,0,3.768,0,5.479
		c-1.667,0.235-3.334,0.477-5.002,0.713c0-1.681,0-3.367,0-5.047c0-0.267,0-0.535,0-0.795c0-0.121,0-0.242,0-0.363
		c0-0.019,0-0.038,0-0.057c0-0.038,0-0.076,0-0.108c0-0.331-0.019-0.661-0.044-0.992c-0.051-0.617-0.146-1.241-0.287-1.846
		c-0.146-0.611-0.337-1.215-0.573-1.8c-0.592-1.445-1.444-2.788-2.52-3.927c-1.056-1.12-2.329-2.043-3.729-2.698
		c-0.783-0.375-1.61-0.662-2.457-0.865c-0.401-0.096-0.814-0.178-1.228-0.229c-0.439-0.057-0.885-0.089-1.33-0.108
		c-0.179-0.006-0.351-0.006-0.522-0.006c-0.031,0-0.063,0-0.089,0c0,0-0.178,0-0.261,0C17.75,3.507,5.996,3.507,5.996,3.507
		c0-1.338,0-2.296,0-2.501c-1.661,0-3.329,0-4.996,0c0,5.002,0,9.998,0,15c1.667,0,3.334,0,4.996,0c0-0.204,0-1.163,0-2.501H18.22
		c0.076,0,0.146,0,0.223,0c0.089,0,0.178,0,0.261,0.007c0.025,0,0.044,0.006,0.063,0.006c0.064,0.013,0.127,0.02,0.191,0.032
		c0.153,0.025,0.305,0.07,0.458,0.127c0.407,0.166,0.776,0.439,1.05,0.783c0.274,0.356,0.452,0.783,0.509,1.228
		c0.013,0.057,0.013,0.108,0.019,0.166c0.007,0.076,0,0.153,0,0.229c0,0.026,0.007,0.051,0.007,0.076c0,0.102,0,0.204,0,0.306
		c0,0.286,0,0.573,0,0.859c0,2.717,0,5.435,0,8.152c-1.865,0.936-5.002,2.851-5.002,5.53c-2.912,0-4.742,0-5.001,0
		c0,3.335,0,6.663,0,9.998c0.259,0,2.09,0,5.001,0c0,1.667,0,3.334,0,5.002c-3.334,0-6.669,0-10.003,0c0-1.338,0-2.296,0-2.501
		c-1.661,0-3.329,0-4.996,0c0,5.002,0,10.003,0,14.999c1.667,0,3.334,0,4.996,0c0-0.205,0-1.162,0-2.5c3.334,0,6.669,0,10.003,0
		c0,4.989,0,19.944,0,22.438c0,2.908-0.261,4.531,1.464,7.191c0,5.123,0,10.245,0,15.369c2.081,0,10.417,0,12.499,0
		c0-2.438,0-4.881,0-7.318c3.207,1.012,6.446,1.635,9.787,1.992c0,1.775,0,3.551,0,5.327c4.168,0,8.331,0,12.499,0
		c0-1.776,0-3.551,0-5.327c1.706-0.185,3.398-0.439,5.078-0.783c0.814-0.166,1.623-0.35,2.425-0.56
		c0.382-0.096,0.77-0.204,1.152-0.312c0.382-0.108,0.757-0.223,1.132-0.337c0,2.437,0,4.881,0,7.318c4.162,0,8.33,0,12.499,0
		c0-5.124,0-10.246,0-15.369c0.458-0.713,0.84-1.483,1.095-2.291c0.121-0.382,0.216-0.77,0.28-1.171
		c0.025-0.191,0.05-0.388,0.063-0.585c0.02-0.191,0.025-0.388,0.025-0.579c3.335-0.006,5.667-0.006,9.001-0.006
		c0,1.338,0,2.296,0,2.501c1.667,0,3.335,0,5.002,0c0-4.996,0-9.998,0-15c-1.667,0-3.335,0-5.002,0c0,0.204,0,1.163,0,2.501
		c-3.328,0-5.667,0-9.001,0l-0.006,0.006c0-0.006,0.006-0.013,0.006-0.019c0-9.991,0-19.995,0-29.986c2.912,0,4.743,0,5.002,0
		c0-3.334,0-6.663,0-9.998c-0.259,0-2.09,0-5.002,0c0-0.331-0.05-0.662-0.146-0.98c-0.096-0.337-0.248-0.668-0.433-0.974
		c-0.197-0.337-0.445-0.656-0.713-0.948c-0.299-0.331-0.636-0.643-0.993-0.922c-0.4-0.325-0.821-0.618-1.253-0.891
		c-0.496-0.306-0.999-0.586-1.521-0.84c-1.197-0.599-2.45-1.088-3.723-1.515c-1.483-0.497-2.991-0.897-4.519-1.241
		c-1.712-0.381-3.443-0.681-5.18-0.923c-1.871-0.261-3.748-0.445-5.631-0.572c-1.96-0.134-3.92-0.197-5.887-0.197
		c0-1.515,0-4.754,0-4.85c0-0.07,0-0.133,0-0.197c0-0.089,0.006-0.185,0.019-0.273c0.013-0.102,0.032-0.204,0.057-0.306
		c0.115-0.432,0.344-0.84,0.662-1.152c0.312-0.312,0.713-0.535,1.146-0.649c0.127-0.031,0.261-0.057,0.394-0.07
		c0.038,0,0.077,0,0.115-0.006c0.077,0,0.153,0,0.235,0c9.119,0.006,27.246,0,36.372,0c0,1.338,0,2.296,0,2.5
		C86.665,15.999,88.333,15.999,90,15.999z"/>
		
	<linearGradient id="top_1" gradientUnits="userSpaceOnUse" x1="45.9905" y1="96.4033" x2="45.9905" y2="41.145">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.1636" style="stop-color:#BABABA"/>
		<stop  offset="0.2485" style="stop-color:#D7D7D7"/>
		<stop  offset="0.3273" style="stop-color:#EBEBEB"/>
		<stop  offset="0.4" style="stop-color:#F8F8F8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	
	<linearGradient id="top_2" xlink:href="#top_1" x1="45.9973" y1="21.2051" x2="45.9973" y2="41.2732"/>
</defs>	 
	 
<path fill="url(#cyl_1)" d="M80.999,31.005c0,3.335,0,6.663,0,9.998c-1.667,0-68.334,0-70.001,0c0-3.334,0-6.663,0-9.998
	C12.665,31.005,79.331,31.005,80.999,31.005z"/>
<path fill="url(#cyl_2)" d="M74.998,76.003c0.006,1.763,0.344,3.366,0.999,5.002c0,0.191-0.006,0.388-0.025,0.579
	c-0.013,0.197-0.038,0.395-0.063,0.585c-0.064,0.401-0.159,0.789-0.28,1.171c-0.255,0.808-0.636,1.578-1.095,2.291
	c-2.653,0.312-4.97,1.043-7.242,2.488c-2.253,1.464-3.882,3.277-5.257,5.563c-0.375,0.114-0.75,0.229-1.132,0.337
	c-0.382,0.108-0.77,0.216-1.152,0.312c-0.802,0.21-1.61,0.395-2.425,0.56c-1.68,0.344-3.373,0.598-5.078,0.783
	c0-1.712,0-3.417,0-5.129c0-0.261-0.051-0.515-0.127-0.764c-0.095-0.299-0.242-0.585-0.414-0.859
	c-0.197-0.312-0.426-0.598-0.681-0.872c-0.286-0.305-0.598-0.585-0.936-0.846c-0.337-0.267-0.693-0.503-1.068-0.713
	c-0.331-0.185-0.668-0.35-1.025-0.483c-0.649-0.242-1.336-0.382-2.03-0.375c-0.693,0-1.387,0.153-2.036,0.407
	c-0.757,0.287-1.458,0.713-2.094,1.215c-0.299,0.242-0.585,0.503-0.853,0.783c-0.254,0.274-0.49,0.566-0.693,0.878
	c-0.159,0.255-0.299,0.522-0.395,0.803c-0.089,0.267-0.146,0.547-0.146,0.827c0,1.712,0,3.417,0,5.129
	c-3.341-0.356-6.58-0.98-9.787-1.992c-1.375-2.291-3.004-4.099-5.263-5.563c-2.265-1.45-4.588-2.182-7.235-2.488
	c-1.725-2.66-1.464-4.283-1.464-7.191c0-2.495,0-17.45,0-22.438c0.789-1.61,1.222-3.188,1.229-5.002
	c-0.007-1.807-0.439-3.385-1.229-4.995c0-1.667,0-3.335,0-5.002h59.997c0,9.991,0,19.995,0,29.986
	c0,0.006-0.006,0.013-0.006,0.019c-0.064,0.166-0.179,0.477-0.216,0.592c-0.077,0.204-0.14,0.408-0.204,0.611
	c-0.121,0.408-0.229,0.821-0.312,1.241C75.086,74.297,74.998,75.138,74.998,76.003z"/>
<path fill="url(#cyl_3)" d="M29.961,93.682c0,2.437,0,4.881,0,7.318c-2.082,0-10.418,0-12.499,0c0-5.124,0-10.246,0-15.369
	c2.647,0.306,4.97,1.038,7.235,2.488C26.958,89.583,28.587,91.391,29.961,93.682z"/>
<path fill="url(#cyl_4)" d="M52.247,95.673c0,1.775,0,3.551,0,5.327c-4.168,0-8.33,0-12.499,0c0-1.776,0-3.551,0-5.327
	c0-1.712,0-3.417,0-5.129c0-0.28,0.057-0.56,0.146-0.827c0.095-0.281,0.236-0.548,0.395-0.803
	c0.204-0.312,0.439-0.604,0.693-0.878c0.268-0.28,0.554-0.541,0.853-0.783c0.636-0.502,1.336-0.929,2.094-1.215
	c0.649-0.254,1.343-0.407,2.036-0.407c0.694-0.006,1.381,0.133,2.03,0.375c0.356,0.134,0.694,0.299,1.025,0.483
	c0.375,0.21,0.731,0.446,1.068,0.713c0.337,0.261,0.649,0.541,0.936,0.846c0.255,0.274,0.484,0.56,0.681,0.872
	c0.172,0.274,0.318,0.56,0.414,0.859c0.077,0.249,0.127,0.503,0.127,0.764C52.247,92.256,52.247,93.961,52.247,95.673z"/>
<path fill="url(#cyl_5)" d="M74.533,85.631c0,5.123,0,10.245,0,15.369c-4.168,0-8.336,0-12.499,0c0-2.438,0-4.881,0-7.318
	c1.375-2.285,3.004-4.099,5.257-5.563C69.563,86.675,71.879,85.943,74.533,85.631z"/>
<path fill="url(#cyl_6)" d="M90,68.5c0,5.002,0,10.004,0,15c-1.667,0-3.335,0-5.002,0c0-0.833,0-14.166,0-15
	C86.665,68.5,88.333,68.5,90,68.5z"/>
<path fill="url(#cyl_7)" d="M84.998,71.001v9.997c-3.334,0-5.667,0-9.001,0.006c-0.655-1.636-0.993-3.239-0.999-5.002
	c0-0.865,0.089-1.706,0.261-2.552c0.083-0.42,0.191-0.833,0.312-1.241c0.064-0.203,0.127-0.407,0.204-0.611
	c0.038-0.115,0.152-0.426,0.216-0.592l0.006-0.006C79.331,71.001,81.669,71.001,84.998,71.001z"/>
<path fill="url(#cyl_8)" d="M5.996,58.502c-1.661,0-3.329,0-4.996,0c0-4.996,0-9.997,0-14.999c1.667,0,3.334,0,4.996,0
	C5.996,44.337,5.996,57.669,5.996,58.502z"/>
<path fill="url(#cyl_9)" d="M15.999,46.004c0.789,1.61,1.222,3.188,1.229,4.995c-0.007,1.814-0.439,3.392-1.229,5.002
	c-3.334,0-6.669,0-10.003,0v-9.998C9.33,46.004,12.665,46.004,15.999,46.004z"/>
<path fill="url(#cyl_10)" d="M75.996,31.005H15.999c0-2.679,3.138-4.594,5.002-5.53c1.858,0.286,3.57,0.223,5.383-0.337
	c1.807-0.573,3.252-1.496,4.614-2.793c1.667-0.236,3.334-0.478,5.002-0.713c1.654,0.688,3.265,1.018,5.072,0.916
	c1.813-0.121,3.367-0.649,4.926-1.546c1.966,0,3.926,0.063,5.887,0.197c1.883,0.127,3.76,0.312,5.631,0.572
	c1.737,0.242,3.468,0.542,5.18,0.923c1.527,0.344,3.035,0.745,4.519,1.241c1.272,0.426,2.526,0.916,3.723,1.515
	c0.521,0.254,1.024,0.534,1.521,0.84c0.433,0.273,0.853,0.566,1.253,0.891c0.357,0.28,0.694,0.591,0.993,0.922
	c0.268,0.292,0.516,0.611,0.713,0.948c0.185,0.306,0.337,0.637,0.433,0.974C75.946,30.343,75.996,30.674,75.996,31.005z"/>
<path fill="url(#cyl_11)" d="M5.996,16.006c-1.661,0-3.329,0-4.996,0c0-5.002,0-9.998,0-15c1.667,0,3.334,0,4.996,0
	C5.996,1.84,5.996,15.172,5.996,16.006z"/>
<path fill="url(#cyl_11)" d="M90,1c0,5.001,0,9.998,0,14.999c-1.667,0-3.335,0-5.002,0c0-0.833,0-14.166,0-14.999
	C86.665,1,88.333,1,90,1z"/>
<path fill="url(#cyl_12)" d="M21.001,16.158c0-0.025-0.007-0.05-0.007-0.076h10.004c0,0.019,0,0.038,0,0.057
	c0,0.121,0,0.242,0,0.363c0,0.261,0,0.528,0,0.795c0,1.68,0,3.366,0,5.047c-1.361,1.298-2.807,2.22-4.614,2.793
	c-1.813,0.56-3.525,0.624-5.383,0.337c0-2.717,0-5.435,0-8.152c0-0.286,0-0.573,0-0.859
	C21.001,16.362,21.001,16.26,21.001,16.158z"/>
<path fill="url(#cyl_13)" d="M18.22,3.501v10.003H5.996V3.507c0,0,11.754,0,11.964-0.006
	C18.042,3.501,18.22,3.501,18.22,3.501z"/>
<path fill="url(#cyl_13)" d="M84.998,3.507v9.991c-9.125,0-27.252,0.006-36.372,0c-0.083,0-0.159,0-0.235,0V3.507V3.501
	c0.102-0.007,0.204-0.007,0.306-0.007c0.114,0,36.301,0.007,36.301,0.007V3.507z"/>
<path fill="url(#cyl_14)" d="M45.998,21.001c-1.559,0.897-3.112,1.425-4.926,1.546c-1.807,0.102-3.417-0.229-5.072-0.916
	c0-1.712,0-5.365,0-5.479h9.998C45.998,16.248,45.998,19.486,45.998,21.001z"/>

<path fill="url(#radial_1)" d="M30.954,14.981c0.025,0.331,0.044,0.661,0.044,0.992c0,0.032,0,0.07,0,0.108H20.995
	c0-0.076,0.007-0.153,0-0.229c-0.006-0.057-0.006-0.108-0.019-0.166c-0.058-0.445-0.235-0.872-0.509-1.228
	c-0.273-0.344-0.643-0.618-1.05-0.783c-0.153-0.057-0.305-0.102-0.458-0.127c-0.064-0.013-0.127-0.02-0.191-0.032
	c-0.02,0-0.038-0.006-0.063-0.006c-0.083-0.007-0.172-0.007-0.261-0.007c-0.077,0-0.146,0-0.223,0V3.501c0.025,0,0.058,0,0.089,0
	c0.172,0,0.344,0,0.522,0.006c0.445,0.02,0.891,0.051,1.33,0.108c0.414,0.051,0.827,0.133,1.228,0.229
	c0.847,0.204,1.674,0.49,2.457,0.865c1.4,0.655,2.673,1.578,3.729,2.698c1.076,1.139,1.928,2.482,2.52,3.927
	c0.236,0.585,0.426,1.189,0.573,1.8C30.808,13.74,30.903,14.364,30.954,14.981z"/>

<path fill="url(#radial_2)" d="M48.391,3.507v9.991c-0.038,0.006-0.077,0.006-0.115,0.006c-0.133,0.013-0.267,0.039-0.394,0.07
	c-0.433,0.115-0.834,0.337-1.146,0.649c-0.318,0.312-0.547,0.72-0.662,1.152c-0.025,0.102-0.044,0.204-0.057,0.306
	c-0.013,0.089-0.019,0.185-0.019,0.273c0,0.064,0,0.127,0,0.197H36c0-0.006,0-0.013,0-0.019c0-0.07-0.007-0.14,0-0.209
	c0-0.363,0.019-0.726,0.051-1.088c0.114-1.267,0.433-2.521,0.929-3.691c0.598-1.419,1.463-2.73,2.533-3.837
	c1.076-1.108,2.354-2.018,3.754-2.66c0.783-0.363,1.604-0.636,2.444-0.833c0.388-0.089,0.776-0.159,1.177-0.21
	c0.426-0.057,0.866-0.083,1.298-0.095c0.07,0,0.134-0.006,0.204-0.006V3.507z"/>
			
<g id="top" opacity="0.5">
	<path fill="url(#top_1)" d="M74.998,76.165c0.006,1.763,0.344,3.367,0.999,5.002
		c0,0.191-0.006,0.388-0.025,0.579c-0.013,0.197-0.038,0.395-0.063,0.585c-0.064,0.401-0.159,0.789-0.28,1.171
		c-0.255,0.808-0.636,1.578-1.095,2.292c-2.653,0.312-4.97,1.043-7.242,2.487c-2.253,1.464-3.882,3.277-5.257,5.563
		c-0.375,0.114-0.75,0.229-1.132,0.337c-0.382,0.108-0.77,0.216-1.152,0.312c-0.802,0.21-1.61,0.395-2.425,0.56
		c-1.68,0.344-3.373,0.598-5.078,0.783c0-1.712,0-3.417,0-5.129c0-0.261-0.051-0.515-0.127-0.764
		c-0.095-0.299-0.242-0.585-0.414-0.859c-0.197-0.312-0.426-0.598-0.681-0.872c-0.286-0.305-0.598-0.585-0.936-0.846
		c-0.337-0.267-0.693-0.502-1.068-0.713c-0.331-0.185-0.668-0.35-1.025-0.483c-0.649-0.242-1.336-0.382-2.03-0.375
		c-0.693,0-1.387,0.152-2.036,0.407c-0.757,0.287-1.458,0.713-2.094,1.215c-0.299,0.242-0.585,0.503-0.853,0.783
		c-0.254,0.274-0.49,0.566-0.693,0.878c-0.159,0.254-0.299,0.522-0.395,0.802c-0.089,0.267-0.146,0.547-0.146,0.827
		c0,1.712,0,3.417,0,5.129c-3.341-0.356-6.58-0.98-9.787-1.992c-1.375-2.291-3.004-4.098-5.263-5.563
		c-2.265-1.45-4.588-2.182-7.235-2.487c-1.725-2.661-1.464-4.283-1.464-7.191c0-2.495,0-17.449,0-22.438
		c0.789-1.61,1.222-3.188,1.229-5.002c-0.007-1.807-0.439-3.385-1.229-4.996c0-1.667,0-3.334,0-5.002h59.997
		c0,9.991,0,19.995,0,29.986c0,0.007-0.006,0.013-0.006,0.019c-0.064,0.166-0.179,0.478-0.216,0.592
		c-0.077,0.204-0.14,0.408-0.204,0.611c-0.121,0.408-0.229,0.821-0.312,1.241C75.086,74.46,74.998,75.3,74.998,76.165z"/>
	<path fill="url(#top_2)" d="M75.996,31.005H15.999c0-2.679,3.138-4.594,5.002-5.53
		c1.858,0.286,3.57,0.223,5.383-0.337c1.807-0.573,3.252-1.496,4.614-2.793c1.667-0.236,3.334-0.478,5.002-0.713
		c1.654,0.688,3.265,1.018,5.072,0.916c1.813-0.121,3.367-0.649,4.926-1.546c1.966,0,3.926,0.063,5.887,0.197
		c1.883,0.127,3.76,0.312,5.631,0.572c1.737,0.242,3.468,0.542,5.18,0.923c1.527,0.344,3.035,0.745,4.519,1.241
		c1.272,0.426,2.526,0.916,3.723,1.515c0.521,0.254,1.024,0.534,1.521,0.84c0.433,0.273,0.853,0.566,1.253,0.891
		c0.357,0.28,0.694,0.591,0.993,0.922c0.268,0.292,0.516,0.611,0.713,0.948c0.185,0.306,0.337,0.637,0.433,0.974
		C75.946,30.343,75.996,30.674,75.996,31.005z"/>
</g>

<use xlink:href="#boundary" class="color"/>

<g class="stroke">
	<use xlink:href="#boundary"/>
	<line x1="84.998" y1="71.001" x2="84.998" y2="80.999"/>
	<polyline points="84.998,3.507 84.998,13.499 84.998,13.504"/>
	<line x1="5.996" y1="13.504" x2="5.996" y2="3.507"/>
	<line x1="5.996" y1="56.002" x2="5.996" y2="46.004"/>
	<line x1="48.391" y1="13.499" x2="48.391" y2="3.507"/>
	<line x1="45.998" y1="16.152" x2="36" y2="16.152"/>
	<line x1="20.995" y1="16.082" x2="30.999" y2="16.082"/>
	<line x1="18.22" y1="13.504" x2="18.22" y2="3.501"/>
	<path d="M30.999,22.344c-1.361,1.298-2.807,2.22-4.614,2.793c-1.813,0.56-3.525,0.624-5.383,0.337"/>
	<path d="M39.749,95.673
		c0-1.712,0-3.417,0-5.129c0-0.28,0.057-0.56,0.146-0.827c0.095-0.281,0.236-0.548,0.395-0.803
		c0.204-0.312,0.439-0.604,0.693-0.878c0.268-0.28,0.554-0.541,0.853-0.783c0.636-0.502,1.336-0.929,2.094-1.215
		c0.649-0.254,1.343-0.407,2.036-0.407c0.694-0.006,1.381,0.133,2.03,0.375c0.356,0.134,0.694,0.299,1.025,0.483
		c0.375,0.21,0.731,0.446,1.068,0.713c0.337,0.261,0.649,0.541,0.936,0.846c0.255,0.274,0.484,0.56,0.681,0.872
		c0.172,0.274,0.318,0.56,0.414,0.859c0.077,0.249,0.127,0.503,0.127,0.764c0,1.712,0,3.417,0,5.129"/>
	<line x1="75.996" y1="31.005" x2="15.999" y2="31.005"/>
	<line x1="15.999" y1="41.002" x2="75.996" y2="41.002"/>
	<path d="M74.533,85.631c-2.653,0.312-4.97,1.043-7.242,2.488c-2.253,1.464-3.882,3.277-5.257,5.563"/>
	<path d="M29.961,93.682c-1.375-2.291-3.004-4.099-5.263-5.563c-2.265-1.45-4.588-2.182-7.235-2.488"/>
	<path d="M45.998,21.001c-1.559,0.897-3.112,1.425-4.926,1.546c-1.807,0.102-3.417-0.229-5.072-0.916"/>
	<path d="M15.999,56.002c0.789-1.61,1.222-3.188,1.229-5.002c-0.007-1.807-0.439-3.385-1.229-4.995"/>
	<path d="M75.996,70.988c0,0.006-0.006,0.013-0.006,0.019c-0.064,0.166-0.179,0.477-0.216,0.592c-0.077,0.204-0.14,0.408-0.204,0.611
		c-0.121,0.408-0.229,0.821-0.312,1.241c-0.172,0.846-0.261,1.687-0.261,2.552c0.006,1.763,0.344,3.366,0.999,5.002"/>
	<path d="M76.009,70.957C76.041,70.874,76.035,70.887,76.009,70.957L76.009,70.957z"/>
	<path d="M75.99,71.007l0.006-0.006c0-0.007,0-0.013,0-0.013c0.007-0.013,0.007-0.019,0.013-0.031"/>
	<line x1="10.999" y1="36.003" x2="80.996" y2="36.003"/>
</g>

<g fill="#606060">
	<rect x="64.896" y="29.452"  />
	<rect x="33.748" y="29.452"  />
	<rect x="14.498" y="29.452"  />
	<rect x="74.7" y="29.452"  />
	<rect x="44.599" y="29.452"  />
	<rect x="23.948" y="29.452"  />
	<rect x="55.449" y="29.452"  />
	<rect x="64.896" y="41.002"  />
	<rect x="33.748" y="41.002"  />
	<rect x="14.498" y="41.002"  />
	<rect x="74.7" y="41.002"  />
	<rect x="44.599" y="41.002"  />
	<rect x="23.948" y="41.002"  />
	<rect x="55.449" y="41.002"  />
</g>

</svg>
