<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 13.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 14948)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   xmlns:agg="http://www.example.com" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 73 152" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
		vector-effect:	non-scaling-stroke;
	}

	.stroke{stroke-width:0.17;}

	.thin {stroke-width:0.08;}

	.thick {stroke-width:0.26;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0.0051" style="stop-color:#757575"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BABABA"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.4749" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BABABA"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="13.3127" y1="39.0283" x2="31.762" y2="39.0283"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="10.0461" y1="27.6982" x2="35.0286" y2="27.6982"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="10.0461" y1="50.3594" x2="35.0286" y2="50.3594"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="10.0461" y1="66.5605" x2="35.0286" y2="66.5605"/>
	<linearGradient id="cyl_5" xlink:href="#cyl" x1="11.1409" y1="58.459" x2="33.9338" y2="58.459"/>
	<linearGradient id="cyl_6" xlink:href="#cyl" x1="20.9036" y1="21.2217" x2="24.1711" y2="21.2217"/>
	<linearGradient id="cyl_7" xlink:href="#cyl" x1="26.136" y1="57.7002" x2="30.7874" y2="62.3516"/>

</defs>

<g transform="matrix(2.88196,0.,0.,2.86527,-27.9522,-44.3401)">

	<rect x="13.313" y="28.778" fill="url(#cyl_1)"  />
	<rect x="10.046" y="26.619" fill="url(#cyl_2)"  />
	<rect x="10.046" y="49.279" fill="url(#cyl_3)"  />
	<rect x="10.046" y="65.48" fill="url(#cyl_4)"  />
	<rect x="11.141" y="51.438" fill="url(#cyl_5)"  />
	<rect x="20.904" y="15.824" fill="url(#cyl_6)"  />

	<g id="nuts" fill="black" opacity="0.5">
		<polygon points="10.863,67.64 12.496,67.64 12.496,68.175 10.863,68.175 10.863,67.64"/>
		<polygon points="32.578,67.64 34.212,67.64 34.212,68.175 32.578,68.175 32.578,67.64"/>
		<polygon points="21.721,67.64 23.354,67.64 23.354,68.175 21.721,68.175 21.721,67.64"/>
		<polygon points="27.418,67.64 29.052,67.64 29.052,68.175 27.418,68.175 27.418,67.64"/>
		<polygon points="16.023,67.64 17.656,67.64 17.656,68.175 16.023,68.175 16.023,67.64"/>
		<polygon points="10.863,48.744 12.496,48.744 12.496,49.279 10.863,49.279 10.863,48.744"/>
		<polygon points="32.578,48.744 34.212,48.744 34.212,49.279 32.578,49.279 32.578,48.744"/>
		<polygon points="21.721,48.744 23.354,48.744 23.354,49.279 21.721,49.279 21.721,48.744"/>
		<polygon points="27.418,48.744 29.052,48.744 29.052,49.279 27.418,49.279 27.418,48.744"/>
		<polygon points="16.023,48.744 17.656,48.744 17.656,49.279 16.023,49.279 16.023,48.744"/>
		<polygon points="10.863,28.778 12.496,28.778 12.496,29.313 10.863,29.313 10.863,28.778"/>
		<polygon points="32.578,28.778 34.212,28.778 34.212,29.313 32.578,29.313 32.578,28.778"/>
		<polygon points="21.721,28.778 23.354,28.778 23.354,29.313 21.721,29.313 21.721,28.778"/>
		<polygon points="27.418,28.778 29.052,28.778 29.052,29.313 27.418,29.313 27.418,28.778"/>
		<polygon points="16.023,28.778 17.656,28.778 17.656,29.313 16.023,29.313 16.023,28.778"/>
		<polygon points="10.863,26.083 12.496,26.083 12.496,26.619 10.863,26.619 10.863,26.083"/>
		<polygon points="32.578,26.083 34.212,26.083 34.212,26.619 32.578,26.619 32.578,26.083"/>
		<polygon points="21.721,26.083 23.354,26.083 23.354,26.619 21.721,26.619 21.721,26.083"/>
		<polygon points="27.418,26.083 29.052,26.083 29.052,26.619 27.418,26.619 27.418,26.083"/>
		<polygon points="16.023,26.083 17.656,26.083 17.656,26.619 16.023,26.619 16.023,26.083"/>
	</g>

	<polygon class="color" points="34.212,29.313 34.212,28.778 35.029,28.778 35.029,26.619 34.212,26.619 
		34.212,26.083 32.578,26.083 32.578,26.619 29.052,26.619 29.052,26.083 27.418,26.083 27.418,26.619 24.171,26.619 24.171,15.824 
		20.904,15.824 20.904,26.619 17.656,26.619 17.656,26.083 16.023,26.083 16.023,26.619 12.496,26.619 12.496,26.083 10.863,26.083 
		10.863,26.619 10.046,26.619 10.046,28.778 10.863,28.778 10.863,29.313 12.496,29.313 12.496,28.778 13.313,28.778 13.313,49.279 
		12.496,49.279 12.496,48.744 10.863,48.744 10.863,49.279 10.046,49.279 10.046,51.438 11.141,51.438 11.141,65.48 10.046,65.48 
		10.046,67.64 10.863,67.64 10.863,68.175 12.496,68.175 12.496,67.64 16.023,67.64 16.023,68.175 17.656,68.175 17.656,67.64 
		21.721,67.64 21.721,68.175 23.354,68.175 23.354,67.64 27.418,67.64 27.418,68.175 29.052,68.175 29.052,67.64 32.578,67.64 
		32.578,68.175 34.212,68.175 34.212,67.64 35.029,67.64 35.029,65.48 33.934,65.48 33.934,51.438 35.029,51.438 35.029,49.279 
		34.212,49.279 34.212,48.744 32.578,48.744 32.578,49.279 31.762,49.279 31.762,28.778 32.578,28.778 32.578,29.313 "/>

	<g opacity="0.5" class="thin">
		<path d="M22.35,51.438
			c-0.871,0.478-1.717,1.003-2.519,1.556s-1.563,1.135-2.275,1.733c-0.713,0.599-1.38,1.215-1.992,1.84
			c-0.613,0.623-1.173,1.257-1.676,1.889c-0.998,1.261-1.772,2.532-2.241,3.729c-0.236,0.6-0.397,1.185-0.47,1.738
			c-0.07,0.553-0.054,1.076,0.068,1.552"/>
		<path d="M19.153,51.438
			c-0.832,0.478-1.611,1.003-2.326,1.556s-1.367,1.135-1.954,1.733s-1.109,1.215-1.564,1.84c-0.455,0.623-0.843,1.257-1.16,1.889
			c-0.632,1.261-0.987,2.532-1.011,3.729c-0.013,0.6,0.061,1.185,0.225,1.738c0.164,0.553,0.424,1.076,0.784,1.552"/>
		<path d="M16.229,51.438
			c-0.728,0.478-1.378,1.003-1.948,1.556c-0.568,0.553-1.06,1.135-1.473,1.733s-0.751,1.215-1.01,1.84
			c-0.26,0.623-0.444,1.257-0.552,1.889c-0.213,1.261-0.122,2.532,0.303,3.729c0.214,0.6,0.512,1.185,0.899,1.738
			c0.388,0.553,0.865,1.076,1.438,1.552"/>
		<path d="M13.811,51.438
			c-0.562,0.478-1.03,1.003-1.409,1.556c-0.377,0.553-0.667,1.135-0.873,1.733s-0.329,1.215-0.374,1.84
			c-0.045,0.623-0.009,1.257,0.102,1.889c0.224,1.261,0.756,2.532,1.593,3.729c0.422,0.6,0.923,1.185,1.5,1.738
			c0.578,0.553,1.239,1.076,1.976,1.552"/>
		<path d="M12.099,51.438
			c-0.351,0.478-0.601,1.003-0.757,1.556s-0.221,1.135-0.203,1.733c0.017,0.599,0.117,1.215,0.292,1.84
			c0.176,0.623,0.427,1.257,0.748,1.889c0.642,1.261,1.569,2.532,2.754,3.729c0.595,0.6,1.255,1.185,1.979,1.738
			c0.722,0.553,1.511,1.076,2.353,1.552"/>
		<path d="M11.23,51.438
			c-0.111,0.478-0.121,1.003-0.043,1.556c0.077,0.553,0.243,1.135,0.483,1.733c0.239,0.599,0.555,1.215,0.935,1.84
			c0.381,0.623,0.828,1.257,1.333,1.889c1.007,1.261,2.257,2.532,3.691,3.729c0.72,0.6,1.489,1.185,2.299,1.738
			c0.809,0.553,1.66,1.076,2.538,1.552"/>
		<path d="M11.276,51.438
			c0.136,0.478,0.367,1.003,0.673,1.556c0.307,0.553,0.688,1.135,1.13,1.733c0.443,0.599,0.949,1.215,1.502,1.84
			c0.557,0.623,1.163,1.257,1.81,1.889c1.293,1.261,2.762,2.532,4.33,3.729c0.788,0.6,1.602,1.185,2.433,1.738
			c0.829,0.553,1.675,1.076,2.52,1.552"/>
		<path d="M12.23,51.438
			c0.373,0.478,0.826,1.003,1.336,1.556c0.509,0.553,1.077,1.135,1.686,1.733c0.611,0.599,1.266,1.215,1.949,1.84
			c0.684,0.623,1.4,1.257,2.138,1.889c1.473,1.261,3.044,2.532,4.618,3.729c0.791,0.6,1.585,1.185,2.368,1.738
			c0.782,0.553,1.556,1.076,2.296,1.552"/>
		<path d="M14.017,51.438
			c0.58,0.478,1.219,1.003,1.891,1.556c0.67,0.553,1.379,1.135,2.104,1.733c0.729,0.599,1.479,1.215,2.236,1.84
			c0.759,0.623,1.527,1.257,2.296,1.889c1.531,1.261,3.077,2.532,4.53,3.729c0.73,0.6,1.441,1.185,2.113,1.738
			c0.672,0.553,1.309,1.076,1.887,1.552"/>
		<path d="M16.491,51.438
			c0.741,0.478,1.512,1.003,2.292,1.556c0.778,0.553,1.569,1.135,2.354,1.733c0.786,0.599,1.571,1.215,2.341,1.84
			c0.772,0.623,1.531,1.257,2.267,1.889c1.471,1.261,2.863,2.532,4.079,3.729c0.609,0.6,1.178,1.185,1.685,1.738
			c0.507,0.553,0.957,1.076,1.325,1.552"/>
		<path d="M19.454,51.438
			c0.84,0.478,1.683,1.003,2.506,1.556s1.633,1.135,2.413,1.733c0.782,0.599,1.538,1.215,2.258,1.84
			c0.723,0.623,1.41,1.257,2.055,1.889c1.286,1.261,2.416,2.532,3.294,3.729c0.44,0.6,0.82,1.185,1.122,1.738
			c0.303,0.553,0.527,1.076,0.654,1.552"/>
		<path d="M22.663,51.438
			c0.873,0.478,1.718,1.003,2.519,1.556c0.802,0.553,1.564,1.135,2.276,1.733c0.714,0.599,1.38,1.215,1.992,1.84
			c0.613,0.623,1.174,1.257,1.675,1.889c1,1.261,1.773,2.532,2.243,3.729c0.235,0.6,0.397,1.185,0.468,1.738
			c0.071,0.553,0.054,1.076-0.068,1.552"/>
		<path d="M25.861,51.438
			c0.834,0.478,1.613,1.003,2.328,1.556c0.713,0.553,1.366,1.135,1.953,1.733s1.109,1.215,1.564,1.84
			c0.455,0.623,0.844,1.257,1.159,1.889c0.633,1.261,0.987,2.532,1.011,3.729c0.013,0.6-0.059,1.185-0.224,1.738
			c-0.164,0.553-0.424,1.076-0.785,1.552"/>
		<path d="M28.786,51.438
			c0.727,0.478,1.378,1.003,1.947,1.556c0.568,0.553,1.06,1.135,1.473,1.733c0.414,0.599,0.75,1.215,1.011,1.84
			c0.26,0.623,0.443,1.257,0.551,1.889c0.214,1.261,0.121,2.532-0.304,3.729c-0.213,0.6-0.511,1.185-0.898,1.738
			C32.178,64.477,31.7,65,31.127,65.476"/>
		<path d="M31.202,51.438
			c0.562,0.478,1.03,1.003,1.409,1.556c0.378,0.553,0.667,1.135,0.874,1.733s0.33,1.215,0.374,1.84
			c0.044,0.623,0.01,1.257-0.104,1.889c-0.222,1.261-0.754,2.532-1.592,3.729c-0.421,0.6-0.921,1.185-1.5,1.738
			c-0.578,0.553-1.238,1.076-1.975,1.552"/>
		<path d="M32.915,51.438
			c0.352,0.478,0.6,1.003,0.756,1.556c0.157,0.553,0.222,1.135,0.204,1.733s-0.117,1.215-0.293,1.84
			c-0.175,0.623-0.426,1.257-0.747,1.889c-0.641,1.261-1.569,2.532-2.754,3.729c-0.594,0.6-1.256,1.185-1.979,1.738
			c-0.724,0.553-1.511,1.076-2.354,1.552"/>
		<path d="M28.523,51.438
			c-0.74,0.478-1.511,1.003-2.291,1.556c-0.779,0.553-1.569,1.135-2.354,1.733c-0.788,0.599-1.572,1.215-2.343,1.84
			c-0.771,0.623-1.53,1.257-2.267,1.889c-1.469,1.261-2.862,2.532-4.078,3.729c-0.609,0.6-1.177,1.185-1.685,1.738
			c-0.508,0.553-0.957,1.076-1.324,1.552"/>
		<path d="M25.559,51.438
			c-0.84,0.478-1.681,1.003-2.505,1.556c-0.823,0.553-1.634,1.135-2.413,1.733c-0.781,0.599-1.539,1.215-2.259,1.84
			c-0.722,0.623-1.408,1.257-2.053,1.889c-1.287,1.261-2.416,2.532-3.295,3.729c-0.441,0.6-0.821,1.185-1.123,1.738
			c-0.301,0.553-0.524,1.076-0.654,1.552"/>
		<path d="M33.783,51.438
			c0.113,0.478,0.122,1.003,0.043,1.556c-0.077,0.553-0.243,1.135-0.482,1.733c-0.24,0.599-0.555,1.215-0.935,1.84
			c-0.381,0.623-0.827,1.257-1.333,1.889c-1.007,1.261-2.256,2.532-3.69,3.729c-0.721,0.6-1.49,1.185-2.3,1.738
			c-0.809,0.553-1.66,1.076-2.537,1.552"/>
		<path d="M33.739,51.438
			c-0.136,0.478-0.367,1.003-0.673,1.556c-0.307,0.553-0.688,1.135-1.131,1.733c-0.443,0.599-0.947,1.215-1.502,1.84
			c-0.555,0.623-1.162,1.257-1.81,1.889c-1.291,1.261-2.762,2.532-4.33,3.729c-0.786,0.6-1.602,1.185-2.432,1.738
			c-0.828,0.553-1.675,1.076-2.519,1.552"/>
		<path d="M32.783,51.438
			c-0.373,0.478-0.825,1.003-1.334,1.556c-0.51,0.553-1.078,1.135-1.688,1.733c-0.61,0.599-1.264,1.215-1.947,1.84
			c-0.686,0.623-1.402,1.257-2.139,1.889c-1.473,1.261-3.044,2.532-4.618,3.729c-0.79,0.6-1.586,1.185-2.369,1.738
			c-0.782,0.553-1.555,1.076-2.295,1.552"/>
		<path d="M30.997,51.438
			c-0.581,0.478-1.218,1.003-1.89,1.556s-1.379,1.135-2.106,1.733s-1.478,1.215-2.235,1.84c-0.759,0.623-1.527,1.257-2.296,1.889
			c-1.532,1.261-3.078,2.532-4.531,3.729c-0.73,0.6-1.439,1.185-2.112,1.738c-0.672,0.553-1.309,1.076-1.886,1.552"/>
	</g>

	<circle fill="url(#cyl_7)" cx="28.51" cy="60.075" r="3.249"/>
	<circle opacity="0.3" cx="28.51" cy="60.075" r="1.889"/>
	<circle class="color" cx="28.51" cy="60.075" r="3.249"/>

	<g opacity="0.5" class="stroke">
		<circle cx="28.51" cy="60.075" r="3.249"/>
		<circle cx="28.51" cy="60.075" r="1.889"/>
		<line x1="10.046" y1="27.69" x2="35.029" y2="27.69"/>
		<line x1="10.046" y1="50.367" x2="35.029" y2="50.367"/>
		<line x1="10.046" y1="66.552" x2="35.029" y2="66.552"/>
		<polygon fill-rule="evenodd" clip-rule="evenodd" points="
			35.029,28.778 35.029,26.619 24.171,26.619 24.171,15.824 20.904,15.824 20.904,26.619 10.046,26.619 10.046,28.778 13.313,28.778 
			13.313,49.279 10.046,49.279 10.046,51.438 11.141,51.438 11.141,65.48 10.046,65.48 10.046,67.64 35.029,67.64 35.029,65.48 
			33.934,65.48 33.934,51.438 35.029,51.438 35.029,49.279 31.762,49.279 31.762,28.778 	"/>
		<line x1="13.313" y1="49.279" x2="31.762" y2="49.279"/>
		<line x1="11.141" y1="51.438" x2="33.934" y2="51.438"/>
		<line x1="11.141" y1="65.48" x2="33.934" y2="65.48"/>
		<line x1="13.313" y1="28.778" x2="31.762" y2="28.778"/>
		<line x1="20.904" y1="26.619" x2="24.171" y2="26.619"/>
	</g>
</g>

</svg>
