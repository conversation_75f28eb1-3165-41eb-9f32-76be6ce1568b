<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_3" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 72 202" xmlns:agg="http://www.example.com">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:0.5;}

	.thin {stroke-width:0.25;}

	.thick {stroke-width:0.75;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>
	 
<defs>
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#4D5C75"/>
		<stop  offset="0.0311" style="stop-color:#616F85"/>
		<stop  offset="0.097" style="stop-color:#8E99A9"/>
		<stop  offset="0.1634" style="stop-color:#B4BCC7"/>
		<stop  offset="0.2292" style="stop-color:#D1D7DE"/>
		<stop  offset="0.2942" style="stop-color:#E5EAEF"/>
		<stop  offset="0.3583" style="stop-color:#F2F6F9"/>
		<stop  offset="0.42" style="stop-color:#F6FAFC"/>
		<stop  offset="0.4747" style="stop-color:#F2F6F8"/>
		<stop  offset="0.5313" style="stop-color:#E6E9EC"/>
		<stop  offset="0.5889" style="stop-color:#D3D5D7"/>
		<stop  offset="0.6471" style="stop-color:#B7B8BA"/>
		<stop  offset="0.7053" style="stop-color:#949396"/>
		<stop  offset="0.72" style="stop-color:#8A888B"/>
		<stop  offset="1" style="stop-color:#F6FAFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="1" y1="137.7388" x2="8.5049" y2="137.7388"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="6.261" y1="192.1006" x2="11.4622" y2="187.0786"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="20.9985" y1="197.4292" x2="20.9985" y2="189.812"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="30.9912" y1="187.9902" x2="65.7969" y2="187.9902"/>
	
	<linearGradient id="cyl_5" xlink:href="#cyl" x1="28.5908" y1="173.584" x2="68.1973" y2="173.584"/>
	<linearGradient id="cyl_6" xlink:href="#cyl" x1="35.8428" y1="170.7842" x2="60.9443" y2="170.7842"/>
	<linearGradient id="cyl_7" xlink:href="#cyl" x1="39.79" y1="128.7549" x2="56.998" y2="128.7549"/>
	<linearGradient id="cyl_8" xlink:href="#cyl" x1="25.7998" y1="85.5244" x2="71" y2="85.5244"/>
	
	<linearGradient id="cyl_9" xlink:href="#cyl" x1="28.5908" y1="81.5234" x2="68.1973" y2="81.5234"/>
	<linearGradient id="cyl_10" xlink:href="#cyl" x1="36.1445" y1="76.3203" x2="60.6553" y2="76.3203"/>
	<linearGradient id="cyl_11" xlink:href="#cyl" x1="28.6018" y1="39.8965" x2="68.0586" y2="39.8965"/>
	<linearGradient id="cyl_12" xlink:href="#cyl" x1="33.6436" y1="5.875" x2="63.0186" y2="5.875"/>
	
	<linearGradient id="cyl_13" xlink:href="#cyl" x1="28.5908" y1="2.7363" x2="68.1973" y2="2.7363"/>
	<linearGradient id="screw_1" xlink:href="#cyl" x1="32.5195" y1="180.5894" x2="34.6719" y2="180.5894"/>
	
	<rect id="screw" x="32.52" y="174.986" fill="url(#screw_1)"  />
</defs>	
	
	
	<polygon fill="url(#cyl_1)" points="8.505,88.129 8.505,184.241 1,187.349 1,88.129 	"/>
	<polygon fill="url(#cyl_2)" points="14.11,189.84 11.006,197.351 1,187.349 8.505,184.241 	"/>
	<polygon fill="url(#cyl_3)" points="30.991,189.84 30.991,197.351 11.006,197.351 14.11,189.84 	"/>
	<rect x="30.991" y="174.98" fill="url(#cyl_4)"  />
	<polygon fill="url(#cyl_5)" points="68.197,172.187 68.197,174.98 65.797,174.98 30.991,174.98 28.591,174.98 28.591,172.187 
		35.843,172.187 60.944,172.187 	"/>
	<polygon fill="url(#cyl_6)" points="60.944,169.381 60.944,172.187 35.843,172.187 35.843,169.381 39.79,169.381 
		56.998,169.381 	"/>
	<rect x="39.79" y="88.129" fill="url(#cyl_7)"  />
	<polygon fill="url(#cyl_8)" points="71,82.92 71,88.129 56.998,88.129 39.79,88.129 25.8,88.129 25.8,82.92 28.591,82.92 
		68.197,82.92 	"/>
	<polygon fill="url(#cyl_9)" points="68.197,80.126 68.197,82.92 28.591,82.92 28.591,80.126 36.145,80.126 60.655,80.126 	"/>
	<path fill="url(#cyl_10)" d="M60.655,72.515v7.612H36.145v-7.612c4.061,0,8.133,0,12.18,0
		C52.435,72.515,56.545,72.515,60.655,72.515z"/>
	<path fill="url(#cyl_11)" d="M68.059,11.543c0,18.608,0,37.229,0,55.838c0,0.503-0.112,0.918-0.39,1.346
		c-0.553,0.881-1.105,1.762-1.672,2.63c-0.527,0.83-1.118,1.157-2.099,1.157c-1.081,0-2.162,0-3.243,0c-4.11,0-8.221,0-12.331,0
		c-4.047,0-8.119,0-12.18,0c-1.131,0-2.25,0-3.381,0c-0.98,0-1.571-0.328-2.099-1.157c-0.566-0.868-1.119-1.749-1.673-2.63
		c-0.276-0.427-0.401-0.843-0.39-1.346c0-18.609,0-37.229,0-55.838c0-0.932,0.302-1.51,1.069-2.051
		c0.854-0.592,1.696-1.183,2.552-1.774c0.439-0.302,0.879-0.44,1.42-0.44c4.902,0,9.792,0,14.681,0c4.903,0,9.792,0,14.694,0
		c0.54,0,0.98,0.138,1.421,0.44c0.854,0.591,1.696,1.183,2.551,1.774C67.758,10.033,68.059,10.612,68.059,11.543z"/>
	<path fill="url(#cyl_12)" d="M63.019,4.472v2.806c-4.902,0-9.791,0-14.694,0c-4.889,0-9.778,0-14.681,0V4.472H63.019z"/>
	<polygon fill="url(#cyl_13)" points="68.197,1 68.197,4.472 63.019,4.472 33.644,4.472 28.591,4.472 28.591,1 	"/>

	<use xlink:href="#screw"/>
	<use xlink:href="#screw" transform="translate(4.403 0)"/>
	<use xlink:href="#screw" transform="translate(14.869 0)"/>
	<use xlink:href="#screw" transform="translate(25.334 0)"/>
	<use xlink:href="#screw" transform="translate(27.486 0)"/>
	<use xlink:href="#screw" transform="translate(29.668 0)"/>

	
<g id="nuts" opacity="0.5">
	<rect x="32.52" y="170.891"  />
	<rect x="36.854" y="170.891"  />
	<rect x="47.32" y="170.891"  />
	<rect x="57.785" y="170.891"  />
	<rect x="62.119" y="170.891"  />
</g>

<path class="color" d="M71,88.129V82.92h-2.803v-2.793h-7.542v-7.612c1.081,0,2.162,0,3.243,0
	c0.98,0,1.571-0.328,2.099-1.157c0.566-0.868,1.119-1.749,1.672-2.63c0.277-0.427,0.39-0.843,0.39-1.346c0-18.609,0-37.229,0-55.838
	c0-0.932-0.301-1.51-1.068-2.051c-0.854-0.592-1.697-1.183-2.551-1.774c-0.44-0.302-0.881-0.44-1.421-0.44V4.472h5.179V1H28.591
	v3.472h5.053v2.806c-0.541,0-0.98,0.138-1.42,0.44c-0.855,0.591-1.697,1.183-2.552,1.774c-0.768,0.541-1.069,1.119-1.069,2.051
	c0,18.608,0,37.229,0,55.838c-0.012,0.503,0.113,0.918,0.39,1.346c0.554,0.881,1.106,1.762,1.673,2.63
	c0.527,0.83,1.118,1.157,2.099,1.157c1.131,0,2.25,0,3.381,0v7.612h-7.554v2.793H25.8v5.209h13.99v81.252h-3.947v2.806h-7.252v2.793
	h2.4v14.859H14.11l-5.605-5.599V88.129H1v99.22l10.006,10.002h19.985V201h34.806v-26.02h2.4v-2.793h-7.253v-2.806h-3.946V88.129H71z
	"/>
	
<g class="stroke">
	<polyline points="30.991,189.84 14.11,189.84 8.505,184.241 8.505,88.129 1,88.129 1,187.349 11.006,197.351 30.991,197.351 	"/>
	<line x1="8.505" y1="184.241" x2="1" y2="187.349"/>
	<line x1="14.11" y1="189.84" x2="11.006" y2="197.351"/>
	<path d="M36.145,72.515
		c4.061,0,8.133,0,12.18,0c4.11,0,8.221,0,12.331,0c1.081,0,2.162,0,3.243,0c0.98,0,1.571-0.328,2.099-1.157
		c0.566-0.868,1.119-1.749,1.672-2.63c0.277-0.427,0.39-0.843,0.39-1.346c0-18.609,0-37.229,0-55.838
		c0-0.932-0.301-1.51-1.068-2.051c-0.854-0.592-1.697-1.183-2.551-1.774c-0.44-0.302-0.881-0.44-1.421-0.44
		c-4.902,0-9.791,0-14.694,0c-4.889,0-9.778,0-14.681,0c-0.541,0-0.98,0.138-1.42,0.44c-0.855,0.591-1.697,1.183-2.552,1.774
		c-0.768,0.541-1.069,1.119-1.069,2.051c0,18.608,0,37.229,0,55.838c-0.012,0.503,0.113,0.918,0.39,1.346
		c0.554,0.881,1.106,1.762,1.673,2.63c0.527,0.83,1.118,1.157,2.099,1.157C33.895,72.515,35.014,72.515,36.145,72.515z"/>
	<line x1="30.991" y1="186.191" x2="65.797" y2="186.191"/>
	<polygon points="28.591,82.92 25.8,82.92 25.8,88.129 39.79,88.129 56.998,88.129 71,88.129 71,82.92 68.197,82.92 	"/>
	<line x1="39.79" y1="88.129" x2="39.79" y2="169.381"/>
	<line x1="56.998" y1="88.129" x2="56.998" y2="169.381"/>
	<polygon points="63.019,4.472 68.197,4.472 68.197,1 28.591,1 28.591,4.472 33.644,4.472 	"/>
	<line x1="63.019" y1="7.278" x2="63.019" y2="4.472"/>
	<polyline points="28.591,82.92 28.591,80.126 36.145,80.126 60.655,80.126 68.197,80.126 68.197,82.92 	"/>
	<line x1="36.145" y1="72.515" x2="36.145" y2="80.126"/>
	<line x1="60.655" y1="72.515" x2="60.655" y2="80.126"/>
	<line x1="33.644" y1="7.278" x2="33.644" y2="4.472"/>
	<polyline points="35.843,172.187 35.843,169.381 39.79,169.381 56.998,169.381 60.944,169.381 60.944,172.187 	"/>
	<polyline points="30.991,174.98 30.991,186.191 30.991,189.84 30.991,197.351 30.991,201 65.797,201 65.797,186.191 65.797,174.98 	"/>
	<polygon points="35.843,172.187 28.591,172.187 28.591,174.98 30.991,174.98 65.797,174.98 68.197,174.98 68.197,172.187 60.944,172.187 	"/>
	<line x1="32.52" y1="174.986" x2="32.52" y2="186.192"/>
	<line x1="34.672" y1="174.986" x2="34.672" y2="186.192"/>
	<line x1="36.854" y1="174.986" x2="36.854" y2="186.192"/>
	<line x1="39.007" y1="174.986" x2="39.007" y2="186.192"/>
	<line x1="47.32" y1="174.986" x2="47.32" y2="186.192"/>
	<line x1="49.473" y1="174.986" x2="49.473" y2="186.192"/>
	<line x1="57.785" y1="174.986" x2="57.785" y2="186.192"/>
	<line x1="59.938" y1="174.986" x2="59.938" y2="186.192"/>
	<line x1="62.119" y1="174.986" x2="62.119" y2="186.192"/>
	<line x1="64.272" y1="174.986" x2="64.272" y2="186.192"/>
	<line x1="28.597" y1="22.911" x2="68.059" y2="22.911"/>
	<line x1="28.597" y1="59.323" x2="68.063" y2="59.323"/>
	<line x1="28.597" y1="67.382" x2="68.063" y2="67.382"/>
</g>

<g id="holes" opacity="0.5">
	<path d="M36.298,192.56c0-0.519,0.022-1.001-0.654-1.001c-0.678,0-0.655,0.482-0.655,1.001s-0.022,1,0.655,1
		C36.32,193.56,36.298,193.078,36.298,192.56z"/>
	<path d="M65.267,196.938c0-0.196-0.02-0.384-0.075-0.573c-0.059-0.2-0.047-0.428-0.341-0.428c-0.293,0-0.282,0.228-0.34,0.428
		c-0.055,0.189-0.074,0.377-0.074,0.573c0,0.196,0.02,0.384,0.074,0.573c0.058,0.2,0.047,0.427,0.34,0.427
		c0.294,0,0.282-0.227,0.341-0.427C65.247,197.321,65.267,197.133,65.267,196.938z"/>
	<path d="M42.817,199.126c0,0.684-0.257,1-0.959,1s-0.959-0.317-0.959-1c0-0.684,0.257-1.001,0.959-1.001
		S42.817,198.442,42.817,199.126z"/>
	<path d="M44.915,192.56c0-0.684-0.256-1.001-0.959-1.001c-0.701,0-0.959,0.317-0.959,1.001s0.258,1,0.959,1
		C44.659,193.56,44.915,193.243,44.915,192.56z"/>
	<path d="M42.817,190.371c0-0.684-0.257-1.001-0.959-1.001s-0.959,0.317-0.959,1.001s0.257,1.001,0.959,1.001
		S42.817,191.054,42.817,190.371z"/>
	<path d="M44.915,196.938c0-0.684-0.256-1.001-0.959-1.001c-0.701,0-0.959,0.317-0.959,1.001c0,0.684,0.258,1,0.959,1
		C44.659,197.938,44.915,197.621,44.915,196.938z"/>
	<path d="M44.915,188.182c0-0.684-0.256-1-0.959-1c-0.701,0-0.959,0.317-0.959,1s0.258,1,0.959,1
		C44.659,189.182,44.915,188.865,44.915,188.182z"/>
	<path d="M32.451,199.126c0,0.196-0.021,0.384-0.075,0.573c-0.059,0.201-0.047,0.428-0.341,0.428c-0.293,0-0.281-0.227-0.34-0.428
		c-0.055-0.189-0.074-0.377-0.074-0.573c0-0.196,0.02-0.384,0.074-0.573c0.058-0.2,0.047-0.428,0.34-0.428
		c0.294,0,0.282,0.228,0.341,0.428C32.431,198.742,32.451,198.93,32.451,199.126z"/>
	<path d="M40.257,188.182c0-0.617-0.15-1-0.841-1s-0.841,0.384-0.841,1c0,0.616,0.15,1,0.841,1S40.257,188.798,40.257,188.182z"/>
	<path d="M38.417,199.126c0,0.617-0.15,1-0.841,1c-0.691,0-0.841-0.384-0.841-1c0-0.616,0.149-1.001,0.841-1.001
		C38.267,198.125,38.417,198.51,38.417,199.126z"/>
	<path d="M38.417,194.749c0-0.617-0.15-1.001-0.841-1.001c-0.691,0-0.841,0.384-0.841,1.001c0,0.616,0.149,1,0.841,1
		C38.267,195.749,38.417,195.365,38.417,194.749z"/>
	<path d="M38.417,190.371c0-0.617-0.15-1.001-0.841-1.001c-0.691,0-0.841,0.384-0.841,1.001c0,0.616,0.149,1.001,0.841,1.001
		C38.267,191.372,38.417,190.987,38.417,190.371z"/>
	<path d="M40.257,196.938c0-0.617-0.15-1.001-0.841-1.001s-0.841,0.384-0.841,1.001c0,0.617,0.15,1,0.841,1
		S40.257,197.554,40.257,196.938z"/>
	<path d="M40.257,192.56c0-0.617-0.15-1.001-0.841-1.001s-0.841,0.384-0.841,1.001c0,0.616,0.15,1,0.841,1
		S40.257,193.176,40.257,192.56z"/>
	<path d="M36.298,188.182c0-0.519,0.022-1-0.654-1c-0.678,0-0.655,0.482-0.655,1s-0.022,1,0.655,1
		C36.32,189.182,36.298,188.7,36.298,188.182z"/>
	<path d="M61.375,199.126c0,0.518,0.022,1-0.654,1c-0.678,0-0.655-0.482-0.655-1c0-0.519-0.022-1.001,0.655-1.001
		C61.397,198.125,61.375,198.608,61.375,199.126z"/>
	<path d="M61.375,194.749c0-0.519,0.022-1.001-0.654-1.001c-0.678,0-0.655,0.482-0.655,1.001s-0.022,1,0.655,1
		C61.397,195.749,61.375,195.267,61.375,194.749z"/>
	<path d="M62.808,188.182c0-0.519,0.021-1-0.655-1c-0.678,0-0.655,0.482-0.655,1s-0.022,1,0.655,1
		C62.829,189.182,62.808,188.7,62.808,188.182z"/>
	<path d="M65.267,192.56c0-0.196-0.02-0.384-0.075-0.573c-0.059-0.2-0.047-0.428-0.341-0.428c-0.293,0-0.282,0.227-0.34,0.427
		c-0.055,0.19-0.074,0.378-0.074,0.574s0.02,0.384,0.074,0.573c0.058,0.2,0.047,0.427,0.34,0.427c0.294,0,0.282-0.227,0.341-0.427
		C65.247,192.943,65.267,192.755,65.267,192.56z"/>
	<path d="M61.375,190.371c0-0.519,0.022-1.001-0.654-1.001c-0.678,0-0.655,0.482-0.655,1.001s-0.022,1.001,0.655,1.001
		C61.397,191.372,61.375,190.889,61.375,190.371z"/>
	<path d="M62.808,196.938c0-0.519,0.021-1.001-0.655-1.001c-0.678,0-0.655,0.482-0.655,1.001s-0.022,1,0.655,1
		C62.829,197.938,62.808,197.456,62.808,196.938z"/>
	<path d="M33.359,188.182c0-0.196-0.02-0.384-0.075-0.573c-0.058-0.2-0.046-0.427-0.34-0.427c-0.293,0-0.282,0.227-0.341,0.427
		c-0.055,0.19-0.074,0.378-0.074,0.574s0.02,0.384,0.074,0.573c0.059,0.2,0.048,0.427,0.341,0.427s0.282-0.227,0.34-0.427
		C33.34,188.565,33.359,188.377,33.359,188.182z"/>
	<path d="M64.358,190.371c0-0.196-0.021-0.384-0.075-0.573c-0.059-0.2-0.047-0.428-0.341-0.428c-0.293,0-0.282,0.228-0.34,0.427
		c-0.056,0.19-0.075,0.378-0.075,0.574s0.02,0.384,0.075,0.573c0.058,0.2,0.047,0.428,0.34,0.428c0.294,0,0.282-0.228,0.341-0.428
		C64.338,190.754,64.358,190.566,64.358,190.371z"/>
	<path d="M64.358,194.749c0-0.196-0.021-0.384-0.075-0.573c-0.059-0.2-0.047-0.428-0.341-0.428c-0.293,0-0.282,0.228-0.34,0.428
		c-0.056,0.189-0.075,0.377-0.075,0.573c0,0.196,0.02,0.384,0.075,0.573c0.058,0.2,0.047,0.427,0.34,0.427
		c0.294,0,0.282-0.227,0.341-0.427C64.338,195.132,64.358,194.944,64.358,194.749z"/>
	<path d="M64.358,199.126c0,0.196-0.021,0.384-0.075,0.573c-0.059,0.201-0.047,0.428-0.341,0.428c-0.293,0-0.282-0.227-0.34-0.428
		c-0.056-0.189-0.075-0.377-0.075-0.573c0-0.196,0.02-0.384,0.075-0.573c0.058-0.2,0.047-0.428,0.34-0.428
		c0.294,0,0.282,0.228,0.341,0.428C64.338,198.742,64.358,198.93,64.358,199.126z"/>
	<path d="M65.267,188.182c0-0.196-0.02-0.384-0.075-0.573c-0.059-0.2-0.047-0.427-0.341-0.427c-0.293,0-0.282,0.227-0.34,0.427
		c-0.055,0.19-0.074,0.378-0.074,0.574s0.02,0.384,0.074,0.573c0.058,0.2,0.047,0.427,0.34,0.427c0.294,0,0.282-0.227,0.341-0.427
		C65.247,188.565,65.267,188.377,65.267,188.182z"/>
	<path d="M33.359,192.56c0-0.196-0.02-0.384-0.075-0.573c-0.058-0.2-0.046-0.428-0.34-0.428c-0.293,0-0.282,0.227-0.341,0.427
		c-0.055,0.19-0.074,0.378-0.074,0.574s0.02,0.384,0.074,0.573c0.059,0.2,0.048,0.427,0.341,0.427s0.282-0.227,0.34-0.427
		C33.34,192.943,33.359,192.755,33.359,192.56z"/>
	<path d="M33.359,196.938c0-0.196-0.02-0.384-0.075-0.573c-0.058-0.2-0.046-0.428-0.34-0.428c-0.293,0-0.282,0.228-0.341,0.428
		c-0.055,0.189-0.074,0.377-0.074,0.573c0,0.196,0.02,0.384,0.074,0.573c0.059,0.2,0.048,0.427,0.341,0.427s0.282-0.227,0.34-0.427
		C33.34,197.321,33.359,197.133,33.359,196.938z"/>
	<path d="M32.451,190.371c0-0.196-0.021-0.384-0.075-0.573c-0.059-0.2-0.047-0.428-0.341-0.428c-0.293,0-0.281,0.228-0.34,0.427
		c-0.055,0.19-0.074,0.378-0.074,0.574s0.02,0.384,0.074,0.573c0.058,0.2,0.047,0.428,0.34,0.428c0.294,0,0.282-0.228,0.341-0.428
		C32.431,190.754,32.451,190.566,32.451,190.371z"/>
	<path d="M32.451,194.749c0-0.196-0.021-0.384-0.075-0.573c-0.059-0.2-0.047-0.428-0.341-0.428c-0.293,0-0.281,0.228-0.34,0.428
		c-0.055,0.189-0.074,0.377-0.074,0.573c0,0.196,0.02,0.384,0.074,0.573c0.058,0.2,0.047,0.427,0.34,0.427
		c0.294,0,0.282-0.227,0.341-0.427C32.431,195.132,32.451,194.944,32.451,194.749z"/>
	<path d="M34.865,199.126c0,0.518,0.022,1-0.653,1c-0.679,0-0.655-0.482-0.655-1c0-0.519-0.023-1.001,0.655-1.001
		C34.888,198.125,34.865,198.608,34.865,199.126z"/>
	<path d="M34.865,194.749c0-0.519,0.022-1.001-0.653-1.001c-0.679,0-0.655,0.482-0.655,1.001s-0.023,1,0.655,1
		C34.888,195.749,34.865,195.267,34.865,194.749z"/>
	<path d="M36.298,196.938c0-0.519,0.022-1.001-0.654-1.001c-0.678,0-0.655,0.482-0.655,1.001s-0.022,1,0.655,1
		C36.32,197.938,36.298,197.456,36.298,196.938z"/>
	<path d="M34.865,190.371c0-0.519,0.022-1.001-0.653-1.001c-0.679,0-0.655,0.482-0.655,1.001s-0.023,1.001,0.655,1.001
		C34.888,191.372,34.865,190.889,34.865,190.371z"/>
	<path d="M49.897,192.56c0-0.708-0.293-1.001-1-1.001s-1,0.293-1,1.001c0,0.708,0.293,1,1,1S49.897,193.267,49.897,192.56z"/>
	<path d="M52.7,199.126c0,0.684-0.258,1-0.96,1s-0.959-0.317-0.959-1c0-0.684,0.257-1.001,0.959-1.001S52.7,198.442,52.7,199.126z"
		/>
	<path d="M59.221,196.938c0-0.617-0.15-1.001-0.841-1.001s-0.842,0.384-0.842,1.001c0,0.617,0.151,1,0.842,1
		S59.221,197.554,59.221,196.938z"/>
	<path d="M57.381,199.126c0,0.617-0.15,1-0.841,1s-0.841-0.384-0.841-1c0-0.616,0.15-1.001,0.841-1.001
		S57.381,198.51,57.381,199.126z"/>
	<path d="M49.897,196.938c0-0.708-0.293-1.001-1-1.001s-1,0.293-1,1.001c0,0.708,0.293,1,1,1S49.897,197.645,49.897,196.938z"/>
	<path d="M47.711,190.371c0-0.708-0.293-1.001-1-1.001s-1,0.293-1,1.001s0.293,1.001,1,1.001S47.711,191.078,47.711,190.371z"/>
	<path d="M47.711,194.749c0-0.708-0.293-1.001-1-1.001s-1,0.293-1,1.001c0,0.708,0.293,1,1,1S47.711,195.456,47.711,194.749z"/>
	<path d="M47.711,199.126c0,0.708-0.293,1-1,1s-1-0.293-1-1c0-0.708,0.293-1.001,1-1.001S47.711,198.418,47.711,199.126z"/>
	<path d="M49.897,188.182c0-0.708-0.293-1-1-1s-1,0.293-1,1c0,0.708,0.293,1,1,1S49.897,188.89,49.897,188.182z"/>
	<path d="M54.799,192.56c0-0.684-0.258-1.001-0.96-1.001s-0.959,0.317-0.959,1.001s0.257,1,0.959,1S54.799,193.243,54.799,192.56z"
		/>
	<path d="M54.799,196.938c0-0.684-0.258-1.001-0.96-1.001s-0.959,0.317-0.959,1.001c0,0.684,0.257,1,0.959,1
		S54.799,197.621,54.799,196.938z"/>
	<path d="M54.799,188.182c0-0.684-0.258-1-0.96-1s-0.959,0.317-0.959,1s0.257,1,0.959,1S54.799,188.865,54.799,188.182z"/>
	<path d="M57.381,190.371c0-0.617-0.15-1.001-0.841-1.001s-0.841,0.384-0.841,1.001c0,0.616,0.15,1.001,0.841,1.001
		S57.381,190.987,57.381,190.371z"/>
	<path d="M59.221,188.182c0-0.617-0.15-1-0.841-1s-0.842,0.384-0.842,1c0,0.616,0.151,1,0.842,1S59.221,188.798,59.221,188.182z"/>
	<path d="M52.7,190.371c0-0.684-0.258-1.001-0.96-1.001s-0.959,0.317-0.959,1.001s0.257,1.001,0.959,1.001
		S52.7,191.054,52.7,190.371z"/>
	<path d="M52.7,194.749c0-0.684-0.258-1.001-0.96-1.001s-0.959,0.317-0.959,1.001c0,0.684,0.257,1,0.959,1
		S52.7,195.432,52.7,194.749z"/>
	<path d="M59.221,192.56c0-0.617-0.15-1.001-0.841-1.001s-0.842,0.384-0.842,1.001c0,0.616,0.151,1,0.842,1
		S59.221,193.176,59.221,192.56z"/>
	<path d="M57.381,194.749c0-0.617-0.15-1.001-0.841-1.001s-0.841,0.384-0.841,1.001c0,0.616,0.15,1,0.841,1
		S57.381,195.365,57.381,194.749z"/>
	<path d="M62.808,192.56c0-0.519,0.021-1.001-0.655-1.001c-0.678,0-0.655,0.482-0.655,1.001s-0.022,1,0.655,1
		C62.829,193.56,62.808,193.078,62.808,192.56z"/>
	<path d="M42.817,194.749c0-0.684-0.257-1.001-0.959-1.001s-0.959,0.317-0.959,1.001c0,0.684,0.257,1,0.959,1
		S42.817,195.432,42.817,194.749z"/>
</g>

<g id="ribs">
	<polyline opacity="0.3" fill="#010101" points="32.734,59.323 32.734,28.019 31.357,25.729 31.357,26.191 31.357,59.323 	"/>
	<polyline opacity="0.3" fill="#010101" points="36.631,59.323 36.631,28.019 35.254,25.729 35.254,26.191 35.254,59.323 	"/>
	<polyline opacity="0.3" fill="#010101" points="42.462,59.323 42.462,28.019 41.085,25.729 41.085,26.191 41.085,59.323 	"/>
	<polyline opacity="0.3" fill="#010101" points="49.339,59.323 49.339,28.019 47.962,25.729 47.962,26.191 47.962,59.323 	"/>
	<polyline opacity="0.3" fill="#010101" points="56.218,59.323 56.218,28.019 54.841,25.729 54.841,26.191 54.841,59.323 	"/>
	<polyline opacity="0.3" fill="#010101" points="62.05,59.323 62.05,28.019 60.673,25.729 60.673,26.191 60.673,59.323 	"/>
	<polyline opacity="0.3" fill="#010101" points="65.296,59.323 65.296,28.019 63.919,25.729 63.919,26.191 63.919,59.323 	"/>
</g>
</svg>
