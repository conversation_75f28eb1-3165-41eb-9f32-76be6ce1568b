<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="_x30_" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="0 0 75 282" enable-background="new 0 0 75 282" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
    <agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke,.thin,.thick{
		stroke-linecap:butt;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
		opacity:0.5;
	}

	.stroke{stroke-width:1;}

	.thin {stroke-width:0.5;}

	.thick {stroke-width:1.5;}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="cyl" gradientUnits="userSpaceOnUse">
		<stop  offset="0.0051" style="stop-color:#4C5B75"/>
		<stop  offset="0.0323" style="stop-color:#616F86"/>
		<stop  offset="0.098" style="stop-color:#8E99AA"/>
		<stop  offset="0.1641" style="stop-color:#B4BCC7"/>
		<stop  offset="0.2298" style="stop-color:#D1D7DE"/>
		<stop  offset="0.2946" style="stop-color:#E5EAEF"/>
		<stop  offset="0.3584" style="stop-color:#F2F6F9"/>
		<stop  offset="0.42" style="stop-color:#F6FAFC"/>
		<stop  offset="0.4709" style="stop-color:#F2F6F8"/>
		<stop  offset="0.5236" style="stop-color:#E6E9EC"/>
		<stop  offset="0.5772" style="stop-color:#D3D5D7"/>
		<stop  offset="0.6314" style="stop-color:#B7B8BB"/>
		<stop  offset="0.6856" style="stop-color:#949396"/>
		<stop  offset="0.7005" style="stop-color:#89878B"/>
		<stop  offset="1" style="stop-color:#F6FAFC"/>
	</linearGradient>
	
	<linearGradient id="cyl_1" xlink:href="#cyl" x1="237.6904" y1="-198.281" x2="284.916" y2="-198.281" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_2" xlink:href="#cyl" x1="249.4922" y1="-120.9465" x2="273.1133" y2="-120.9465" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_3" xlink:href="#cyl" x1="230.6201" y1="-64.7146" x2="291.9854" y2="-64.7146" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_4" xlink:href="#cyl" x1="302.6191" y1="-63.9783" x2="302.6191" y2="-36.6163" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_5" xlink:href="#cyl" x1="291.1563" y1="-42.9055" x2="291.1563" y2="-57.267" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_6" xlink:href="#cyl" x1="230.6201" y1="-6.9001" x2="291.9854" y2="-6.9001" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_7" xlink:href="#cyl" x1="239.0176" y1="-35.8025" x2="283.9629" y2="-35.8025" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_8" xlink:href="#cyl" x1="237.6904" y1="-178.6501" x2="284.916" y2="-178.6501" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="cyl_9" xlink:href="#cyl" x1="248.2188" y1="-270.7522" x2="274.3662" y2="-270.7522" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	
	<linearGradient id="con_1" xlink:href="#cyl" x1="235.5781" y1="-196.8567" x2="284.2084" y2="-178.7756" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
	<linearGradient id="window_1" xlink:href="#cyl" x1="252.6523" y1="-35.8137" x2="298.1667" y2="-35.8137" gradientTransform="matrix(1 0 0 -1 -229.6201 -4.0747)"/>
</defs>

<g id="sect">
	<polygon fill="url(#cyl_1)" points="55.296,185.56 55.296,202.852 47.833,202.852 15.514,202.852 8.07,202.852 8.07,185.56"/>
	<polygon id="con" fill="url(#con_1)" points="55.296,185.56 8.07,185.56 15.514,178.106 47.833,178.106"/>
</g>

<use xlink:href="#sect" transform="translate(0 24.746)"/>
<use xlink:href="#sect" transform="translate(0 49.49)"/>
	
<g id="cyl">
	<rect x="19.872" y="62.475" fill="url(#cyl_2)"  />
	<polygon fill="url(#cyl_3)" points="62.365,58.804 62.365,62.475 43.493,62.475 19.872,62.475 1,62.475 1,58.804 9.397,58.804 54.343,58.804"/>
	<polygon fill="url(#cyl_4)" points="74,32.543 74,60.024 71.998,60.024 71.998,53.56 71.998,39.025 71.998,32.543 	"/>
	<path fill="url(#cyl_5)" d="M71.998,39.025v14.537c-5.893,0-11.763,0-17.655,0c-0.13,0-0.263,0-0.395,0
		c-3.832-4.085-3.832-10.435,0-14.537c0.132,0,0.265,0,0.395,0C60.235,39.025,66.105,39.025,71.998,39.025z"/>
	<polygon fill="url(#cyl_6)" points="62.365,1 62.365,4.651 54.343,4.651 9.397,4.651 1,4.651 1,1 	"/>
	<path id="boss" fill="url(#cyl_7)" d="M53.948,53.56c0.132,0,0.265,0,0.395,0v5.245H9.397V4.651h44.945v34.374
		c-0.13,0-0.263,0-0.395,0C50.116,43.125,50.116,49.476,53.948,53.56z M40.07,43.051c0-7.547,0-15.079,0-22.627
		c0-2.415-0.99-3.408-3.401-3.408c-3.33,0-6.659,0-9.988,0c-2.394,0-3.404,0.993-3.404,3.408c0,7.549,0,15.08,0,22.627
		c0,2.417,1.011,3.41,3.404,3.41c3.329,0,6.658,0,9.988,0C39.08,46.46,40.07,45.467,40.07,43.051z"/>
	<polygon fill="url(#cyl_8)" points="55.296,171.268 55.296,177.883 8.07,177.883 8.07,171.268 	"/>
	<rect x="18.599" y="252.357" fill="url(#cyl_9)"  />
</g>

<path id="window" fill="url(#window_1)" d="M40.073,20.425c0,7.55,0,15.08,0,22.628c0,2.416-0.993,3.41-3.404,3.41
	c-3.33,0-6.659,0-9.988,0c-2.394,0-3.404-0.994-3.404-3.41c0-7.548,0-15.078,0-22.628c0-2.415,1.011-3.408,3.404-3.408
	c3.329,0,6.658,0,9.988,0C39.08,17.016,40.073,18.01,40.073,20.425z"/>
<path id="boundary" class="color" d="M15.514,202.867l-7.444,7.456v17.271h7.444h32.321
	h7.461v-17.271l-7.461-7.456H15.514z M15.514,227.613l-7.444,7.456v17.288h10.531v28.639h26.146v-28.637h10.55v-17.288l-7.461-7.457
	L15.514,227.613L15.514,227.613z M71.998,32.543v6.481c-5.893,0-11.763,0-17.655,0V4.651h8.022V1H1v3.651h8.397v54.153H1v3.671
	h18.872v108.793H8.07v3.409v3.427h7.444l-7.444,7.454v17.292h7.444h32.321h7.461v-17.292l-7.461-7.454h7.461v-3.427v-3.409H43.493
	V62.475h18.872v-3.671h-8.022V53.56c5.893,0,11.763,0,17.655,0v6.462H74V32.541h-2.002V32.543z"/>
	
<g id="nuts" fill="#606060">
	<rect x="2.051" y="62.468"  />
	<rect x="57.152" y="62.468"  />
	<rect x="49.283" y="62.468"  />
	<rect x="33.538" y="62.468"  />
	<rect x="9.924" y="62.468"  />
	<rect x="25.667" y="62.468"  />
	<rect x="17.796" y="62.468"  />
	<rect x="41.411" y="62.468"  />
</g>

<g id="nuts_2" fill="#606060">	
	<rect x="49.283" y="169.496"  />
	<rect x="33.538" y="169.496"  />
	<rect x="9.924" y="169.496"  />
	<rect x="25.667" y="169.496"  />
	<rect x="17.796" y="169.496"  />
	<rect x="41.411" y="169.496"  />
</g>

<use xlink:href="#nuts_2" transform="translate(0 8.61)"/>
<use xlink:href="#nuts_2" transform="translate(0 33.356)"/>
<use xlink:href="#nuts_2" transform="translate(0 58.101)"/>
<use xlink:href="#nuts_2" transform="translate(0 82.861)"/>

<g id="net" class="thin">
	<path d="M27.739,252.357
		c-0.956,0.977-1.85,2.045-2.671,3.174c-0.819,1.127-1.568,2.313-2.243,3.536c-0.671,1.222-1.273,2.48-1.793,3.754
		c-0.523,1.274-0.969,2.563-1.332,3.855c-0.726,2.571-1.133,5.169-1.16,7.608c-0.013,1.226,0.067,2.418,0.258,3.548
		c0.189,1.129,0.486,2.198,0.899,3.167"/>
	<path d="M21.606,252.357
		c-0.644,0.977-1.181,2.045-1.617,3.174c-0.432,1.127-0.764,2.313-1.002,3.536c-0.238,1.222-0.38,2.48-0.43,3.754
		c-0.051,1.274-0.01,2.563,0.118,3.855c0.255,2.571,0.866,5.169,1.828,7.608c0.483,1.226,1.058,2.418,1.722,3.548
		c0.664,1.129,1.421,2.198,2.267,3.167"/>
	<path d="M18.646,252.357
		c-0.13,0.977-0.139,2.045-0.05,3.174c0.089,1.127,0.278,2.313,0.555,3.536c0.275,1.222,0.636,2.48,1.071,3.754
		c0.438,1.274,0.951,2.563,1.529,3.855c1.156,2.571,2.592,5.169,4.237,7.608c0.826,1.226,1.71,2.418,2.64,3.548
		c0.929,1.129,1.905,2.198,2.912,3.167"/>
	<path d="M19.792,252.357
		c0.428,0.977,0.949,2.045,1.534,3.174c0.585,1.127,1.236,2.313,1.935,3.536c0.702,1.222,1.451,2.48,2.238,3.754
		c0.785,1.274,1.606,2.563,2.453,3.855c1.688,2.571,3.493,5.169,5.301,7.608c0.906,1.226,1.819,2.418,2.718,3.548
		c0.897,1.129,1.783,2.198,2.638,3.167"/>
	<path d="M24.685,252.357
		c0.852,0.977,1.735,2.045,2.63,3.174c0.894,1.127,1.802,2.313,2.702,3.536c0.902,1.222,1.805,2.48,2.688,3.754
		c0.884,1.274,1.758,2.563,2.602,3.855c1.688,2.571,3.287,5.169,4.681,7.608c0.7,1.226,1.354,2.418,1.935,3.548
		c0.581,1.129,1.098,2.198,1.52,3.167"/>
	<path d="M31.769,252.357
		c1.003,0.977,1.971,2.045,2.89,3.174c0.922,1.127,1.795,2.313,2.609,3.536c0.82,1.222,1.586,2.48,2.287,3.754
		c0.705,1.274,1.349,2.563,1.922,3.855c1.148,2.571,2.038,5.169,2.575,7.608c0.272,1.226,0.455,2.418,0.537,3.548
		c0.084,1.129,0.061,2.198-0.077,3.167"/>
	<path d="M38.795,252.357
		c0.833,0.977,1.58,2.045,2.232,3.174c0.653,1.127,1.216,2.313,1.691,3.536c0.475,1.222,0.862,2.48,1.159,3.754
		c0.298,1.274,0.506,2.563,0.634,3.855c0.245,2.571,0.138,5.169-0.351,7.608c-0.244,1.226-0.584,2.418-1.033,3.548
		c-0.446,1.129-0.992,2.198-1.647,3.167"/>
	<path d="M43.535,252.357
		c0.403,0.977,0.688,2.045,0.869,3.174c0.179,1.127,0.252,2.313,0.233,3.536c-0.023,1.222-0.135,2.48-0.338,3.754
		c-0.2,1.274-0.489,2.563-0.858,3.855c-0.735,2.571-1.801,5.169-3.158,7.608c-0.681,1.226-1.44,2.418-2.271,3.548
		c-0.831,1.129-1.733,2.198-2.7,3.167"/>
	<path d="M35.091,252.357
		c-0.963,0.977-1.929,2.045-2.875,3.174c-0.942,1.127-1.874,2.313-2.768,3.536c-0.897,1.222-1.765,2.48-2.592,3.754
		c-0.829,1.274-1.618,2.563-2.357,3.855c-1.478,2.571-2.771,5.169-3.78,7.608c-0.505,1.226-0.94,2.418-1.289,3.548
		c-0.346,1.129-0.604,2.198-0.75,3.167"/>
	<path d="M44.479,252.357
		c-0.157,0.977-0.424,2.045-0.774,3.174c-0.351,1.127-0.789,2.313-1.297,3.536c-0.506,1.222-1.089,2.48-1.725,3.754
		c-0.636,1.274-1.333,2.563-2.075,3.855c-1.482,2.571-3.169,5.169-4.97,7.608c-0.904,1.226-1.839,2.418-2.791,3.548
		c-0.951,1.129-1.923,2.198-2.892,3.167"/>
	<path d="M41.33,252.357
		c-0.663,0.977-1.396,2.045-2.166,3.174c-0.77,1.127-1.582,2.313-2.418,3.536c-0.833,1.222-1.695,2.48-2.565,3.754
		c-0.869,1.274-1.755,2.563-2.635,3.855c-1.76,2.571-3.534,5.169-5.202,7.608c-0.838,1.226-1.651,2.418-2.424,3.548
		c-0.773,1.129-1.502,2.198-2.164,3.167"/>
</g>

<g id="lines" class="stroke">
	<polyline points="15.514,227.596 8.07,235.069 8.07,252.357 18.599,252.357 44.746,252.357 55.296,252.357 55.296,235.069 47.833,227.596 	"/>
	<polyline points="15.514,202.852 8.07,210.324 8.07,227.596 15.514,227.596 47.833,227.596 55.296,227.596 55.296,210.324 47.833,202.852 	"/>
	<polyline points="15.514,178.106 8.07,185.56 8.07,202.852 15.514,202.852 47.833,202.852 55.296,202.852 55.296,185.56 47.833,178.106 	"/>
	<line x1="18.599" y1="280.998" x2="44.746" y2="280.998"/>
	<line x1="55.296" y1="210.324" x2="8.07" y2="210.324"/>
	<line x1="8.07" y1="174.679" x2="55.296" y2="174.679"/>
	<line x1="55.296" y1="235.069" x2="8.07" y2="235.069"/>
	<polyline points="8.07,171.268 19.872,171.268 43.493,171.268 55.296,171.268 	"/>
	<polyline points="8.07,178.106 8.07,174.679 8.07,171.268 	"/>
	<polyline points="55.296,178.106 55.296,174.679 55.296,171.268 	"/>
	<line x1="55.296" y1="185.56" x2="8.07" y2="185.56"/>
	<line x1="9.397" y1="58.804" x2="9.397" y2="4.651"/>
	<line x1="54.343" y1="4.651" x2="54.343" y2="39.025"/>
	<path d="M26.681,46.46
		c-2.394,0-3.404-0.993-3.404-3.41c0-7.547,0-15.079,0-22.627c0-2.415,1.011-3.408,3.404-3.408c3.329,0,6.658,0,9.988,0
		c2.411,0,3.401,0.993,3.401,3.408c0,7.549,0,15.08,0,22.627c0,2.417-0.99,3.41-3.401,3.41C33.337,46.46,30.01,46.46,26.681,46.46z"
		/>
	<line x1="54.343" y1="53.56" x2="54.343" y2="58.804"/>
	<path d="M71.998,53.56
		c-5.893,0-11.763,0-17.655,0c-0.13,0-0.263,0-0.395,0c-3.832-4.085-3.832-10.435,0-14.537c0.132,0,0.265,0,0.395,0
		c5.893,0,11.763,0,17.655,0"/>
	<polygon points="71.998,53.56 71.998,60.024 74,60.024 74,32.543 71.998,32.543 71.998,39.025 	"/>
	<polygon points="54.343,4.651 62.365,4.651 62.365,1 1,1 1,4.651 9.397,4.651 	"/>
	<polygon points="19.872,62.475 1,62.475 1,58.804 9.397,58.804 54.343,58.804 62.365,58.804 62.365,62.475 43.493,62.475 	"/>
	<line x1="18.599" y1="252.357" x2="18.599" y2="280.998"/>
	<polyline points="8.07,178.106 15.514,178.106 47.833,178.106 55.296,178.106 	"/>
	<line x1="19.872" y1="62.475" x2="19.872" y2="171.268"/>
	<line x1="44.746" y1="252.357" x2="44.746" y2="280.998"/>
	<line x1="43.493" y1="62.475" x2="43.493" y2="171.268"/>
</g>
</svg>
