<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param name="liquidColor" description="Liquid color" type="C" cssAttributes="fill" classes="liquidColor" forceRepaint="true"/>
	<agg:param name="bgColor" description="Background color" type="C" cssAttributes="fill" classes="bgColor" forceRepaint="true"/>
	<agg:param name="level" description="Level" type="level" attributes="height" min="44" max="0" ids="liquidLevel"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.liquidColor {
		fill:#004A93
	}

	.bgColor {
		fill:#A1BFE2
	}
	
	.stroked,.liquidTexture{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		fill:none;
	}

	.stroked{
		stroke: #000000;
	}
	
	.liquidTexture{
		stroke: white;
	}

      ]]>
</style>
	 
<defs>
	<path id="maskPath" d="M0.261,0.261h9.478
		v2.288H0.261V0.261z M0.261,41.451h9.478v2.288H0.261V41.451z M0.261,36.874h9.478v2.288H0.261V36.874z M0.261,32.298h9.478v2.288
		H0.261V32.298z M0.261,27.72h9.478v2.289H0.261V27.72z M0.261,23.144h9.478v2.288H0.261V23.144z M0.261,18.569h9.478v2.287H0.261
		V18.569z M0.261,13.991h9.478v2.288H0.261V13.991z M0.261,9.414h9.478v2.288H0.261V9.414z M0.261,4.837h9.478v2.289H0.261V4.837z"/>
</defs>

<clipPath id="mask">
	<use xlink:href="#maskPath"  overflow="visible"/>
</clipPath>

<g clip-path="url(#mask)" >
	<rect x="0" y="0" class="liquidColor"  />
	<rect id="liquidLevel" x="0" y="0" class="bgColor"  />	
</g>

<use xlink:href="#maskPath" class="stroked"/>

</svg>
