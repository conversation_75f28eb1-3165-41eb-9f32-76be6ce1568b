<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param name="liquidColor" description="Liquid color" type="C" cssAttributes="fill" classes="liquidColor" forceRepaint="true"/>
	<agg:param name="bgColor" description="Background color" type="C" cssAttributes="fill" classes="bgColor" forceRepaint="true"/>
	<agg:param name="level" description="Level" type="level" attributes="height" min="44" max="0" ids="liquidLevel"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.liquidColor {
		fill:#004A93
	}

	.bgColor {
		fill:#A1BFE2
	}
	
	.stroked,.liquidTexture{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		fill:none;
	}

	.stroked{
		stroke: #000000;
	}
	
	.liquidTexture{
		stroke: white;
	}

      ]]>
</style>
	 
<defs>
	<path id="maskPath" d="M0.274,40.383
		h10.452v1.113H0.274V40.383z M0.274,38.155h10.452v1.113H0.274V38.155z M0.274,42.611h10.452v1.114H0.274V42.611z M0.274,35.925
		h10.452v1.115H0.274V35.925z M0.274,33.699h10.452v1.113H0.274V33.699z M0.274,31.471h10.452v1.112H0.274V31.471z M0.274,29.242
		h10.452v1.115H0.274V29.242z M0.274,27.014h10.452v1.114H0.274V27.014z M0.274,24.785h10.452V25.9H0.274V24.785z M0.274,22.558
		h10.452v1.114H0.274V22.558z M0.274,20.327h10.452v1.115H0.274V20.327z M0.274,18.1h10.452v1.114H0.274V18.1z M0.274,15.872h10.452
		v1.113H0.274V15.872z M0.274,13.644h10.452v1.114H0.274V13.644z M0.274,11.416h10.452v1.114H0.274V11.416z M0.274,9.187h10.452
		v1.114H0.274V9.187z M0.274,6.958h10.452v1.115H0.274V6.958z M0.274,4.731h10.452v1.114H0.274V4.731z M0.274,2.502h10.452v1.114
		H0.274V2.502z M0.274,0.274h10.452v1.115H0.274V0.274z"/>
</defs>

<clipPath id="mask">
	<use xlink:href="#maskPath"  overflow="visible"/>
</clipPath>

<g clip-path="url(#mask)" >
	<rect x="0" y="0" class="liquidColor"  />
	<rect id="liquidLevel" x="0" y="0" class="bgColor"  />	
</g>

<use xlink:href="#maskPath" class="stroked"/>

</svg>
