<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param name="liquidColor" description="Liquid color" type="C" cssAttributes="fill" classes="liquidColor" forceRepaint="true"/>
	<agg:param name="bgColor" description="Background color" type="C" cssAttributes="fill" classes="bgColor" forceRepaint="true"/>
	<agg:param name="level" description="Level" type="level" attributes="height" min="44" max="0" ids="liquidLevel"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.liquidColor {
		fill:#004A93
	}

	.bgColor {
		fill:#A1BFE2
	}
	
	.stroked,.liquidTexture{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		fill:none;
	}

	.stroked{
		stroke: #000000;
	}
	
	.liquidTexture{
		stroke: white;
	}

      ]]>
</style>
	 
<defs>
	<path id="maskPath" d="M17.926,39.788
		c1.085,0,1.963,0.887,1.963,1.977c0,1.093-0.878,1.978-1.963,1.978c-1.083,0-1.963-0.885-1.963-1.978
		C15.963,40.675,16.843,39.788,17.926,39.788z M10.073,39.788c1.084,0,1.963,0.887,1.963,1.977c0,1.093-0.879,1.978-1.963,1.978
		c-1.085,0-1.963-0.885-1.963-1.978C8.111,40.675,8.989,39.788,10.073,39.788z M25.78,31.883c1.083,0,1.963,0.884,1.963,1.977
		c0,1.092-0.88,1.977-1.963,1.977c-1.085,0-1.964-0.885-1.964-1.977C23.816,32.767,24.695,31.883,25.78,31.883z M21.853,35.836
		c1.084,0,1.963,0.885,1.963,1.978c0,1.091-0.879,1.975-1.963,1.975c-1.085,0-1.964-0.884-1.964-1.975
		C19.889,36.721,20.768,35.836,21.853,35.836z M17.926,31.883c1.085,0,1.963,0.884,1.963,1.977c0,1.092-0.878,1.977-1.963,1.977
		c-1.083,0-1.963-0.885-1.963-1.977C15.963,32.767,16.843,31.883,17.926,31.883z M14,35.836c1.085,0,1.963,0.885,1.963,1.978
		c0,1.091-0.878,1.975-1.963,1.975c-1.084,0-1.962-0.884-1.962-1.975C12.038,36.721,12.916,35.836,14,35.836z M6.148,35.836
		c1.084,0,1.963,0.885,1.963,1.978c0,1.091-0.879,1.975-1.963,1.975c-1.084,0-1.964-0.884-1.964-1.975
		C4.183,36.721,5.063,35.836,6.148,35.836z M10.073,31.883c1.084,0,1.963,0.884,1.963,1.977c0,1.092-0.879,1.977-1.963,1.977
		c-1.085,0-1.963-0.885-1.963-1.977C8.111,32.767,8.989,31.883,10.073,31.883z M2.221,31.883c1.083,0,1.962,0.884,1.962,1.977
		c0,1.092-0.879,1.977-1.962,1.977c-1.084,0-1.963-0.885-1.963-1.977C0.258,32.767,1.137,31.883,2.221,31.883z M25.78,23.977
		c1.083,0,1.963,0.885,1.963,1.977c0,1.091-0.88,1.975-1.963,1.975c-1.085,0-1.964-0.884-1.964-1.975
		C23.816,24.861,24.695,23.977,25.78,23.977z M21.853,27.928c1.084,0,1.963,0.887,1.963,1.977c0,1.093-0.879,1.978-1.963,1.978
		c-1.085,0-1.964-0.885-1.964-1.978C19.889,28.814,20.768,27.928,21.853,27.928z M17.926,23.977c1.085,0,1.963,0.885,1.963,1.977
		c0,1.091-0.878,1.975-1.963,1.975c-1.083,0-1.963-0.884-1.963-1.975C15.963,24.861,16.843,23.977,17.926,23.977z M14,27.928
		c1.085,0,1.963,0.887,1.963,1.977c0,1.093-0.878,1.978-1.963,1.978c-1.084,0-1.962-0.885-1.962-1.978
		C12.038,28.814,12.916,27.928,14,27.928z M6.148,27.928c1.084,0,1.963,0.887,1.963,1.977c0,1.093-0.879,1.978-1.963,1.978
		c-1.084,0-1.964-0.885-1.964-1.978C4.183,28.814,5.063,27.928,6.148,27.928z M10.073,23.977c1.084,0,1.963,0.885,1.963,1.977
		c0,1.091-0.879,1.975-1.963,1.975c-1.085,0-1.963-0.884-1.963-1.975C8.111,24.861,8.989,23.977,10.073,23.977z M2.221,23.977
		c1.083,0,1.962,0.885,1.962,1.977c0,1.091-0.879,1.975-1.962,1.975c-1.084,0-1.963-0.884-1.963-1.975
		C0.258,24.861,1.137,23.977,2.221,23.977z M25.78,16.07c1.083,0,1.963,0.885,1.963,1.977c0,1.092-0.88,1.976-1.963,1.976
		c-1.085,0-1.964-0.884-1.964-1.976C23.816,16.956,24.695,16.07,25.78,16.07z M21.853,20.023c1.084,0,1.963,0.885,1.963,1.977
		s-0.879,1.977-1.963,1.977c-1.085,0-1.964-0.885-1.964-1.977S20.768,20.023,21.853,20.023z M17.926,16.07
		c1.085,0,1.963,0.885,1.963,1.977c0,1.092-0.878,1.976-1.963,1.976c-1.083,0-1.963-0.884-1.963-1.976
		C15.963,16.956,16.843,16.07,17.926,16.07z M14,20.023c1.085,0,1.963,0.885,1.963,1.977S15.085,23.977,14,23.977
		c-1.084,0-1.962-0.885-1.962-1.977S12.916,20.023,14,20.023z M6.148,20.023c1.084,0,1.963,0.885,1.963,1.977
		s-0.879,1.977-1.963,1.977c-1.084,0-1.964-0.885-1.964-1.977S5.063,20.023,6.148,20.023z M10.073,16.07
		c1.084,0,1.963,0.885,1.963,1.977c0,1.092-0.879,1.976-1.963,1.976c-1.085,0-1.963-0.884-1.963-1.976
		C8.111,16.956,8.989,16.07,10.073,16.07z M2.221,16.07c1.083,0,1.962,0.885,1.962,1.977c0,1.092-0.879,1.976-1.962,1.976
		c-1.084,0-1.963-0.884-1.963-1.976C0.258,16.956,1.137,16.07,2.221,16.07z M25.78,8.163c1.083,0,1.963,0.886,1.963,1.977
		s-0.88,1.977-1.963,1.977c-1.085,0-1.964-0.885-1.964-1.977S24.695,8.163,25.78,8.163z M21.853,12.118
		c1.084,0,1.963,0.884,1.963,1.977c0,1.091-0.879,1.976-1.963,1.976c-1.085,0-1.964-0.885-1.964-1.976
		C19.889,13.002,20.768,12.118,21.853,12.118z M17.926,8.163c1.085,0,1.963,0.886,1.963,1.977s-0.878,1.977-1.963,1.977
		c-1.083,0-1.963-0.885-1.963-1.977S16.843,8.163,17.926,8.163z M14,12.118c1.085,0,1.963,0.884,1.963,1.977
		c0,1.091-0.878,1.976-1.963,1.976c-1.084,0-1.962-0.885-1.962-1.976C12.038,13.002,12.916,12.118,14,12.118z M6.148,12.118
		c1.084,0,1.963,0.884,1.963,1.977c0,1.091-0.879,1.976-1.963,1.976c-1.084,0-1.964-0.885-1.964-1.976
		C4.183,13.002,5.063,12.118,6.148,12.118z M10.073,8.163c1.084,0,1.963,0.886,1.963,1.977s-0.879,1.977-1.963,1.977
		c-1.085,0-1.963-0.885-1.963-1.977S8.989,8.163,10.073,8.163z M2.221,8.163c1.083,0,1.962,0.886,1.962,1.977
		s-0.879,1.977-1.962,1.977c-1.084,0-1.963-0.885-1.963-1.977S1.137,8.163,2.221,8.163z M21.853,4.211
		c1.084,0,1.963,0.885,1.963,1.977c0,1.092-0.879,1.977-1.963,1.977c-1.085,0-1.964-0.885-1.964-1.977
		C19.889,5.097,20.768,4.211,21.853,4.211z M17.926,0.258c1.085,0,1.963,0.884,1.963,1.977c0,1.091-0.878,1.976-1.963,1.976
		c-1.083,0-1.963-0.885-1.963-1.976C15.963,1.142,16.843,0.258,17.926,0.258z M14,4.211c1.085,0,1.963,0.885,1.963,1.977
		c0,1.092-0.878,1.977-1.963,1.977c-1.084,0-1.962-0.885-1.962-1.977C12.038,5.097,12.916,4.211,14,4.211z M6.148,4.211
		c1.084,0,1.963,0.885,1.963,1.977c0,1.092-0.879,1.977-1.963,1.977c-1.084,0-1.964-0.885-1.964-1.977
		C4.183,5.097,5.063,4.211,6.148,4.211z M10.073,0.258c1.084,0,1.963,0.884,1.963,1.977c0,1.091-0.879,1.976-1.963,1.976
		c-1.085,0-1.963-0.885-1.963-1.976C8.111,1.142,8.989,0.258,10.073,0.258z"/>
</defs>

<clipPath id="mask">
	<use xlink:href="#maskPath"  overflow="visible"/>
</clipPath>

<g clip-path="url(#mask)" >
	<rect x="0" y="0" class="liquidColor"  />
	<rect id="liquidLevel" x="0" y="0" class="bgColor"  />	
</g>

<use xlink:href="#maskPath" class="stroked"/>

</svg>
