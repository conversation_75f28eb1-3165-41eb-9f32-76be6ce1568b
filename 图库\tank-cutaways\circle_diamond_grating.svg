<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param name="liquidColor" description="Liquid color" type="C" cssAttributes="fill" classes="liquidColor" forceRepaint="true"/>
	<agg:param name="bgColor" description="Background color" type="C" cssAttributes="fill" classes="bgColor" forceRepaint="true"/>
	<agg:param name="level" description="Level" type="level" attributes="height" min="44" max="0" ids="liquidLevel"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.liquidColor {
		fill:#004A93
	}

	.bgColor {
		fill:#A1BFE2
	}
	
	.stroked,.liquidTexture{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		fill:none;
	}

	.stroked{
		stroke: #000000;
	}
	
	.liquidTexture{
		stroke: white;
	}

      ]]>
</style>
	 
<defs>
	<path id="maskPath" d="M0.261,41.142v2.598
		h2.642L0.261,41.142z M0.261,33.895v5.197l2.642-2.599L0.261,33.895z M0.261,26.648v5.197l2.642-2.599L0.261,26.648z M0.261,19.402
		v5.196L2.903,22L0.261,19.402z M0.261,12.156v5.196l2.642-2.598L0.261,12.156z M0.261,4.909v5.197l2.642-2.599L0.261,4.909z
		 M27.097,22l2.643,2.598v-5.196L27.097,22z M7.631,41.142l-2.642,2.598h5.284L7.631,41.142z M29.739,2.859V0.261h-2.643
		L29.739,2.859z M15,41.142l-2.643,2.598h5.285L15,41.142z M2.903,0.261H0.261v2.598L2.903,0.261z M22.37,41.142l-2.644,2.598h5.285
		L22.37,41.142z M27.097,43.739h2.643v-2.598L27.097,43.739z M27.097,36.493l2.643,2.599v-5.197L27.097,36.493z M29.739,10.106V4.909
		l-2.643,2.598L29.739,10.106z M25.012,0.261h-5.285l2.644,2.598L25.012,0.261z M17.643,0.261h-5.285L15,2.859L17.643,0.261z
		 M7.631,2.859l2.642-2.598H4.989L7.631,2.859z M29.739,17.352v-5.196l-2.643,2.598L29.739,17.352z M29.739,31.846v-5.197
		l-2.643,2.599L29.739,31.846z M23.412,40.116l2.642-2.599l2.643,2.599l-2.643,2.599L23.412,40.116z M16.042,40.116l2.642-2.599
		l2.644,2.599l-2.644,2.599L16.042,40.116z M8.672,40.116l2.643-2.599l2.643,2.599l-2.643,2.599L8.672,40.116z M1.303,40.116
		l2.643-2.599l2.642,2.599l-2.642,2.599L1.303,40.116z M19.726,36.493l2.645-2.599l2.642,2.599l-2.642,2.599L19.726,36.493z
		 M12.357,36.493L15,33.895l2.643,2.599L15,39.092L12.357,36.493z M4.988,36.493l2.643-2.599l2.643,2.599L7.63,39.092L4.988,36.493z
		 M23.411,32.872l2.643-2.599l2.643,2.599l-2.644,2.598L23.411,32.872z M16.041,32.872l2.643-2.6l2.644,2.6l-2.646,2.598
		L16.041,32.872z M8.672,32.871l2.643-2.599l2.643,2.6l-2.643,2.598L8.672,32.871z M1.302,32.87l2.643-2.599l2.642,2.601L3.944,35.47
		L1.302,32.87z M19.726,29.247l2.645-2.599l2.642,2.599l-2.642,2.599L19.726,29.247z M12.357,29.247L15,26.648l2.643,2.599L15,31.846
		L12.357,29.247z M4.988,29.247l2.643-2.599l2.643,2.599L7.63,31.846L4.988,29.247z M23.412,25.623l2.642-2.598l2.643,2.598
		l-2.643,2.599L23.412,25.623z M16.042,25.623l2.642-2.598l2.644,2.598l-2.644,2.599L16.042,25.623z M8.672,25.623l2.643-2.598
		l2.643,2.598l-2.643,2.599L8.672,25.623z M1.304,25.623l2.643-2.598l2.642,2.598l-2.642,2.599L1.304,25.623z M19.726,22.001
		l2.645-2.598l2.642,2.599l-2.643,2.598L19.726,22.001z M12.357,22.001L15,19.403l2.642,2.599L15,24.601L12.357,22.001z
		 M4.987,22.001l2.643-2.598l2.642,2.599L7.63,24.601L4.987,22.001z M23.412,18.377l2.642-2.599l2.643,2.599l-2.643,2.599
		L23.412,18.377z M16.042,18.377l2.642-2.599l2.644,2.599l-2.644,2.599L16.042,18.377z M8.673,18.377l2.643-2.599l2.642,2.599
		l-2.642,2.599L8.673,18.377z M1.304,18.377l2.643-2.599l2.642,2.599l-2.642,2.599L1.304,18.377z M19.726,14.754l2.645-2.599
		l2.642,2.599l-2.642,2.598L19.726,14.754z M12.357,14.754L15,12.156l2.642,2.599L15,17.353L12.357,14.754z M4.988,14.754
		l2.643-2.599l2.642,2.599l-2.642,2.598L4.988,14.754z M23.413,11.132l2.641-2.599l2.643,2.599l-2.643,2.599L23.413,11.132z
		 M16.043,11.132l2.641-2.599l2.644,2.6l-2.644,2.599L16.043,11.132z M8.672,11.131l2.644-2.599l2.642,2.599l-2.643,2.599
		L8.672,11.131z M1.303,11.131l2.643-2.599l2.642,2.6L3.945,13.73L1.303,11.131z M19.727,7.507l2.644-2.599l2.642,2.599l-2.642,2.599
		L19.727,7.507z M12.357,7.507L15,4.909l2.642,2.599L15,10.106L12.357,7.507z M4.988,7.507l2.643-2.599l2.643,2.599l-2.643,2.599
		L4.988,7.507z M23.413,3.884l2.641-2.599l2.643,2.599l-2.643,2.599L23.413,3.884z M16.043,3.884l2.641-2.599l2.644,2.599
		l-2.644,2.599L16.043,3.884z M8.673,3.884l2.643-2.599l2.643,2.599l-2.643,2.599L8.673,3.884z M1.304,3.884l2.643-2.599l2.642,2.599
		L3.946,6.483L1.304,3.884z"/>
</defs>

<clipPath id="mask">
	<use xlink:href="#maskPath"  overflow="visible"/>
</clipPath>

<g clip-path="url(#mask)" >
	<rect x="0" y="0" class="liquidColor"  />
	<rect id="liquidLevel" x="0" y="0" class="bgColor"  />	
</g>

<use xlink:href="#maskPath" class="stroked"/>

</svg>
