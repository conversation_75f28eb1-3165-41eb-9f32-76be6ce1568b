<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param name="liquidColor" description="Liquid color" type="C" cssAttributes="fill" classes="liquidColor" forceRepaint="true"/>
	<agg:param name="bgColor" description="Background color" type="C" cssAttributes="fill" classes="bgColor" forceRepaint="true"/>
	<agg:param name="level" description="Level" type="level" attributes="height" min="44" max="0" ids="liquidLevel"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.liquidColor {
		fill:#004A93
	}

	.bgColor {
		fill:#A1BFE2
	}
	
	.stroked,.liquidTexture{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		fill:none;
	}

	.stroked{
		stroke: #000000;
	}
	
	.liquidTexture{
		stroke: white;
	}

      ]]>
</style>
	 
<defs>
	<polygon id="maskPath" points="
		33.711,43.747 40.404,39.397 33.711,35.048 43.75,30.698 33.711,26.348 40.404,21.999 30.366,17.648 40.404,13.299 33.711,8.949 
		40.404,4.599 37.059,0.25 13.635,0.25 3.596,4.599 10.288,8.949 0.25,13.299 6.941,17.648 0.25,21.999 10.288,26.348 3.596,30.698 
		10.288,35.048 3.596,39.397 13.635,43.747 "/>
</defs>

<clipPath id="mask">
	<use xlink:href="#maskPath"  overflow="visible"/>
</clipPath>

<g clip-path="url(#mask)" >
	<rect x="0" y="0" class="liquidColor"  />
	
	<g class="liquidTexture">
		<line x1="0.029" y1="0.018" x2="5.61" y2="0.018"/>
		<line x1="0.029" y1="2.768" x2="1.663" y2="2.768"/>
		<line x1="0.029" y1="8.264" x2="1.663" y2="8.264"/>
		<line x1="0.029" y1="13.762" x2="3.636" y2="13.762"/>
		<line x1="0.029" y1="16.512" x2="1.663" y2="16.512"/>
		<line x1="0.029" y1="19.259" x2="5.61" y2="19.259"/>
		<line x1="0.029" y1="22.009" x2="1.663" y2="22.009"/>
		<line x1="5.61" y1="24.757" x2="0.029" y2="24.757"/>
		<line x1="1.663" y1="27.507" x2="0.029" y2="27.507"/>
		<line x1="0.029" y1="35.751" x2="5.61" y2="35.751"/>
		<line x1="1.663" y1="38.502" x2="0.029" y2="38.502"/>
		<line x1="5.61" y1="41.251" x2="0.029" y2="41.251"/>
		<line x1="24.184" y1="13.762" x2="17.448" y2="13.762"/>
		<line x1="3.636" y1="43.997" x2="0.029" y2="43.997"/>
		<line x1="22.498" y1="43.997" x2="17.448" y2="43.997"/>
		<line x1="13.501" y1="43.997" x2="7.583" y2="43.997"/>
		<line x1="15.474" y1="38.502" x2="23.063" y2="38.502"/>
		<line x1="22.829" y1="27.507" x2="15.474" y2="27.507"/>
		<line x1="15.474" y1="22.009" x2="22.498" y2="22.009"/>
		<line x1="22.21" y1="16.512" x2="15.474" y2="16.512"/>
		<line x1="23.473" y1="8.264" x2="15.474" y2="8.264"/>
		<line x1="23.197" y1="2.768" x2="15.474" y2="2.768"/>
		<line x1="15.474" y1="41.251" x2="9.556" y2="41.251"/>
		<line x1="5.61" y1="38.502" x2="11.528" y2="38.502"/>
		<line x1="9.556" y1="35.751" x2="15.474" y2="35.751"/>
		<line x1="9.556" y1="33.003" x2="3.636" y2="33.003"/>
		<line x1="13.501" y1="33.003" x2="19.42" y2="33.003"/>
		<line x1="1.663" y1="30.254" x2="7.583" y2="30.254"/>
		<line x1="11.528" y1="30.254" x2="17.448" y2="30.254"/>
		<line x1="11.528" y1="27.507" x2="5.61" y2="27.507"/>
		<line x1="9.556" y1="24.757" x2="15.474" y2="24.757"/>
		<line x1="5.61" y1="22.009" x2="11.528" y2="22.009"/>
		<line x1="15.474" y1="19.259" x2="9.556" y2="19.259"/>
		<line x1="11.528" y1="16.512" x2="5.61" y2="16.512"/>
		<line x1="13.501" y1="13.762" x2="7.583" y2="13.762"/>
		<line x1="7.583" y1="11.015" x2="1.663" y2="11.015"/>
		<line x1="17.448" y1="11.015" x2="11.528" y2="11.015"/>
		<line x1="11.528" y1="8.264" x2="5.61" y2="8.264"/>
		<line x1="7.583" y1="5.517" x2="1.663" y2="5.517"/>
		<line x1="17.448" y1="5.517" x2="11.528" y2="5.517"/>
		<line x1="11.528" y1="2.768" x2="5.61" y2="2.768"/>
		<line x1="9.556" y1="0.018" x2="15.474" y2="0.018"/>
		<line x1="21.393" y1="0.018" x2="26.974" y2="0.018"/>
		<line x1="19.973" y1="19.259" x2="26.974" y2="19.259"/>
		<line x1="26.974" y1="24.757" x2="19.42" y2="24.757"/>
		<line x1="19.151" y1="35.751" x2="26.974" y2="35.751"/>
		<line x1="26.974" y1="41.251" x2="19.42" y2="41.251"/>
		<line x1="40.784" y1="0.018" x2="43.862" y2="0.018"/>
		<line x1="43.862" y1="5.517" x2="42.759" y2="5.517"/>
		<line x1="43.862" y1="11.015" x2="42.759" y2="11.015"/>
		<line x1="43.862" y1="13.762" x2="38.813" y2="13.762"/>
		<line x1="43.862" y1="19.259" x2="40.784" y2="19.259"/>
		<line x1="40.784" y1="24.757" x2="43.862" y2="24.757"/>
		<line x1="42.759" y1="30.254" x2="43.862" y2="30.254"/>
		<line x1="40.784" y1="35.751" x2="43.862" y2="35.751"/>
		<line x1="43.862" y1="41.251" x2="40.784" y2="41.251"/>
		<line x1="43.862" y1="43.997" x2="38.813" y2="43.997"/>
		<line x1="34.866" y1="43.997" x2="28.948" y2="43.997"/>
		<line x1="36.838" y1="38.502" x2="42.759" y2="38.502"/>
		<line x1="42.759" y1="27.507" x2="36.838" y2="27.507"/>
		<line x1="36.838" y1="22.009" x2="42.759" y2="22.009"/>
		<line x1="42.759" y1="16.512" x2="36.838" y2="16.512"/>
		<line x1="42.759" y1="8.264" x2="36.838" y2="8.264"/>
		<line x1="42.759" y1="2.768" x2="36.838" y2="2.768"/>
		<line x1="36.838" y1="41.251" x2="30.921" y2="41.251"/>
		<line x1="26.974" y1="38.502" x2="32.893" y2="38.502"/>
		<line x1="30.921" y1="35.751" x2="36.838" y2="35.751"/>
		<line x1="30.921" y1="33.003" x2="25.001" y2="33.003"/>
		<line x1="34.866" y1="33.003" x2="40.784" y2="33.003"/>
		<line x1="23.027" y1="30.254" x2="28.948" y2="30.254"/>
		<line x1="32.893" y1="30.254" x2="38.813" y2="30.254"/>
		<line x1="32.893" y1="27.507" x2="26.974" y2="27.507"/>
		<line x1="30.921" y1="24.757" x2="36.838" y2="24.757"/>
		<line x1="26.974" y1="22.009" x2="32.893" y2="22.009"/>
		<line x1="36.838" y1="19.259" x2="30.921" y2="19.259"/>
		<line x1="32.893" y1="16.512" x2="26.974" y2="16.512"/>
		<line x1="34.866" y1="13.762" x2="28.948" y2="13.762"/>
		<line x1="28.948" y1="11.015" x2="23.027" y2="11.015"/>
		<line x1="38.813" y1="11.015" x2="32.893" y2="11.015"/>
		<line x1="32.893" y1="8.264" x2="26.974" y2="8.264"/>
		<line x1="28.948" y1="5.517" x2="23.027" y2="5.517"/>
		<line x1="38.813" y1="5.517" x2="32.893" y2="5.517"/>
		<line x1="32.893" y1="2.768" x2="26.974" y2="2.768"/>
		<line x1="30.921" y1="0.018" x2="36.838" y2="0.018"/>
	</g>
	
	<rect id="liquidLevel" x="0" y="0" class="bgColor"  />	
</g>

<use xlink:href="#maskPath" class="stroked"/>

</svg>
