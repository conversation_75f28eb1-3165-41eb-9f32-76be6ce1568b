<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:agg="http://www.example.com" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="-1 -1 112 152" xml:space="preserve">

<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>
	 
<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.5px;
		opacity:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>
	 
<defs>
	<linearGradient id="horizontalGradient" gradientUnits="userSpaceOnUse" x1="5" y1="100" x2="145" y2="100">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	
	<linearGradient id="flangeGradient" xlink:href="#horizontalGradient" gradientUnits="userSpaceOnUse" x1="0" y1="100" x2="150" y2="100"/>
	
	<linearGradient id="verticalGradient" gradientUnits="userSpaceOnUse" x1="55" y1="0" x2="55" y2="7.5">
		<stop  offset="0" style="stop-color:#999999"/>
		<stop  offset="0.2" style="stop-color:#FEFEFE"/>
		<stop  offset="1" style="stop-color:#999999"/>
	</linearGradient>
	
	<rect id="bend" fill="url(#verticalGradient)"  />
	<rect id="flange" fill="url(#flangeGradient)"  />
	
	<polygon id="boundary" points="110,10 110,0 0,0 0,10 5,10 5,45 0,45 0,52.5 5,52.5 5,97.5 0,97.5 0,105 5,105 
	5,140 0,140 0,150 110,150 110,140 105,140 105,105 110,105 110,97.5 105,97.5 105,52.5 110,52.5 110,45 105,45 105,10 	"/>
</defs> 

<rect x="5" fill="url(#horizontalGradient)"  />
<use xlink:href="#bend" y="45"/>
<use xlink:href="#bend" y="97.5"/>

<use xlink:href="#flange"/>
<use xlink:href="#flange" y="140"/>

<g class="color">
	<use xlink:href="#boundary"/>
</g>

<g class="stroke">
	<line x1="5" y1="10" x2="105" y2="10"/>
	<line x1="5" y1="45" x2="105" y2="45"/>
	<line x1="5" y1="105" x2="105" y2="105"/>
	<line x1="5" y1="97.5" x2="105" y2="97.5"/>
	<line x1="5" y1="140" x2="105" y2="140"/>
	<line x1="5" y1="52.5" x2="105" y2="52.5"/>
	<use xlink:href="#boundary"/>
</g>
</svg>
