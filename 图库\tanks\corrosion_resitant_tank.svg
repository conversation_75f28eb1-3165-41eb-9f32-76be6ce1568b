<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" 
	  viewBox="0 0 124 154" enable-background="new 0 0 124 154" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="h" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#BDBDBD"/>
		<stop  offset="0.3568" style="stop-color:#8E8D8D"/>
		<stop  offset="1" style="stop-color:#525252"/>
	</linearGradient>
	<path id="boundary" d="M110.187,20.144c-25.632-24.191-70.742-24.191-96.374,0
		c-3.362,0-11.813,0-11.813,0V152h120V20.144C122,20.144,116.212,20.144,110.187,20.144z"/>
</defs>

<linearGradient id="h1" xlink:href="#h" gradientUnits="userSpaceOnUse" x1="34.8882" y1="146.0024" x2="7.1387" y2="146.0024"/>
<path fill="url(#h1)" d="M34.26,152c-8.488,0-16.976,0-25.464,0c0.976-4.021,2.03-8.019,3.151-11.995
	c2.545,0,5.091,0,7.625,0c3.118,0,6.234,0,9.34,0C30.627,144.037,32.41,148.024,34.26,152z"/>
<linearGradient id="h2" xlink:href="#h" gradientUnits="userSpaceOnUse" x1="89.7524" y1="146.0024" x2="115.2051" y2="146.0024"/>
<path fill="url(#h2)" d="M115.205,152c-8.488,0-16.965,0-25.453,0c1.838-3.976,3.622-7.963,5.337-11.995
	c5.651,0,11.313,0,16.965,0C113.175,143.981,114.229,147.979,115.205,152z"/>

<linearGradient id="v" xlink:href="#h" gradientUnits="userSpaceOnUse" x1="115.0469" y1="45.0298" x2="115.0469" y2="-2.0095"/>

<path fill="url(#v)" d="M110.193,20.144c3.352,0,6.705,0,10.058,0c-0.796,8.031-1.861,16.016-3.252,23.979
	C110.742,37.783,109.004,28.723,110.193,20.144z"/>
<path fill="url(#v)" d="M13.819,20.144c1.189,8.579-0.561,17.64-6.806,23.979C5.623,36.16,4.546,28.174,3.75,20.144
	C7.103,20.144,10.455,20.144,13.819,20.144z"/>
	
<radialGradient id="sph" cx="-3.6099" cy="-11.2842" r="134.035" gradientTransform="matrix(1.0012 0 0 1 -9.460449e-004 0)" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#777777"/>
	<stop  offset="0.0577" style="stop-color:#979696"/>
	<stop  offset="0.1154" style="stop-color:#B5B4B4"/>
	<stop  offset="0.1709" style="stop-color:#CDCCCC"/>
	<stop  offset="0.2225" style="stop-color:#DDDEDE"/>
	<stop  offset="0.2667" style="stop-color:#E4E4E4"/>
	<stop  offset="0.6606" style="stop-color:#B1AFAF"/>
	<stop  offset="0.7867" style="stop-color:#B2B1B1"/>
	<stop  offset="0.8506" style="stop-color:#B9B7B7"/>
	<stop  offset="0.9004" style="stop-color:#C4C2C2"/>
	<stop  offset="0.9431" style="stop-color:#D5D3D3"/>
	<stop  offset="0.9807" style="stop-color:#ECEBEA"/>
	<stop  offset="1" style="stop-color:#FFFFFF"/>
</radialGradient>

<path fill="url(#sph)" d="M116.993,44.123c-12.334,0-24.668,0-37.002,0c-3.196-7.997-6.392-15.982-9.599-23.979
	c-5.595,0-11.201,0-16.796,0c-3.196,7.997-6.402,15.982-9.599,23.979c-12.334,0-24.668,0-36.991,0
	c6.246-6.339,7.995-15.4,6.806-23.979c25.632-24.191,70.742-24.191,96.374,0C108.999,28.723,110.736,37.783,116.993,44.123z"/>
	
<linearGradient id="body" gradientUnits="userSpaceOnUse" x1="2.0005" y1="86.0718" x2="122" y2="86.0718">
	<stop  offset="0" style="stop-color:#777777"/>
	<stop  offset="0.0577" style="stop-color:#979696"/>
	<stop  offset="0.1154" style="stop-color:#B5B4B4"/>
	<stop  offset="0.1709" style="stop-color:#CDCCCC"/>
	<stop  offset="0.2225" style="stop-color:#DDDEDE"/>
	<stop  offset="0.2667" style="stop-color:#E4E4E4"/>
	<stop  offset="0.6606" style="stop-color:#B1AFAF"/>
	<stop  offset="0.7867" style="stop-color:#B2B1B1"/>
	<stop  offset="0.8506" style="stop-color:#B9B7B7"/>
	<stop  offset="0.9004" style="stop-color:#C4C2C2"/>
	<stop  offset="0.9431" style="stop-color:#D5D3D3"/>
	<stop  offset="0.9807" style="stop-color:#ECEBEA"/>
	<stop  offset="1" style="stop-color:#FFFFFF"/>
</linearGradient>

<path fill="url(#body)" d="M122,20.144c0,43.949,0,87.897,0,131.856c-2.265,0-4.53,0-6.795,0
	c-0.976-4.021-2.03-8.019-3.15-11.995c-5.652,0-11.314,0-16.965,0c-1.716,4.032-3.499,8.02-5.337,11.995
	c-18.501,0-36.991,0-55.493,0c-1.85-3.976-3.632-7.963-5.349-11.995c-3.106,0-14.419,0-16.965,0
	c-1.121,3.977-2.175,7.975-3.151,11.995c-2.265,0-4.53,0-6.795,0c0-43.959,0-87.908,0-131.856c0.583,0,1.167,0,1.749,0
	c0.796,8.031,1.873,16.016,3.264,23.979c12.322,0,24.656,0,36.991,0c3.196-7.997,6.402-15.982,9.598-23.979
	c5.595,0,11.202,0,16.796,0c3.207,7.997,6.402,15.982,9.599,23.979c12.334,0,24.668,0,37.002,0
	c1.391-7.963,2.456-15.948,3.252-23.979C120.834,20.144,121.417,20.144,122,20.144z"/>

<use xlink:href="#boundary" class="color"/>
<g class="stroke">
	<use xlink:href="#boundary"/>
	<path d="M3.75,20.144
		c0.796,8.031,1.873,16.016,3.264,23.979c12.322,0,24.656,0,36.991,0c3.196-7.997,6.402-15.982,9.598-23.979
		c5.595,0,11.202,0,16.796,0c3.207,7.997,6.402,15.982,9.599,23.979c12.334,0,24.668,0,37.002,0
		c1.391-7.963,2.456-15.948,3.252-23.979"/>
	<path d="M89.752,152c1.838-3.976,3.622-7.963,5.337-11.995c5.651,0,11.313,0,16.965,0c1.121,3.977,2.175,7.975,3.15,11.995"/>
	<path d="M116.999,44.123c-6.257-6.339-7.995-15.4-6.806-23.979"/>
	<path d="M7.013,44.123c6.245-6.339,7.995-15.4,6.806-23.979"/>
	<path d="M8.795,152
		c0.976-4.021,2.03-8.019,3.151-11.995c2.545,0,5.091,0,7.625,0c3.118,0,6.234,0,9.34,0c1.716,4.032,3.499,8.02,5.349,11.995"/>
</g>
</svg>
