<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 37 82" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>	 
	<linearGradient id="baseGradient" gradientUnits="userSpaceOnUse" x1="1" y1="50" x2="36" y2="50">
		<stop  offset="0" style="stop-color:#A0A0A0"/>
		<stop  offset="0.0498" style="stop-color:#B5B4B4"/>
		<stop  offset="0.1132" style="stop-color:#CAC9C9"/>
		<stop  offset="0.1802" style="stop-color:#D9D9D9"/>
		<stop  offset="0.2525" style="stop-color:#E4E4E3"/>
		<stop  offset="0.3394" style="stop-color:#E7E7E7"/>
		<stop  offset="0.4572" style="stop-color:#DBDADA"/>
		<stop  offset="0.6647" style="stop-color:#BEBDBC"/>
		<stop  offset="0.936" style="stop-color:#959494"/>
		<stop  offset="1" style="stop-color:#8B8B8B"/>
	</linearGradient>
	
	<linearGradient id="bottomGradient" xlink:href="#baseGradient" gradientUnits="userSpaceOnUse" x1="5.375" y1="50" x2="31.625" y2="50"/>
	
	<path id="boundary" d="M31.75,10.24c-2.65-2.4-5.48-3.63-8.87-4.31V1h-8.75v4.93c-3.39,0.68-6.22,1.91-8.87,4.31C2.77,12.49,1,15.61,1,19.18v49.19
	c0,2.51,1.96,4.54,4.38,4.54V81h26.25v-8.09c2.42,0,4.37-2.03,4.37-4.54V19.18C36,15.61,34.24,12.49,31.75,10.24z M14.38,79H10
	c-1.19,0-1.62-0.97-1.62-2.16c0-1.2,0.43-2.17,1.62-2.17h4.38c1.2,0,2.17,0.97,2.17,2.17C16.55,78.03,15.58,79,14.38,79z M27,79.12
	h-4.37c-1.2,0-2.17-0.97-2.17-2.16c0-1.2,0.97-2.17,2.17-2.17H27c1.2,0,1.63,0.97,1.63,2.17C28.63,78.15,28.2,79.12,27,79.12z"/>
</defs>

<path id="body" fill="url(#baseGradient)" d="M31.625,72.91C34.041,72.909,36,70.874,36,68.363V19.182c0-3.576-1.764-6.689-4.253-8.945
	c-2.647-2.4-5.478-3.625-8.872-4.311V1h-8.75v4.926C10.73,6.611,7.9,7.836,5.253,10.236C2.764,12.492,1,15.605,1,19.182v49.182
	c0,2.511,1.959,4.546,4.375,4.546L31.625,72.91z"/>
<path id="bottom" fill="url(#bottomGradient)" d="M5.375,72.91V81h26.25v-8.09H5.375z M14.375,79H10c-1.197,0-1.625-0.97-1.625-2.167
	S8.803,74.666,10,74.666h4.375c1.197,0,2.167,0.97,2.167,2.167S15.572,79,14.375,79z M27,79.122h-4.375
	c-1.197,0-2.166-0.97-2.166-2.167s0.969-2.167,2.166-2.167H27c1.197,0,1.625,0.97,1.625,2.167S28.197,79.122,27,79.122z"/>

<use xlink:href="#boundary" class="color"/>

<g class="stroke">
	<path d="M14.125,5.979c0,1.006,0.988,1.67,1.718,1.989c0.868,0.38,1.726,0.515,2.657,0.515s1.789-0.135,2.657-0.515c0.729-0.319,1.718-0.983,1.718-1.989"/>
	<line x1="5.375" y1="72.91" x2="31.625" y2="72.91"/>
	<line x1="4" y1="19.18" x2="33" y2="19.182"/>
	<use xlink:href="#boundary"/>
</g>

</svg>
