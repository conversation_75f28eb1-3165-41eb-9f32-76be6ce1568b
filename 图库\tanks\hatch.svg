<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   x="0px" y="0px"
	   viewBox="0 0 50 50" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke {
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}
	
      ]]>
</style>
	 
<linearGradient id="flange" gradientUnits="userSpaceOnUse" x1="4.4536" y1="44.4229" x2="44.0037" y2="7.0364">
	<stop  offset="0.0051" style="stop-color:#E1EAF7"/>
	<stop  offset="0.1237" style="stop-color:#D0DAE7"/>
	<stop  offset="0.3583" style="stop-color:#A8B4C7"/>
	<stop  offset="0.5152" style="stop-color:#8D9BB2"/>
	<stop  offset="0.5394" style="stop-color:#9FABBD"/>
	<stop  offset="0.9997" style="stop-color:#F6FAFC"/>
</linearGradient>

<circle fill="url(#flange)" cx="25" cy="25" r="25"/>

<g fill="#787878" opacity="0.7" >
	<polygon points="43.529,14.301 41.285,13.006 39.044,14.301 39.044,16.891 41.285,18.185 43.529,16.891"/>
	<polygon points="46.397,25 45.104,22.756 42.512,22.756 41.216,25 42.512,27.244 45.104,27.244"/>
	<polygon points="35.697,43.529 36.994,41.288 35.697,39.042 33.109,39.042 31.813,41.288 33.109,43.529"/>
	<polygon points="24.998,46.395 27.244,45.102 27.244,42.51 24.998,41.216 22.759,42.51 22.759,45.102"/>
	<polygon points="14.3,43.529 16.894,43.529 18.185,41.288 16.894,39.042 14.3,39.042 13.009,41.288"/>
	<polygon points="6.471,35.697 8.712,36.991 10.956,35.697 10.956,33.106 8.712,31.815 6.471,33.106"/>
	<polygon points="3.603,25 4.899,27.244 7.488,27.244 8.782,25 7.488,22.756 4.899,22.756"/>
	<polygon points="6.471,14.301 6.471,16.891 8.712,18.185 10.956,16.891 10.956,14.301 8.712,13.006"/>
	<polygon points="14.3,6.471 13.009,8.712 14.3,10.956 16.894,10.956 18.185,8.712 16.894,6.471"/>
	<polygon points="35.697,6.471 33.109,6.471 31.813,8.712 33.109,10.956 35.697,10.956 36.994,8.712"/>
	<polygon points="43.529,35.697 43.529,33.106 41.285,31.815 39.044,33.106 39.044,35.697 41.285,36.991"/>
	<polygon points="24.998,3.605 22.759,4.894 22.759,7.488 24.998,8.781 27.244,7.488 27.244,4.894"/>
</g>

<circle class="color" cx="25" cy="25" r="25"/>

</svg>
