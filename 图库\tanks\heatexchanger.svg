<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="-1px" y="-1px"  
	 viewBox="-1 -1 162 202" xml:space="preserve">
	 
<agg:params>
	<agg:param  type="C" name="color" classes="color" cssAttributes="fill" description="Color"/>
    <agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke {
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}
	
      ]]>
</style>

<defs>
	<path id="boundary" d="M160,32V17.5h-5.07C153.447,7.774,120.479,0,80,0C39.522,0,6.554,7.774,5.071,17.5H0V32h5
			v136H0v14.5h5.071C6.554,192.225,39.522,200,80,200c40.479,0,73.447-7.775,74.93-17.5H160V168h-5V32H160z"/>
</defs>

<linearGradient id="verticalGradient"  gradientUnits="userSpaceOnUse" x1="80" y1="0" x2="80" y2="200">
	<stop  offset="0" style="stop-color:#FFFFFF"/>
	<stop  offset="0.1" style="stop-color:#999999"/>
	<stop  offset="0.9" style="stop-color:#999999"/>
	<stop  offset="1" style="stop-color:#FFFFFF"/>
</linearGradient>

<linearGradient id="horizontalGradient" gradientUnits="userSpaceOnUse" x1="5" y1="100" x2="150" y2="100">
	<stop  offset="0" style="stop-color:#767676"/>
	<stop  offset="0.06" style="stop-color:#919191"/>
	<stop  offset="0.14" style="stop-color:#B1B1B1"/>
	<stop  offset="0.21" style="stop-color:#CECECE"/>
	<stop  offset="0.28" style="stop-color:#E4E4E4"/>
	<stop  offset="0.35" style="stop-color:#F6F6F6"/>
	<stop  offset="0.42" style="stop-color:#FEFEFF"/>
	<stop  offset="0.47" style="stop-color:#F6F6F6"/>
	<stop  offset="0.53" style="stop-color:#E7E7E7"/>
	<stop  offset="0.58" style="stop-color:#D2D1D1"/>
	<stop  offset="0.64" style="stop-color:#B7B7B7"/>
	<stop  offset="0.7" style="stop-color:#989898"/>
	<stop  offset="0.72" style="stop-color:#8B8B8B"/>
	<stop  offset="1" style="stop-color:#FDFDFD"/>
</linearGradient>

<linearGradient id="flangeGradient" xlink:href="#horizontalGradient" x1="0" y1="100" x2="160" y2="100"/>

<rect x="5" y="32" fill="url(#horizontalGradient)"  />

<path fill="url(#verticalGradient)" d="M154.929,17.5C153.447,7.774,120.479,0,80,0C39.523,0,6.553,7.774,5.071,17.5H154.929z"/>
<path fill="url(#verticalGradient)" d="M5.071,182.5C6.552,192.225,39.522,200,80,200c40.479,0,73.447-7.775,74.929-17.5H5.071z"/>

<rect y="17.5" fill="url(#flangeGradient)"  />
<rect y="168" fill="url(#flangeGradient)"  />

<use xlink:href="#boundary" class="color"/>

<g class="stroke">
	<use xlink:href="#boundary"/>
	<line x1="5.071" y1="17.5" x2="154.929" y2="17.5"/>
	<line x1="5" y1="32" x2="155" y2="32"/>
	<line x1="5" y1="168" x2="155" y2="168"/>
	<line x1="5.071" y1="182.5" x2="154.929" y2="182.5"/>
	<line x1="0" y1="175.25" x2="160" y2="175.25"/>
	<line x1="0" y1="24.75" x2="160" y2="24.75"/>
</g>
</svg>
