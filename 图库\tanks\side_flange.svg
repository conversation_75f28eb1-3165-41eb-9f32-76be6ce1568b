<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="0 0 48 23" enable-background="new 0 0 48 23" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke {
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}
	
      ]]>
</style>

<g>
	<linearGradient id="base" gradientUnits="userSpaceOnUse" x1="261.4697" y1="-160.0273" x2="311.1494" y2="-160.0273" gradientTransform="matrix(0.9259 0 0 -0.9259 -241.1013 -142.8716)">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	<rect x="1" y="1" fill="url(#base)"  />

	<linearGradient id="SVGID_2_" xlink:href="#base" x1="266.8877" y1="-171.3672" x2="305.7314" y2="-171.3672" gradientTransform="matrix(0.9259 0 0 -0.9259 -241.1013 -142.8716)"/>
	<rect x="6.017" y="9.604" fill="url(#SVGID_2_)"  />

	<polygon class="color" points="1,1 1,9.604 6.017,9.604 6.017,22 41.983,22 41.983,9.604 47,9.604 47,1"/>
	
	<line class="stroke" x1="6.017" y1="9.604" x2="41.983" y2="9.604"/>
	<polygon class="stroke" points="1,1 1,9.604 6.017,9.604 6.017,22 41.983,22 41.983,9.604 47,9.604 47,1 	"/>
</g>
</svg>
