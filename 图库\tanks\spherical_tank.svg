<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 125 150"  xmlns:agg="http://www.example.com"  enable-background="new 0 0 125 150" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="base" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>

	<radialGradient id="radial" cx="39.4946" cy="32.6724" r="100" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#F9F9F9"/>
		<stop  offset="0.17" style="stop-color:#EAEAEA"/>
		<stop  offset="0.48" style="stop-color:#C1C1C1"/>
		<stop  offset="0.93" style="stop-color:#7F7F7F"/>
		<stop  offset="1" style="stop-color:#757575"/>
	</radialGradient>
		 
	<linearGradient id="linearmid" xlink:href="#base" x1="50" y1="0" x2="75" y2="0"/>
	<linearGradient id="lineartop" xlink:href="#base" x1="37.5181" y1="0" x2="87.4937" y2="0"/>
	<linearGradient id="linearleft" xlink:href="#base" x1="10" y1="0" x2="35" y2="0"/>
	<linearGradient id="linearright" xlink:href="#base" x1="90" y1="0" x2="115" y2="0"/>
</defs>

<rect x="50" y="123.735" class="stroke" fill="url(#linearmid)"  />
<circle fill="url(#radial)"  class="stroke" cx="62.5" cy="62.5" r="62.5"/>
<rect x="37.518"  class="stroke" fill="url(#lineartop)"  />
<path fill="url(#linearleft)"  class="stroke" d="M35,150c-8.333,0-16.667,0-25,0c0-22.907,0-45.814,0-68.722c0-0.94,0.053-1.865,0.238-2.795
	c0.131-0.662,0.434-2.184,1.438-2.184c0.215,0,0.416,0.077,0.602,0.177c0.268,0.144,0.504,0.339,0.726,0.547
	c0.327,0.308,0.622,0.646,0.907,0.996c0.35,0.433,0.683,0.88,1.007,1.331c0.544,0.758,1.065,1.529,1.579,2.303
	c0.268,0.4,0.532,0.8,0.796,1.198c0.947,1.438,1.89,2.88,2.839,4.317c0.696,1.054,1.396,2.105,2.1,3.152
	c1.891,2.813,3.806,5.606,5.703,8.414c0.685,1.011,1.364,2.025,2.034,3.046c0.813,1.242,1.608,2.496,2.364,3.773
	c0.513,0.868,1.006,1.746,1.454,2.65c0.355,0.72,0.68,1.458,0.923,2.223c0.089,0.291,0.169,0.586,0.222,0.887
	c0.042,0.229,0.068,0.462,0.068,0.696C35,124.673,35,137.337,35,150z"/>
<path fill="url(#linearright)" class="stroke" d="M90,150c0-12.663,0-25.327,0-37.991c0-0.236,0.026-0.468,0.068-0.697
	c0.053-0.301,0.132-0.595,0.224-0.888c0.242-0.771,0.57-1.511,0.93-2.237c0.451-0.911,0.948-1.799,1.466-2.673
	c0.752-1.27,1.544-2.512,2.354-3.748c0.664-1.012,1.336-2.017,2.012-3.019c1.899-2.815,3.824-5.609,5.716-8.431
	c0.705-1.052,1.405-2.105,2.105-3.162c0.945-1.434,1.888-2.87,2.833-4.304c0.261-0.399,0.525-0.8,0.79-1.198
	c0.515-0.774,1.038-1.542,1.581-2.3c0.324-0.453,0.655-0.898,1.007-1.332c0.284-0.349,0.581-0.689,0.909-0.997
	c0.222-0.21,0.457-0.405,0.726-0.549c0.186-0.1,0.387-0.177,0.602-0.177c1.004,0,1.307,1.522,1.437,2.184
	c0.186,0.929,0.239,1.855,0.239,2.795c0,22.907,0,45.815,0,68.722C106.667,150,98.333,150,90,150z"/>
	
<path opacity="0.5" fill="white" d="M105.701,34.045c0,13.498-19.334,9.094-43.188,9.094c-23.857,0-43.203,4.404-43.203-9.094
	S38.649,9.605,62.506,9.605C86.361,9.605,105.701,20.547,105.701,34.045z"/>
	
<path class="color" d="M125,62.5c0-25.628-15.433-47.642-37.506-57.287V0H37.518v5.208
	C15.438,14.849,0,36.867,0,62.5c0,12.497,3.682,24.127,10,33.894c0,17.869,0,35.738,0,53.607c8.333,0,16.667,0,25,0
	c0-10.456,0-20.913,0-31.37c4.7,2.307,9.727,4.047,15,5.117V150h25v-26.252c5.272-1.07,10.3-2.811,15-5.118
	c0,10.457,0,20.914,0,31.37c8.333,0,16.667,0,25,0c0-17.869,0-35.738,0-53.607C121.318,86.627,125,74.997,125,62.5z"/>
</svg>
