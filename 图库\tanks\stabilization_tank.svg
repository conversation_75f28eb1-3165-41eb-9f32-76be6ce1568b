<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 62 202" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="supportGradient" gradientUnits="userSpaceOnUse" x1="31.0396" y1="201.333" x2="31.0395" y2="113.8373">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.136" style="stop-color:#8A8A8A"/>
		<stop  offset="0.3564" style="stop-color:#A7A7A7"/>
		<stop  offset="0.5757" style="stop-color:#BBBBBB"/>
		<stop  offset="0.7916" style="stop-color:#C8C8C8"/>
		<stop  offset="1" style="stop-color:#CCCCCC"/>
	</linearGradient>
	
	<linearGradient id="baseGradient" gradientUnits="userSpaceOnUse"  x1="6" y1="0" x2="56" y2="0">
		<stop  offset="0" style="stop-color:#757575"/>
		<stop  offset="0.0583" style="stop-color:#959595"/>
		<stop  offset="0.132" style="stop-color:#BABABA"/>
		<stop  offset="0.2058" style="stop-color:#D7D7D7"/>
		<stop  offset="0.2788" style="stop-color:#EBEBEB"/>
		<stop  offset="0.3507" style="stop-color:#F8F8F8"/>
		<stop  offset="0.42" style="stop-color:#FCFCFC"/>
		<stop  offset="0.4749" style="stop-color:#F8F8F8"/>
		<stop  offset="0.5318" style="stop-color:#EBEBEB"/>
		<stop  offset="0.5897" style="stop-color:#D7D7D7"/>
		<stop  offset="0.6481" style="stop-color:#BABABA"/>
		<stop  offset="0.7066" style="stop-color:#959595"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FCFCFC"/>
	</linearGradient>
	
	<linearGradient id="pipeGradient" xlink:href="#baseGradient"  x1="38.4902" y1="0" x2="41.0498" y2="0"/>
</defs>
	 
	<path fill="url(#supportGradient)" d="M53.55,155.04l-3.68,2.479V114.04h3.68V155.04z M12.21,114.04H8.53v41.989l3.68,2.421V114.04z
		 M53.55,192.85l-3.68-2.42v8.41h3.68v-5.98V192.85z M8.53,192.62v6.22h3.68v-8.699L8.53,192.62z"/>
	<path fill="url(#supportGradient)" d="M30.58,170.561L5.97,154.34v-40.3H1V201h4.97v-6.65L30.6,177.73l7.89,5.199v-7.15l-2.53-1.67
		l2.53-1.709v-7.221L30.58,170.561z M5.97,187.09v-25.6l2.56,1.689v22.19L5.97,187.09z M12.21,182.88v-17.271l12.97,8.541
		L12.21,182.88z M56.029,114.04v39.33L41.05,163.46v7.21l8.81-5.949l0.011,0.01v18.539l-8.55-5.629H41.05v6.979l14.979,9.87V201H61
		v-86.96H56.029z M56.029,187.34l-2.479-1.64v-23.46l2.479-1.68V187.34z"/>
		
	<rect id="pipe" fill="url(#pipeGradient)" x="38.49" y="7.54"  />
		
	<polygon fill="url(#baseGradient)" points="26.07,180.779 26.07,180.79 26.07,201 36.01,201 36.01,181.29 30.6,177.73 		"/>
	<polygon fill="url(#baseGradient)" points="41.05,9.7 41.05,140.13 41.05,149.859 56.029,140.13 56.029,114.04 56.029,9.7 		"/>
	<polygon fill="url(#baseGradient)" points="5.97,114.04 5.97,140.13 26.07,153.141 26.07,167.59 30.58,170.561 30.63,170.48 
			36.01,166.859 36.01,166.85 36.01,153.141 38.49,151.52 38.49,140.13 38.49,9.7 5.97,9.7 		"/>

	<path id="boundary" class="color" d="M56.03,114.04V9.7H41.051V7.54H38.49V9.7H5.97v104.34H1V201h4.97v-6.65l2.56-1.729v6.22h3.68
		v-8.699l13.86-9.351V201h9.939v-19.71l2.48,1.64V201h2.561v-16.38l8.819,5.81v8.41h3.681v-5.98l2.479,1.631V201H61v-86.96H56.03z
		 M8.53,185.37l-2.56,1.72v-25.6l2.56,1.689V185.37z M8.53,156.029l-2.56-1.689v-14.21h0.05l2.51,1.62V156.029z M12.21,182.88
		v-17.271l12.97,8.541L12.21,182.88z M26.07,167.59l-13.86-9.13v-14.29l13.86,8.971V167.59z M38.49,175.779l-2.53-1.67l2.53-1.709
		V175.779z M38.49,165.18l-2.48,1.67v-13.709l2.48-1.621V165.18z M41.051,149.859l8.819-5.719v13.379l-8.819,5.94V149.859z
		 M41.051,177.471v-6.801l8.81-5.949l0.01-0.011v18.56L41.051,177.471z M56.03,187.34l-2.479-1.64v-23.46l2.479-1.68V187.34z
		 M56.03,153.37l-2.479,1.67v-13.319l2.479-1.57V153.37z"/>
			
	<g class="stroke">
		<polyline points="41.32,177.641 49.87,183.27 53.55,185.7 56.029,187.34 		"/>
		<polyline points="53.55,192.85 49.87,190.43 41.05,184.62 		"/>
		
		<line x1="56.029" y1="194.49" x2="53.55" y2="192.859"/>
		<polyline points="41.05,163.46 49.87,157.52 53.55,155.04 56.029,153.37 		"/>
		<polyline points="12.21,158.46 26.07,167.59 30.58,170.561 35.96,174.109 38.49,175.779 		"/>
		<polyline points="5.97,154.34 8.53,156.029 12.21,158.45 		"/>
		<polyline points="38.49,182.93 36.01,181.29 30.6,177.73 25.18,174.15 12.21,165.609 8.53,163.18 5.97,161.49 		"/>
		<polyline points="5.97,187.09 8.53,185.37 12.21,182.88 25.18,174.15 		"/>
		
		<line x1="53.55" y1="162.24" x2="53.55" y2="185.7"/>
		
		<line x1="49.87" y1="183.27" x2="49.87" y2="164.73"/>
		<polyline points="53.55,192.85 53.55,192.859 53.55,198.84 49.87,198.84 49.87,190.43 		"/>
		
		<line x1="8.53" y1="185.37" x2="8.53" y2="163.18"/>
		<line x1="12.21" y1="165.609" x2="12.21" y2="182.88"/>
		<polyline points="26.07,180.79 26.07,201 36.01,201 36.01,181.29 		"/>
		<polyline points="53.55,141.74 53.55,141.74 53.55,155.04 		"/>
		
		<line x1="49.87" y1="157.52" x2="49.87" y2="144.141"/>
		<polyline points="36.01,166.85 36.01,153.141 38.49,151.52 		"/>
		
		<line x1="38.49" y1="172.4" x2="35.96" y2="174.109"/>
		<polyline points="30.6,177.73 26.07,180.779 26.07,180.79 12.21,190.141 8.53,192.62 5.97,194.35 		"/>
		<polyline points="5.97,140.13 26.07,153.141 26.07,167.59 		"/>
		<polyline points="12.21,144.17 12.21,158.45 12.21,158.46 		"/>
		
		<line x1="8.53" y1="156.029" x2="8.53" y2="141.79"/>
		<polyline points="30.63,170.48 36.01,166.859 36.01,166.85 38.49,165.18 		"/>
		<polyline points="38.49,9.7	5.97,9.7 5.97,114.04 		"/>
		<polyline points="56.029,114.04 56.029,9.7 41.05,9.7 		"/>
		<polyline points="56.029,160.561 53.55,162.24 49.859,164.721 41.05,170.67 		"/>
		<polygon points="56.029,114.04 61,114.04 61,201 56.029,201 56.029,194.49 56.029,187.34 56.029,160.561 56.029,153.37 56.029,140.13 		"/>
		<polyline points="12.21,190.141 12.21,198.84 8.53,198.84 8.53,192.62 		"/>
		
		<line x1="41.05" y1="149.859" x2="56.029" y2="140.13"/>
		<line x1="41.05" y1="140.13" x2="56.029" y2="140.13"/>
		<line x1="5.97" y1="140.13" x2="38.49" y2="140.13"/>
		<polygon points="41.05,9.7 41.05,7.54 38.49,7.54 38.49,9.7 38.49,140.13 38.49,151.52 38.49,165.18 38.49,172.4 38.49,175.779 38.49,182.93 38.49,201 
			41.05,201 41.05,184.62 41.05,177.641 41.05,170.67 41.05,163.46 41.05,149.859 41.05,140.13 		"/>
		<polyline points="25.99,153.141 26.07,153.141 36.01,153.141 		"/>
		<polygon points="5.97,194.35 5.97,201 1,201 1,114.04 5.97,114.04 5.97,140.13 5.97,154.34 5.97,161.49 5.97,187.09 		"/>
	</g>

	<g id="fence" class="stroke">
		<line x1="15.98" y1="174.912" x2="25.993" y2="174.912"/>
		<line x1="15.98" y1="166.216" x2="25.993" y2="166.216"/>
		<line x1="15.98" y1="157.521" x2="25.993" y2="157.521"/>
		<line x1="15.98" y1="148.826" x2="25.993" y2="148.826"/>
		<line x1="15.98" y1="140.129" x2="25.993" y2="140.129"/>
		<line x1="15.98" y1="131.434" x2="25.993" y2="131.434"/>
		<line x1="15.98" y1="122.738" x2="25.993" y2="122.738"/>
		<line x1="15.98" y1="114.043" x2="25.993" y2="114.043"/>
		<line x1="15.98" y1="105.348" x2="25.993" y2="105.348"/>
		<line x1="15.98" y1="96.651" x2="25.993" y2="96.651"/>
		<line x1="15.98" y1="87.956" x2="25.993" y2="87.956"/>
		<line x1="15.98" y1="79.261" x2="25.993" y2="79.261"/>
		<line x1="15.98" y1="70.565" x2="25.993" y2="70.565"/>
		<line x1="15.98" y1="61.869" x2="25.993" y2="61.869"/>
		<line x1="15.98" y1="53.173" x2="25.993" y2="53.173"/>
		<line x1="15.98" y1="44.478" x2="25.993" y2="44.478"/>
		<line x1="15.98" y1="35.782" x2="25.993" y2="35.782"/>
		<line x1="15.98" y1="27.087" x2="25.993" y2="27.087"/>
		<line x1="15.98" y1="18.391" x2="25.993" y2="18.391"/>
		<line x1="56.034" y1="1" x2="56.034" y2="9.696"/>
		<line x1="51.026" y1="1" x2="51.026" y2="9.696"/>
		<line x1="46.02" y1="1" x2="46.02" y2="9.696"/>
		<line x1="41.014" y1="1" x2="41.014" y2="9.696"/>
		<line x1="36.007" y1="1" x2="36.007" y2="9.696"/>
		<line x1="31" y1="1" x2="31" y2="9.696"/>
		<line x1="20.987" y1="1" x2="20.987" y2="9.696"/>
		<line x1="10.974" y1="1" x2="10.974" y2="9.696"/>
		<line x1="5.967" y1="1" x2="5.967" y2="9.696"/>
		<line x1="56.034" y1="1" x2="5.967" y2="1"/>
		<line x1="25.993" y1="1" x2="25.993" y2="201"/>
		<line x1="15.98" y1="1" x2="15.98" y2="201"/>
		<line x1="15.98" y1="192.303" x2="25.993" y2="192.303"/>
		<line x1="15.98" y1="183.607" x2="25.993" y2="183.607"/>
	</g>

</svg>
