<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"   xmlns:agg="http://www.example.com" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="-1 -1 152 172" enable-background="new 0 0 150 170" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:2px;
		opacity:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#79A2C9;;
	}

      ]]>
</style>

<defs>
	<polygon id="boundary" points="0,0 149.999,0 149.999,15 125,15 125,25 150,50 150,130 116.647,160.004 75,170 33.354,160.004 
		0,129.999 0,50.001 25,25 25,15 0,15 	"/>
</defs>
	 
<use xlink:href="#boundary" class="color"/>
	 
<polygon opacity="0.6" fill="#FFFFFF" points="57.27,15 68.974,15 68.974,25 65.96,50.001 65.96,129.999 69.978,160 75,170 
	60.225,160 48.405,129.999 48.405,50.001 57.27,25 	"/>
<polygon opacity="0.7" fill="#FFFFFF" points="46.597,15 57.27,15 57.27,25 48.405,50.001 48.405,129.999 60.225,160 75,170 
	51.331,160 32.396,129.999 32.396,50.001 46.597,25 	"/>
<polygon opacity="0.5" fill="#FFFFFF" points="37.575,15 46.597,15 46.597,25 32.396,50.001 32.396,129.999 51.331,160 75,170 
	43.812,160 18.862,129.999 18.862,50.001 37.575,25 	"/>
<polygon opacity="0.3" fill="#FFFFFF" points="30.728,15 37.575,15 37.575,25 18.862,50.001 18.862,129.999 43.812,160 75,170 
	38.106,160 8.591,129.999 8.591,50.001 30.728,25 	"/>
<rect x="84.041" opacity="0.4" fill="#FFFFFF"  />
<rect x="101.596" opacity="0.3" fill="#FFFFFF"  />
<polygon opacity="0.5" fill="#FFFFFF" points="68.974,15 81.027,15 81.027,25 84.041,50.001 84.041,129.999 80.022,160 75,170 
	69.978,160 65.96,129.999 65.96,50.001 68.974,25 	"/>
<rect x="131.139" opacity="0.1" fill="#FFFFFF"  />
<rect x="65.96" opacity="0.5" fill="#FFFFFF"  />
<rect x="48.405" opacity="0.6" fill="#FFFFFF"  />
<rect x="32.396" opacity="0.7" fill="#FFFFFF"  />
<rect x="18.862" opacity="0.45" fill="#FFFFFF"  />
<rect x="8.591" opacity="0.25" fill="#FFFFFF"  />
<polygon opacity="0.1" fill="#FFFFFF" points="119.273,15 112.426,15 112.426,25 131.139,50.001 131.139,129.999 106.188,160 
	75,170 111.895,160 141.41,129.999 141.41,50.001 119.273,25 	"/>
<polygon opacity="0.2" fill="#FFFFFF" points="112.426,15 103.404,15 103.404,25 117.605,50.001 117.605,129.999 98.67,160 75,170 
	106.188,160 131.139,129.999 131.139,50.001 112.426,25 	"/>
<polygon opacity="0.3" fill="#FFFFFF" points="103.404,15 92.73,15 92.73,25 101.596,50.001 101.596,129.999 89.775,160 75,170 
	98.67,160 117.605,129.999 117.605,50.001 103.404,25 	"/>
<rect x="117.605" opacity="0.2" fill="#FFFFFF"  />
<polygon opacity="0.4" fill="#FFFFFF" points="92.73,15 81.027,15 81.027,25 84.041,50.001 84.041,129.999 80.022,160 75,170 
	89.775,160 101.596,129.999 101.596,50.001 92.73,25 	"/>
		
<g class="stroke">
	<use xlink:href="#boundary"/>
	<line x1="25" y1="25" x2="125" y2="25"/>
	<line x1="0" y1="50.001" x2="150" y2="50"/>
	<line x1="0" y1="129.999" x2="150" y2="130"/>
	<line x1="33.354" y1="160.004" x2="116.647" y2="160.004"/>
	<line x1="25" y1="15" x2="125" y2="15"/>
</g>

</svg>
