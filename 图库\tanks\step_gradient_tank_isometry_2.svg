<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Default" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 134 204" xmlns:agg="http://www.example.com" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:2px;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		opacity:0.5;
		fill:none;
	}
	
	.color {
		fill:#79A2C9;
	}

      ]]>
</style>
	 
<defs>
	<path id="boundary" d="M2.002,20.141c-0.05-2.475,1.121-3.897,2.85-5.455c2.853-2.57,6.135-3.996,9.666-5.33
		c5.574-2.106,11.279-3.424,17.136-4.472C43.393,2.785,55.091,2,67,2c11.911,0,23.611,0.785,35.353,2.886
		c5.85,1.047,11.547,2.364,17.115,4.466c3.535,1.334,6.82,2.759,9.677,5.332c1.73,1.558,2.903,2.98,2.854,5.458
		c0,39.806,0.001,79.612,0.002,119.417C132,174.044,102.898,202,67,202S2,174.044,2,139.559C2,99.753,2.001,59.947,2.002,20.141z"/>
</defs>

	<use xlink:href="#boundary" class="color"/>

	<path opacity="0.2" fill="#FFFFFF" d="M67.007,19.932c-20.787,1.693-41.575,3.374-62.375,5.066
		c0,39.864,0,79.74,0,119.616c0,14.371,5.429,28.248,15.925,38.616c0.334,0.333,0.682,0.667,1.029,0.988
		c5.454,5.116,11.834,9.329,18.896,12.357c3.474,1.495,7.113,2.706,10.882,3.608c2.341,0.557,4.734,0.988,7.165,1.285
		c1.917,0.248,3.859,0.408,5.828,0.47c0.875,0.049,1.763,0.062,2.65,0.062c0.888,0,1.775-0.013,2.65-0.062
		c1.98-0.062,3.936-0.222,5.865-0.47c2.406-0.297,4.76-0.729,7.075-1.272c3.782-0.902,7.436-2.113,10.921-3.608
		c6.998-3.015,13.352-7.167,18.768-12.233c0.424-0.396,0.836-0.791,1.248-1.198c10.432-10.368,15.834-24.208,15.834-38.542
		c0-39.876,0-79.752,0-119.616C108.581,23.305,87.793,21.625,67.007,19.932z"/>
	<path opacity="0.21" fill="#FFFFFF" d="M67,19.932c-18.227,3.238-36.455,6.475-54.682,9.7
		c0,39.876,0,79.752,0,119.616c0,18.967,8.67,38.949,28.158,47.314v0.013c3.474,1.495,7.113,2.706,10.882,3.608
		c2.341,0.557,4.734,0.988,7.165,1.285c1.917,0.248,3.859,0.408,5.827,0.47C65.225,201.987,66.113,202,67,202
		c0.888,0,1.775-0.013,2.65-0.062c1.98-0.062,3.936-0.222,5.865-0.47c2.406-0.297,4.76-0.729,7.075-1.272
		c3.783-0.902,7.436-2.113,10.921-3.608l-0.013-0.025c19.514-8.353,28.184-28.347,28.184-47.314c0-39.864,0-79.74,0-119.616
		C103.455,26.407,85.228,23.169,67,19.932z"/>
	<path opacity="0.23" fill="#FFFFFF" d="M67,19.932C52.812,24.455,38.624,28.977,24.435,33.5
		c0,39.864,0,79.74,0,119.616c0,9.886,1.376,18.931,5.698,28.087c4.052,8.563,10.985,16.534,21.225,18.956v0.024
		c2.341,0.557,4.734,0.988,7.165,1.285c1.917,0.248,3.859,0.408,5.828,0.47C65.225,201.987,66.112,202,67,202
		s1.775-0.013,2.65-0.062c1.98-0.062,3.936-0.222,5.865-0.47c2.405-0.297,4.76-0.729,7.075-1.272v-0.024
		c10.265-2.41,17.198-10.38,21.264-18.956c4.335-9.156,5.711-18.202,5.711-28.1c0-39.876,0-79.752,0-119.616
		C95.376,28.977,81.188,24.455,67,19.932z"/>
	<path opacity="0.25" fill="#FFFFFF" d="M67,19.932c-9.004,5.45-18.008,10.886-27,16.323
		c0,39.876,0,79.752,0,119.616c0,8.786,0.708,17.287,3.062,25.838c1.801,6.537,6.239,18.573,15.462,19.747v0.012
		c1.917,0.248,3.859,0.408,5.827,0.47C65.225,201.987,66.113,202,67,202c0.888,0,1.775-0.013,2.65-0.062
		c1.98-0.062,3.936-0.222,5.865-0.47v-0.012c9.198-1.212,13.623-13.235,15.423-19.771C93.28,173.146,94,164.645,94,155.871
		c0-39.864,0-79.74,0-119.616C84.996,30.818,76.005,25.381,67,19.932z"/>
	<path opacity="0.35" fill="#FFFFFF" d="M76.248,37.701c0,39.864,0,79.74,0,119.616
		c0,7.674-0.206,15.335-0.81,22.996c-0.309,3.831-0.721,7.637-1.377,11.43c-0.334,1.891-0.72,3.77-1.286,5.61
		c-0.334,1.1-1.273,4.56-3.125,4.584C68.774,201.987,67.887,202,67,202c-0.888,0-1.776-0.013-2.65-0.062
		c-1.866-0.025-2.792-3.484-3.126-4.584c-0.566-1.841-0.952-3.719-1.287-5.61c-0.669-3.793-1.068-7.611-1.376-11.442
		c-0.618-7.649-0.811-15.311-0.811-22.984c0-39.876,0-79.752,0-119.616c3.074-5.919,6.161-11.838,9.249-17.77
		C70.086,25.863,73.161,31.782,76.248,37.701z"/>

	<path opacity="0.2" fill="#FFFFFF" d="M129.148,14.691c-2.251-2.026-4.76-3.349-7.461-4.461
		c-0.721-0.309-1.467-0.593-2.212-0.877c-3.255-1.224-6.56-2.187-9.905-2.965c-2.38-0.581-4.786-1.063-7.216-1.495
		c-2.779-0.495-5.57-0.927-8.349-1.273c-5.904-0.766-11.808-1.223-17.751-1.445C73.18,2.049,70.105,2,67.006,2
		c-3.101,0-6.175,0.049-9.249,0.173c-5.943,0.223-11.848,0.679-17.751,1.445c-2.779,0.347-5.57,0.778-8.348,1.273
		c-2.431,0.432-4.837,0.914-7.217,1.495c-3.357,0.778-6.663,1.742-9.917,2.978c-0.746,0.284-1.479,0.569-2.2,0.878
		c-2.701,1.112-5.223,2.422-7.474,4.448c-0.077,0.062-0.154,0.136-0.219,0.198l62.375,5.054l62.361-5.054
		C129.302,14.827,129.225,14.753,129.148,14.691z"/>
	<path opacity="0.21" fill="#FFFFFF" d="M119.469,9.353c-3.254-1.224-6.56-2.187-9.905-2.965
		c-2.38-0.581-4.786-1.063-7.216-1.495c-2.778-0.495-5.57-0.927-8.349-1.273c-5.904-0.766-11.808-1.223-17.751-1.445
		C73.173,2.049,70.099,2,67,2c-3.101,0-6.175,0.049-9.249,0.173c-5.943,0.223-11.848,0.679-17.751,1.445
		c-2.779,0.347-5.57,0.778-8.348,1.273c-2.431,0.432-4.837,0.914-7.217,1.495c-3.357,0.778-6.663,1.742-9.917,2.978
		c-0.746,0.284-1.479,0.569-2.2,0.878L67,19.942l54.682-9.7V10.23C120.96,9.921,120.214,9.637,119.469,9.353z"/>
	<path opacity="0.23" fill="#FFFFFF" d="M102.348,4.892c-2.778-0.495-5.57-0.927-8.349-1.273
		c-5.904-0.766-11.808-1.223-17.751-1.445C73.173,2.049,70.099,2,67,2c-3.101,0-6.175,0.049-9.249,0.173
		c-5.943,0.223-11.848,0.679-17.751,1.445c-2.779,0.347-5.57,0.778-8.348,1.273c-2.431,0.432-4.837,0.914-7.217,1.495L67,19.942
		l42.564-13.555C107.184,5.806,104.778,5.324,102.348,4.892z"/>
	<path opacity="0.25" fill="#FFFFFF" d="M76.248,2.173C73.173,2.049,70.099,2,67,2
		c-3.101,0-6.175,0.049-9.249,0.173c-5.943,0.223-11.848,0.679-17.751,1.445v0.013l27,16.311l27-16.311V3.619
		C88.095,2.853,82.191,2.396,76.248,2.173z"/>
	<path opacity="0.35" fill="#FFFFFF" d="M76.248,2.173v0.013l-9.249,17.756L57.75,2.186V2.173
		C60.824,2.049,63.898,2,66.999,2C70.098,2,73.173,2.049,76.248,2.173z"/>
	
	<g class="stroke">
		<use xlink:href="#boundary"/>
		<path d="M125.503,151.441c-23.68,13.795-93.326,13.795-117.006,0"/>
		<path d="M131.996,19.932c0,10.004-29.101,18.114-64.998,18.114S2.001,29.936,2.001,19.932"/>
	</g>
</svg>
