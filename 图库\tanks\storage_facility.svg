<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:agg="http://www.example.com" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 167 102" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
    <agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.5;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="tankBody" gradientUnits="userSpaceOnUse" x1="1" y1="0" x2="166" y2="0">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	
	<rect id="body" x="1" y="15.286"  />
</defs>	 	 

<use xlink:href="#body" fill="url(#tankBody)"/>
<use xlink:href="#body" class="color"/>
<use xlink:href="#body" class="stroke"/>

<g id="fence" class="stroke">
	<line x1="1" y1="1" x2="166" y2="1"/>
	<line x1="166" y1="15.286" x2="166" y2="1"/>
	<line x1="164.311" y1="15.286" x2="164.311" y2="1"/>
	<line x1="162.217" y1="15.286" x2="162.217" y2="1"/>
	<line x1="159.313" y1="15.286" x2="159.313" y2="1"/>
	<line x1="155.633" y1="15.286" x2="155.633" y2="1"/>
	<line x1="151.213" y1="15.286" x2="151.213" y2="1"/>
	<line x1="146.098" y1="15.286" x2="146.098" y2="1"/>
	<line x1="140.34" y1="15.286" x2="140.34" y2="1"/>
	<line x1="133.999" y1="15.286" x2="133.999" y2="1"/>
	<line x1="127.14" y1="15.286" x2="127.14" y2="1"/>
	<line x1="119.833" y1="15.286" x2="119.833" y2="1"/>
	<line x1="112.152" y1="15.286" x2="112.152" y2="1"/>
	<line x1="104.179" y1="15.286" x2="104.179" y2="1"/>
	<line x1="95.993" y1="15.286" x2="95.993" y2="1"/>
	<line x1="87.679" y1="15.286" x2="87.679" y2="1"/>
	<line x1="79.322" y1="15.286" x2="79.322" y2="1"/>
	<line x1="71.007" y1="15.286" x2="71.007" y2="1"/>
	<line x1="62.821" y1="15.286" x2="62.821" y2="1"/>
	<line x1="54.848" y1="15.286" x2="54.848" y2="1"/>
	<line x1="47.167" y1="15.286" x2="47.167" y2="1"/>
	<line x1="39.861" y1="15.286" x2="39.861" y2="1"/>
	<line x1="33.001" y1="15.286" x2="33.001" y2="1"/>
	<line x1="11.367" y1="15.286" x2="11.367" y2="1"/>
	<line x1="7.686" y1="15.286" x2="7.686" y2="1"/>
	<line x1="4.784" y1="15.286" x2="4.784" y2="1"/>
	<line x1="2.689" y1="15.286" x2="2.689" y2="1"/>
	<line x1="1" y1="15.286" x2="1" y2="1"/>
</g>

<g id="ladder" class="stroke">
	<polyline points="26.815,1.182 26.815,88 15.787,88 15.787,1 	"/>
	<line x1="15.787" y1="6.004" x2="26.815" y2="6.004"/>
	<line x1="15.787" y1="10.828" x2="26.815" y2="10.828"/>
	<line x1="15.787" y1="20.475" x2="26.815" y2="20.475"/>
	<line x1="15.787" y1="25.297" x2="26.815" y2="25.297"/>
	<line x1="15.787" y1="30.121" x2="26.815" y2="30.121"/>
	<line x1="15.787" y1="34.944" x2="26.815" y2="34.944"/>
	<line x1="15.787" y1="39.767" x2="26.815" y2="39.767"/>
	<line x1="15.787" y1="44.59" x2="26.815" y2="44.59"/>
	<line x1="15.787" y1="49.414" x2="26.815" y2="49.414"/>
	<line x1="15.787" y1="54.237" x2="26.815" y2="54.237"/>
	<line x1="15.787" y1="59.061" x2="26.815" y2="59.061"/>
	<line x1="15.787" y1="63.884" x2="26.815" y2="63.884"/>
	<line x1="15.787" y1="68.707" x2="26.815" y2="68.707"/>
	<line x1="15.787" y1="73.53" x2="26.815" y2="73.53"/>
	<line x1="15.787" y1="78.353" x2="26.815" y2="78.353"/>
	<line x1="15.787" y1="83.177" x2="26.815" y2="83.177"/>
</g>
</svg>
