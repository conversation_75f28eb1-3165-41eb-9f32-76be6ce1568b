<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns:agg="http://www.example.com" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 150 200" enable-background="new 0 0 150 200" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
    <agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>
	 
<defs>
	<path id="boundary" d="M148.995,20.95v158.093c0,5.18-6.723,8.735-12.231,10.986c-6.195,2.529-12.722,4.126-19.63,5.416
	c-13.936,2.605-27.763,3.549-42.141,3.549c-14.366,0-28.193-0.943-42.117-3.549c-6.919-1.29-13.446-2.878-19.654-5.416
	c-5.496-2.251-12.219-5.807-12.219-10.986V20.95c0-3.829,3.84-6.79,7.815-8.925c7.299-3.928,15.323-5.848,24.046-7.477
	C46.788,1.942,60.627,1,74.993,1s28.193,0.942,42.104,3.54c8.737,1.629,16.771,3.549,24.072,7.478
	C145.144,14.16,148.995,17.113,148.995,20.95z"/>
	
	<linearGradient id="tankBody" gradientUnits="userSpaceOnUse" x1="1.002" y1="109.978" x2="149" y2="109.978">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	
	<linearGradient id="tankTop" gradientUnits="userSpaceOnUse" x1="0" y1="0" x2="150" y2="40">
		<stop  offset="0" style="stop-color:#B1B1B1"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
</defs>	 
	 
<g>
	<use xlink:href="#boundary" fill="url(#tankBody)"/>
	<path fill="url(#tankTop)" d="M148.998,20.957c0,3.833-3.849,6.789-7.824,8.931c-7.29,3.928-15.316,5.844-24.038,7.474
		c-13.929,2.601-27.765,3.55-42.136,3.55c-14.361,0-28.186-0.948-42.105-3.545c-8.732-1.629-16.765-3.544-24.062-7.476
		C4.855,27.75,1,24.793,1,20.957c0-3.834,3.851-6.79,7.825-8.932c7.289-3.928,15.314-5.844,24.038-7.474C46.792,1.95,60.627,1,75,1
		c14.361,0,28.187,0.948,42.105,3.545c8.732,1.629,16.763,3.545,24.061,7.476C145.144,14.163,148.998,17.119,148.998,20.957z"/>
</g>

<g class="stroke">
		<path d="M149,20.956
			c0,3.833-3.849,6.789-7.824,8.931c-7.29,3.928-15.316,5.843-24.04,7.474c-13.929,2.602-27.763,3.55-42.136,3.55
			c-14.36,0-28.185-0.947-42.104-3.544c-8.732-1.629-16.765-3.545-24.062-7.476c-3.978-2.142-7.833-5.099-7.833-8.935"/>
		<use xlink:href="#boundary"/>
		<line x1="38" y1="38.238" x2="38" y2="196.327"/>
		<line x1="111.999" y1="38.238" x2="111.999" y2="196.327"/>
</g>

<use xlink:href="#boundary" class="color"/>

<g id="rivets" opacity="0.5">
	<circle cx="116.5" cy="45.5" r="1.5"/>
	<circle cx="116.5" cy="51.687" r="1.5"/>
	<circle cx="116.5" cy="57.875" r="1.5"/>
	<circle cx="116.5" cy="64.062" r="1.5"/>
	<circle cx="116.5" cy="70.25" r="1.5"/>
	<circle cx="116.5" cy="76.437" r="1.5"/>
	<circle cx="116.5" cy="82.625" r="1.5"/>
	<circle cx="116.5" cy="88.812" r="1.5"/>
	<circle cx="116.5" cy="95" r="1.5"/>
	<circle cx="116.5" cy="101.187" r="1.5"/>
	<circle cx="116.5" cy="107.375" r="1.5"/>
	<circle cx="116.5" cy="113.562" r="1.5"/>
	<circle cx="116.5" cy="119.75" r="1.5"/>
	<circle cx="116.5" cy="125.938" r="1.5"/>
	<circle cx="116.5" cy="132.125" r="1.5"/>
	<circle cx="116.5" cy="138.313" r="1.5"/>
	<circle cx="116.5" cy="144.5" r="1.5"/>
	<circle cx="116.5" cy="150.688" r="1.5"/>
	<circle cx="116.5" cy="156.875" r="1.5"/>
	<circle cx="116.5" cy="163.063" r="1.5"/>
	<circle cx="116.5" cy="169.25" r="1.5"/>
	<circle cx="116.5" cy="175.438" r="1.5"/>
	<circle cx="116.5" cy="181.625" r="1.5"/>
	<circle cx="116.5" cy="187.813" r="1.5"/>
</g>

<use xlink:href="#rivets" transform="translate(-73.5 2)"/>

</svg>
