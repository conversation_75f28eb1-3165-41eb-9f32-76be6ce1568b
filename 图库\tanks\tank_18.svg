<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns:agg="http://www.example.com"
	 xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"  
	 viewBox="0 0 154 204" xml:space="preserve">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<linearGradient id="base" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#767676"/>
		<stop  offset="0.06" style="stop-color:#919191"/>
		<stop  offset="0.14" style="stop-color:#B1B1B1"/>
		<stop  offset="0.21" style="stop-color:#CECECE"/>
		<stop  offset="0.28" style="stop-color:#E4E4E4"/>
		<stop  offset="0.35" style="stop-color:#F6F6F6"/>
		<stop  offset="0.42" style="stop-color:#FEFEFF"/>
		<stop  offset="0.47" style="stop-color:#F6F6F6"/>
		<stop  offset="0.53" style="stop-color:#E7E7E7"/>
		<stop  offset="0.58" style="stop-color:#D2D1D1"/>
		<stop  offset="0.64" style="stop-color:#B7B7B7"/>
		<stop  offset="0.7" style="stop-color:#989898"/>
		<stop  offset="0.72" style="stop-color:#8B8B8B"/>
		<stop  offset="1" style="stop-color:#FDFDFD"/>
	</linearGradient>
	<path id="boundary" d="M142.043,20h0.244V2.5H13.203V20c-5.844,0-10.58,4.737-10.58,10.581v143.333
	c0,5.844,4.736,10.581,10.58,10.581v17.5h129.084v-17.5h-0.244c5.842,0,10.58-4.737,10.58-10.581V30.581
	C152.623,24.737,147.885,20,142.043,20z"/>
</defs>

<linearGradient id="gradient1" xlink:href="#base" x1="2.5" y1="102.2476" x2="152.5" y2="102.2476"/>
<linearGradient id="gradient2" xlink:href="#base" x1="13.0806" y1="11.25" x2="142.165" y2="11.25"/>

<path fill="url(#gradient1)" d="M152.5,173.914c0,5.844-4.737,10.581-10.58,10.581H13.081c-5.843,0-10.581-4.737-10.581-10.581
	V30.581C2.5,24.737,7.237,20,13.081,20H141.92c5.843,0,10.58,4.737,10.58,10.581V173.914z"/>
<rect x="13.081" y="2.5" fill="url(#gradient2)"  />
<rect x="13.081" y="184.495" fill="url(#gradient2)"  />

<use xlink:href="#boundary" class="color"/>

<g class="stroke">
	<use xlink:href="#boundary"/>	
	<line x1="13.081" y1="20" x2="142.165" y2="20"/>
	<line x1="13.081" y1="184.495" x2="142.165" y2="184.495"/>
</g>
	
</svg>
