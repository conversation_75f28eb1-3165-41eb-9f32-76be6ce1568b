<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"  xmlns:agg="http://www.example.com" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	   viewBox="0 0 150 200" xml:space="preserve" offset="0.15">
	 
<agg:params>
	<agg:param type="C" name="color" description="Color" cssAttributes="fill" classes="color"/>
	<agg:param type="F" name="saturation" description="Saturation" min="0" max="1" cssAttributes="opacity" classes="color"/>
</agg:params>

<style type="text/css" >
   <![CDATA[

	.stroke{
		stroke-width:0.3;
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: black;
		fill:none;
	}
	
	.color {
		fill:#3664BF;
		opacity:0.2;
	}

      ]]>
</style>

<defs>
	<path id="tankBody" d="M75,0C33.579,0,0,8.14,0,18.182v163.637C0,191.859,33.579,200,75,200c41.422,0,75-8.141,75-18.182V18.182C150,8.14,116.422,0,75,0z"/>
	<circle id="c" rx="0" ry="0" r="3"/>
</defs>
	 
<linearGradient id="horizontalGradient" gradientUnits="userSpaceOnUse" x1="0" y1="100" x2="150" y2="100">
	<stop  offset="0" style="stop-color:#767676"/>
	<stop  offset="0.06" style="stop-color:#919191"/>
	<stop  offset="0.14" style="stop-color:#B1B1B1"/>
	<stop  offset="0.21" style="stop-color:#CECECE"/>
	<stop  offset="0.28" style="stop-color:#E4E4E4"/>
	<stop  offset="0.35" style="stop-color:#F6F6F6"/>
	<stop  offset="0.42" style="stop-color:#FEFEFF"/>
	<stop  offset="0.47" style="stop-color:#F6F6F6"/>
	<stop  offset="0.53" style="stop-color:#E7E7E7"/>
	<stop  offset="0.58" style="stop-color:#D2D1D1"/>
	<stop  offset="0.64" style="stop-color:#B7B7B7"/>
	<stop  offset="0.7" style="stop-color:#989898"/>
	<stop  offset="0.72" style="stop-color:#8B8B8B"/>
	<stop  offset="1" style="stop-color:#FDFDFD"/>
</linearGradient>

<linearGradient id="verticalGradient" gradientUnits="userSpaceOnUse" x1="75" y1="0" x2="75" y2="200">
	<stop  offset="0" style="stop-color:#757575"/>
	<stop  offset="0.03" style="stop-color:#959595"/>
	<stop  offset="0.06" style="stop-color:#BABABA"/>
	<stop  offset="0.1" style="stop-color:#D7D7D7"/>
	<stop  offset="0.13" style="stop-color:#EBEBEB"/>
	<stop  offset="0.17" style="stop-color:#F8F8F8"/>
	<stop  offset="0.2" style="stop-color:#FCFCFC"/>
	<stop  offset="0.9" style="stop-color:#FFFFFF"/>
	<stop  offset="1" style="stop-color:#B0B0B0"/>
</linearGradient>

<use xlink:href="#tankBody" opacity="0.5" fill="url(#horizontalGradient)"/>
<use xlink:href="#tankBody" opacity="0.5" fill="url(#verticalGradient)"/>
<use xlink:href="#tankBody" class="color"/>

<g class="stroke">
	<use xlink:href="#tankBody"/>
	<line x1="0" y1="18.182" x2="150" y2="18.182"/>
	<line x1="0" y1="181.818" x2="150" y2="181.818"/>
	<line x1="13.915" y1="18.182" x2="13.915" y2="181.818"/>
</g>

<g style="color:black;opacity:0.5">
	<circle cx="19.181" cy="25.75" r="1.5"/>
	<circle cx="19.181" cy="31.937" r="1.5"/>
	<circle cx="19.181" cy="38.125" r="1.5"/>
	<circle cx="19.181" cy="44.312" r="1.5"/>
	<circle cx="19.181" cy="50.5" r="1.5"/>
	<circle cx="19.181" cy="56.687" r="1.5"/>
	<circle cx="19.181" cy="62.875" r="1.5"/>
	<circle cx="19.181" cy="69.062" r="1.5"/>
	<circle cx="19.181" cy="75.25" r="1.5"/>
	<circle cx="19.181" cy="81.437" r="1.5"/>
	<circle cx="19.181" cy="87.625" r="1.5"/>
	<circle cx="19.181" cy="93.812" r="1.5"/>
	<circle cx="19.181" cy="100" r="1.5"/>
	<circle cx="19.181" cy="106.188" r="1.5"/>
	<circle cx="19.181" cy="112.375" r="1.5"/>
	<circle cx="19.181" cy="118.563" r="1.5"/>
	<circle cx="19.181" cy="124.75" r="1.5"/>
	<circle cx="19.181" cy="130.938" r="1.5"/>
	<circle cx="19.181" cy="137.125" r="1.5"/>
	<circle cx="19.181" cy="143.313" r="1.5"/>
	<circle cx="19.181" cy="149.5" r="1.5"/>
	<circle cx="19.181" cy="155.688" r="1.5"/>
	<circle cx="19.181" cy="161.875" r="1.5"/>
	<circle cx="19.181" cy="168.063" r="1.5"/>
	<circle cx="19.181" cy="174.25" r="1.5"/>
	<circle cx="13.188" cy="13.5" r="1.5"/>
	<circle cx="19.375" cy="13.5" r="1.5"/>
	<circle cx="25.563" cy="13.5" r="1.5"/>
	<circle cx="31.75" cy="13.5" r="1.5"/>
	<circle cx="37.938" cy="13.5" r="1.5"/>
	<circle cx="44.125" cy="13.5" r="1.5"/>
	<circle cx="50.313" cy="13.5" r="1.5"/>
	<circle cx="56.5" cy="13.5" r="1.5"/>
	<circle cx="62.688" cy="13.5" r="1.5"/>
	<circle cx="68.875" cy="13.5" r="1.5"/>
	<circle cx="75.063" cy="13.5" r="1.5"/>
	<circle cx="81.25" cy="13.5" r="1.5"/>
	<circle cx="87.438" cy="13.5" r="1.5"/>
	<circle cx="93.625" cy="13.5" r="1.5"/>
	<circle cx="99.813" cy="13.5" r="1.5"/>
	<circle cx="106" cy="13.5" r="1.5"/>
	<circle cx="112.188" cy="13.5" r="1.5"/>
	<circle cx="118.375" cy="13.5" r="1.5"/>
	<circle cx="124.563" cy="13.5" r="1.5"/>
	<circle cx="130.75" cy="13.5" r="1.5"/>
	<circle cx="136.938" cy="13.5" r="1.5"/>
	<circle cx="13.188" cy="187" r="1.5"/>
	<circle cx="19.375" cy="187" r="1.5"/>
	<circle cx="25.563" cy="187" r="1.5"/>
	<circle cx="31.75" cy="187" r="1.5"/>
	<circle cx="37.938" cy="187" r="1.5"/>
	<circle cx="44.125" cy="187" r="1.5"/>
	<circle cx="50.313" cy="187" r="1.5"/>
	<circle cx="56.5" cy="187" r="1.5"/>
	<circle cx="62.688" cy="187" r="1.5"/>
	<circle cx="68.875" cy="187" r="1.5"/>
	<circle cx="75.063" cy="187" r="1.5"/>
	<circle cx="81.25" cy="187" r="1.5"/>
	<circle cx="87.438" cy="187" r="1.5"/>
	<circle cx="93.625" cy="187" r="1.5"/>
	<circle cx="99.813" cy="187" r="1.5"/>
	<circle cx="106" cy="187" r="1.5"/>
	<circle cx="112.188" cy="187" r="1.5"/>
	<circle cx="118.375" cy="187" r="1.5"/>
	<circle cx="124.563" cy="187" r="1.5"/>
	<circle cx="130.75" cy="187" r="1.5"/>
	<circle cx="136.938" cy="187" r="1.5"/>
</g>

</svg>
