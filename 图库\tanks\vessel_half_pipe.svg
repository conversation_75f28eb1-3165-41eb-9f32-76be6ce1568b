<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='105.371px'

    height='123.242px'

    viewBox='0 0 105.371 123.242'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M104.315,67.078c0-3.72-3.02-6.739-6.729-6.739c0-2.54,0-5.091,0-7.641c3.71,0,6.729-3.01,6.729-6.729   c0-3.721-3.02-6.74-6.729-6.74c0-6.73,0-13.47,0-20.2c0-8.74-13.46-13.13-19.35-14.76c-8.44-2.34-16.83-3.2-25.551-3.2   c-8.72,0-17.109,0.86-25.56,3.2c-5.89,1.63-19.33,6.02-19.33,14.76c0,6.73,0,13.47,0,20.2c-3.72,0-6.74,3.02-6.74,6.74   c0,3.72,3.021,6.729,6.74,6.729c0,2.55,0,5.101,0,7.641c-3.72,0-6.74,3.02-6.74,6.739c0,3.721,3.021,6.73,6.74,6.73   c0,2.55,0,5.1,0,7.64c-3.72,0-6.74,3.01-6.74,6.73c0,3.72,3.021,6.74,6.74,6.74c0,1.64,0,3.279,0,4.92c0,0.92,0.15,1.79,0.43,2.609   c-8.43,6.55,4.16,17.92,9.83,8.9c2.5,1.17,5.05,2.06,7.141,2.7c-2.221,8.67,11.6,11.649,13.149,2.85c4.72,0.64,9.48,0.9,14.34,0.9   c4.86,0,9.62-0.261,14.341-0.9c1.55,8.8,15.38,5.82,13.149-2.85c2.09-0.641,4.64-1.521,7.14-2.7c5.671,9.01,18.25-2.34,9.841-8.9   c0.279-0.819,0.43-1.689,0.43-2.609c0-1.641,0-3.28,0-4.92c3.71,0,6.729-3.021,6.729-6.74c0-3.721-3.02-6.73-6.729-6.73   c0-2.54,0-5.09,0-7.64C101.296,73.809,104.315,70.799,104.315,67.078z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M87.273,111.276c5.662,9.141,18.35-2.299,9.844-8.873' />

  <path d='M97.582,94.901c3.717,0,6.733-3.016,6.733-6.734c0-3.721-3.017-6.734-6.733-6.734' />

  <path d='M67.016,116.847c1.517,8.842,15.397,5.867,13.158-2.822' />

  <path d='M97.582,73.794c3.717,0,6.733-3.014,6.733-6.734c0-3.719-3.017-6.736-6.733-6.736' />

  <path d='M7.789,81.433c-3.719,0-6.733,3.014-6.733,6.734c0,3.719,3.015,6.734,6.733,6.734' />

  <path d='M97.582,52.688c3.717,0,6.733-3.015,6.733-6.733c0-3.721-3.017-6.736-6.733-6.736' />

  <path d='M7.789,60.323c-3.719,0-6.733,3.018-6.733,6.736c0,3.721,3.015,6.734,6.733,6.734' />

  <path d='M8.254,102.403c-8.506,6.574,4.184,18.014,9.844,8.873' />

  <path d='M25.197,114.024c-2.238,8.689,11.642,11.664,13.159,2.822' />

  <path d='M97.582,99.827c0,8.747-13.449,13.133-19.332,14.764c-8.451,2.34-16.844,3.195-25.564,3.195   c-8.717,0-17.106-0.855-25.555-3.194c-5.884-1.628-19.342-6.017-19.342-14.765c0-26.938,0-53.873,0-80.813   c0-8.744,13.449-13.132,19.332-14.762c8.451-2.342,16.844-3.197,25.564-3.197c8.716,0,17.106,0.855,25.555,3.195   c5.883,1.629,19.342,6.02,19.342,14.764C97.582,45.954,97.582,72.89,97.582,99.827z' />

  <path d='M7.789,39.218c-3.719,0-6.733,3.016-6.733,6.736c0,3.719,3.015,6.733,6.733,6.733' />

 </g>

</svg>