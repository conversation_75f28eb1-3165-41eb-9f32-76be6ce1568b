<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='105.371px'

    height='204.053px'

    viewBox='0 0 105.371 204.053'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='61.666,36.974 61.666,25.003 61.666,13.023 61.666,1.054 43.706,1.054 43.706,13.023 43.706,25.003    43.706,36.974 52.686,36.974  ' />

  <path d='M104.315,147.876c0-3.72-3.02-6.739-6.729-6.739c0-2.54,0-5.091,0-7.641c3.71,0,6.729-3.01,6.729-6.729   c0-3.721-3.02-6.74-6.729-6.74c0-6.73,0-13.47,0-20.2c0-8.74-13.46-13.13-19.35-14.76c-8.44-2.34-16.83-3.2-25.551-3.2   c-8.72,0-17.109,0.86-25.56,3.2c-5.89,1.63-19.33,6.02-19.33,14.76c0,6.73,0,13.47,0,20.2c-3.72,0-6.74,3.02-6.74,6.74   c0,3.72,3.021,6.729,6.74,6.729c0,2.55,0,5.101,0,7.641c-3.72,0-6.74,3.02-6.74,6.739c0,3.721,3.021,6.73,6.74,6.73   c0,2.55,0,5.1,0,7.64c-3.72,0-6.74,3.01-6.74,6.73c0,3.72,3.021,6.74,6.74,6.74c0,1.64,0,3.279,0,4.92c0,0.92,0.15,1.79,0.43,2.609   c-8.43,6.55,4.16,17.92,9.83,8.9c2.5,1.17,5.05,2.06,7.141,2.7c-2.221,8.67,11.6,11.649,13.149,2.85c4.72,0.64,9.48,0.9,14.34,0.9   c4.86,0,9.62-0.261,14.341-0.9c1.55,8.8,15.38,5.82,13.149-2.85c2.09-0.641,4.64-1.521,7.14-2.7c5.671,9.01,18.25-2.34,9.841-8.9   c0.279-0.819,0.43-1.689,0.43-2.609c0-1.641,0-3.28,0-4.92c3.71,0,6.729-3.021,6.729-6.74c0-3.721-3.02-6.73-6.729-6.73   c0-2.54,0-5.09,0-7.64C101.296,154.606,104.315,151.597,104.315,147.876z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M67.017,197.657c1.516,8.842,15.396,5.867,13.156-2.822' />

  <rect x='43.708'

      y='1.056'

      fill='none'

      width='17.957'

      height='35.916' />

  <path d='M52.686,171.659c3.984,2.777,12.251,8.979,17.958,8.979c4.959,0,8.979-4.02,8.979-8.979s-4.02-8.98-8.979-8.98   C64.937,162.679,56.67,168.883,52.686,171.659z' />

  <path d='M87.273,192.087c5.661,9.141,18.351-2.299,9.844-8.873' />

  <path d='M97.581,175.712c3.719,0,6.734-3.015,6.734-6.734c0-3.721-3.016-6.734-6.734-6.734' />

  <path d='M97.581,154.604c3.719,0,6.734-3.013,6.734-6.733c0-3.719-3.016-6.732-6.734-6.732' />

  <path d='M97.581,133.497c3.719,0,6.734-3.014,6.734-6.732c0-3.721-3.016-6.736-6.734-6.736' />

  <path d='M7.79,162.243c-3.72,0-6.734,3.014-6.734,6.734c0,3.72,3.015,6.734,6.734,6.734' />

  <path d='M7.79,141.138c-3.72,0-6.734,3.014-6.734,6.732c0,3.721,3.015,6.733,6.734,6.733' />

  <line x1='66.155'

      y1='25'

      x2='39.218'

      y2='25' />

  <path d='M34.728,162.679c5.707,0,13.975,6.204,17.958,8.98c-3.983,2.777-12.251,8.979-17.958,8.979   c-4.959,0-8.979-4.02-8.979-8.979S29.769,162.679,34.728,162.679z' />

  <path d='M8.254,183.214c-8.507,6.574,4.184,18.014,9.844,8.873' />

  <path d='M25.198,194.835c-2.238,8.689,11.641,11.664,13.158,2.822' />

  <path d='M97.581,180.638c0,8.746-13.448,13.133-19.331,14.764c-8.45,2.34-16.845,3.195-25.564,3.195   c-8.718,0-17.106-0.855-25.554-3.194c-5.885-1.628-19.342-6.019-19.342-14.765c0-26.938,0-53.873,0-80.813   c0-8.744,13.448-13.132,19.331-14.762c8.45-2.342,16.845-3.197,25.564-3.197c8.716,0,17.106,0.855,25.554,3.195   c5.883,1.627,19.342,6.02,19.342,14.764C97.581,126.765,97.581,153.7,97.581,180.638z' />

  <line x1='52.686'

      y1='36.972'

      x2='52.686'

      y2='171.659' />

  <path d='M7.79,120.028c-3.72,0-6.734,3.016-6.734,6.736c0,3.719,3.015,6.732,6.734,6.732' />

  <line x1='66.155'

      y1='13.028'

      x2='39.218'

      y2='13.028' />

 </g>

</svg>