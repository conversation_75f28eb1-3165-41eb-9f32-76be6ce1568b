<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='109.861px'

    height='206.675px'

    viewBox='0 0 109.861 206.675'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='63.908,36.977 63.908,24.997 63.908,13.027 63.908,1.057 45.948,1.057 45.948,13.027 45.948,24.997    45.948,36.977 54.929,36.977  ' />

  <path d='M99.828,135.747c0-11.97,0-23.95,0-35.92c0-8.75-13.46-13.14-19.34-14.771c-8.45-2.33-16.84-3.189-25.56-3.189   c-8.721,0-17.11,0.859-25.561,3.2c-5.89,1.63-19.33,6.01-19.33,14.76c0,11.97,0,23.95,0,35.92c-2.12,2.11-4.239,4.229-6.35,6.34   c-1.86,1.86-2.63,3.73-2.63,6.36c0,10.729,0,21.46,0,32.189c0,2.12,0.43,4.24,1.22,6.21c0.39,0.98,0.87,1.92,1.43,2.82   c0.561,0.9,1.19,1.75,1.87,2.56c1.4,1.65,3.021,3.091,4.761,4.351c1.84,1.34,3.81,2.47,5.85,3.47c2.17,1.07,4.42,1.97,6.71,2.75   c2.43,0.83,4.91,1.521,7.4,2.11c0.68,0.149,1.359,0.3,2.05,0.45c3.41,0.71,5.71,0.02,8.18-2.45c1.57-1.561,3.14-3.13,4.71-4.7   c3.2,0.27,6.43,0.39,9.69,0.39c3.27,0,6.489-0.12,9.699-0.39c1.57,1.57,3.141,3.14,4.7,4.71c2.471,2.46,4.771,3.15,8.19,2.44   c0.68-0.15,1.359-0.301,2.04-0.45c2.5-0.59,4.97-1.28,7.399-2.11c2.29-0.78,4.551-1.68,6.721-2.75c2.04-1,4-2.14,5.84-3.47   c1.74-1.26,3.37-2.7,4.76-4.351c1.37-1.609,2.521-3.42,3.311-5.38c0.789-1.97,1.22-4.09,1.22-6.21c0-10.729,0-21.46,0-32.189   c0-2.63-0.78-4.5-2.63-6.36C104.059,139.977,101.938,137.857,99.828,135.747z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M54.931,171.658c3.983,2.777,12.251,8.98,17.958,8.98c4.96,0,8.979-4.02,8.979-8.98   c0-4.957-4.02-8.978-8.979-8.978C67.181,162.681,58.914,168.885,54.931,171.658z' />

  <line x1='68.399'

      y1='13.029'

      x2='41.462'

      y2='13.029' />

  <line x1='68.399'

      y1='25.002'

      x2='41.462'

      y2='25.002' />

  <path d='M99.825,180.639c0,8.746-13.447,13.133-19.331,14.762c-8.45,2.343-16.845,3.197-25.563,3.197   c-8.717,0-17.107-0.854-25.555-3.193c-5.885-1.629-19.341-6.018-19.341-14.766c0-26.938,0-53.875,0-80.813   c0-8.746,13.448-13.131,19.331-14.762c8.451-2.342,16.845-3.197,25.564-3.197c8.717,0,17.107,0.855,25.555,3.194   c5.883,1.629,19.34,6.019,19.34,14.765C99.825,126.764,99.825,153.701,99.825,180.639z' />

  <rect x='45.952'

      y='1.056'

      fill='none'

      width='17.957'

      height='35.918' />

  <line x1='54.931'

      y1='36.974'

      x2='54.931'

      y2='171.658' />

  <path d='M36.972,162.681c5.709,0,13.975,6.204,17.959,8.978c-3.984,2.777-12.25,8.98-17.959,8.98   c-4.958,0-8.979-4.02-8.979-8.98C27.993,166.701,32.014,162.681,36.972,162.681z' />

  <path d='M99.825,135.744c2.117,2.115,4.234,4.231,6.351,6.348c1.856,1.857,2.63,3.725,2.63,6.351   c0,10.733,0,21.464,0,32.196c0,2.121-0.43,4.238-1.219,6.206c-0.789,1.962-1.936,3.772-3.305,5.386   c-1.395,1.645-3.02,3.082-4.766,4.348c-1.834,1.332-3.803,2.473-5.839,3.471c-2.174,1.068-4.427,1.971-6.72,2.752   c-2.427,0.83-4.9,1.518-7.4,2.102c-0.68,0.158-1.36,0.307-2.043,0.451c-3.416,0.711-5.715,0.025-8.183-2.441   c-1.577-1.578-3.155-3.158-4.734-4.736' />

  <path d='M45.265,198.176c-1.579,1.578-3.158,3.158-4.737,4.734c-2.467,2.469-4.765,3.154-8.181,2.443   c-0.684-0.145-1.364-0.293-2.044-0.451c-2.499-0.584-4.972-1.271-7.401-2.102c-2.291-0.781-4.543-1.688-6.717-2.752   c-2.036-0.998-4.006-2.137-5.842-3.469c-1.743-1.268-3.367-2.707-4.764-4.35c-0.686-0.809-1.315-1.66-1.872-2.562   c-0.556-0.901-1.038-1.845-1.433-2.824c-0.788-1.969-1.219-4.085-1.219-6.206c0-10.732,0-21.463,0-32.196   c0-2.626,0.773-4.493,2.631-6.351c2.115-2.116,4.232-4.232,6.349-6.348' />

 </g>

</svg>