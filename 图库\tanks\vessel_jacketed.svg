<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='109.861px'

    height='125.86px'

    viewBox='0 0 109.861 125.86'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M99.829,54.925c0-11.97,0-23.939,0-35.91c0-8.75-13.46-13.14-19.34-14.77c-8.45-2.34-16.84-3.19-25.56-3.19   c-8.72,0-17.11,0.851-25.56,3.2c-5.88,1.63-19.33,6.01-19.33,14.76c0,11.971,0,23.94,0,35.91c-2.12,2.12-4.24,4.24-6.35,6.35   c-1.86,1.86-2.63,3.73-2.63,6.351c0,10.74,0,21.47,0,32.2c0,2.12,0.43,4.239,1.22,6.21c0.39,0.979,0.87,1.92,1.43,2.819   c0.56,0.9,1.18,1.75,1.87,2.561c1.4,1.64,3.02,3.08,4.76,4.35c1.84,1.33,3.81,2.471,5.85,3.471c2.17,1.069,4.42,1.97,6.71,2.75   c2.43,0.829,4.91,1.52,7.4,2.1c0.68,0.16,1.36,0.31,2.05,0.45c3.41,0.72,5.71,0.029,8.18-2.44c1.57-1.57,3.14-3.13,4.71-4.71h0.01   c3.2,0.28,6.42,0.4,9.68,0.4c3.27,0,6.49-0.12,9.69-0.4c1.57,1.57,3.15,3.14,4.71,4.71c2.47,2.47,4.77,3.16,8.19,2.44   c0.68-0.141,1.36-0.29,2.04-0.45c2.5-0.58,4.97-1.271,7.4-2.1c2.29-0.78,4.54-1.681,6.72-2.75c2.04-1,4-2.141,5.841-3.471   c1.739-1.27,3.369-2.71,4.76-4.35c1.37-1.61,2.52-3.42,3.31-5.38c0.79-1.971,1.22-4.09,1.22-6.21c0-10.73,0-21.46,0-32.2   c0-2.62-0.779-4.49-2.63-6.351C104.058,59.165,101.939,57.045,99.829,54.925z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M99.826,99.823c0,8.747-13.447,13.135-19.332,14.764c-8.451,2.342-16.844,3.195-25.563,3.195   c-8.717,0-17.108-0.854-25.556-3.193c-5.884-1.629-19.34-6.018-19.34-14.766c0-26.937,0-53.873,0-80.813   c0-8.744,13.449-13.131,19.332-14.761c8.451-2.343,16.844-3.194,25.564-3.194c8.716,0,17.106,0.852,25.554,3.191   c5.883,1.629,19.34,6.02,19.34,14.764C99.826,45.95,99.826,72.887,99.826,99.823z' />

  <path d='M45.265,117.36c-1.579,1.58-3.158,3.158-4.737,4.738c-2.467,2.467-4.765,3.152-8.181,2.439   c-0.683-0.143-1.364-0.293-2.043-0.451c-2.499-0.582-4.972-1.271-7.402-2.1c-2.291-0.783-4.542-1.687-6.716-2.752   c-2.036-0.998-4.006-2.139-5.842-3.47c-1.743-1.267-3.368-2.707-4.764-4.349c-0.686-0.809-1.315-1.662-1.872-2.561   c-0.555-0.902-1.039-1.846-1.433-2.826c-0.788-1.969-1.219-4.086-1.219-6.207c0-10.73,0-21.463,0-32.195   c0-2.627,0.773-4.492,2.631-6.35c2.115-2.117,4.232-4.234,6.349-6.35' />

  <path d='M99.826,54.929c2.117,2.115,4.233,4.232,6.35,6.35c1.856,1.857,2.63,3.723,2.63,6.35   c0,10.732,0,21.465,0,32.195c0,2.121-0.429,4.24-1.219,6.207c-0.788,1.961-1.935,3.771-3.305,5.387   c-1.395,1.643-3.019,3.082-4.765,4.348c-1.834,1.331-3.803,2.473-5.84,3.471c-2.174,1.066-4.426,1.969-6.719,2.752   c-2.428,0.828-4.901,1.518-7.4,2.1c-0.68,0.158-1.361,0.309-2.043,0.451c-3.416,0.713-5.714,0.027-8.183-2.439   c-1.578-1.58-3.155-3.158-4.734-4.738' />

 </g>

</svg>