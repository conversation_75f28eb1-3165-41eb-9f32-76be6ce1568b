<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='91.902px'

    height='199.652px'

    viewBox='0 0 91.902 199.652'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='54.927,36.977 54.927,24.997 54.927,13.026 54.927,1.057 36.978,1.057 36.978,13.026 36.978,24.997    36.978,36.977 45.947,36.977  ' />

  <path d='M71.507,85.057c-8.449-2.34-16.84-3.19-25.56-3.19s-17.11,0.851-25.56,3.2c-5.881,1.62-19.33,6.01-19.33,14.76   c0,26.94,0,53.87,0,80.811c0,8.75,13.46,13.14,19.34,14.76c8.45,2.34,16.84,3.2,25.55,3.2c8.72,0,17.12-0.86,25.57-3.2   c5.88-1.63,19.33-6.01,19.33-14.76c0-26.94,0-53.87,0-80.811C90.848,91.076,77.388,86.687,71.507,85.057z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M45.95,171.658c3.984,2.777,12.251,8.979,17.958,8.979c4.96,0,8.979-4.02,8.979-8.979s-4.02-8.979-8.979-8.979   C58.201,162.679,49.935,168.881,45.95,171.658z' />

  <path d='M27.993,162.679c5.707,0,13.975,6.202,17.957,8.979c-3.982,2.777-12.25,8.979-17.957,8.979   c-4.96,0-8.979-4.02-8.979-8.979S23.033,162.679,27.993,162.679z' />

  <line x1='59.42'

      y1='24.999'

      x2='32.481'

      y2='24.999' />

  <line x1='45.95'

      y1='36.972'

      x2='45.95'

      y2='171.658' />

  <line x1='59.42'

      y1='13.027'

      x2='32.481'

      y2='13.027' />

  <rect x='36.973'

      y='1.056'

      fill='none'

      width='17.959'

      height='35.916' />

  <path d='M90.847,180.638c0,8.745-13.449,13.133-19.33,14.763c-8.452,2.341-16.846,3.196-25.566,3.196   c-8.715,0-17.105-0.855-25.554-3.195c-5.884-1.629-19.341-6.019-19.341-14.764c0-26.938,0-53.874,0-80.813   c0-8.746,13.447-13.134,19.332-14.763c8.449-2.343,16.844-3.196,25.563-3.196c8.718,0,17.108,0.854,25.557,3.193   c5.881,1.629,19.34,6.018,19.34,14.766C90.847,126.764,90.847,153.699,90.847,180.638z' />

 </g>

 <g id='fill'

     class='fill'>

  <path d='M13.356,171.658c0-2.35-0.973-3.323-3.322-3.323s-3.322,0.974-3.322,3.323c0,2.349,0.973,3.322,3.322,3.322   S13.356,174.007,13.356,171.658z' />

  <path d='M13.356,158.19c0-2.35-0.973-3.322-3.322-3.322s-3.322,0.973-3.322,3.322c0,2.347,0.973,3.319,3.322,3.319   S13.356,160.537,13.356,158.19z' />

  <path d='M13.356,144.72c0-2.348-0.973-3.32-3.322-3.32s-3.322,0.973-3.322,3.32c0,2.35,0.973,3.322,3.322,3.322   S13.356,147.069,13.356,144.72z' />

  <path d='M13.356,131.252c0-2.35-0.973-3.322-3.322-3.322s-3.322,0.973-3.322,3.322s0.973,3.322,3.322,3.322   S13.356,133.602,13.356,131.252z' />

  <path d='M78.545,131.252c0-2.35,0.973-3.322,3.322-3.322s3.322,0.973,3.322,3.322s-0.973,3.322-3.322,3.322   S78.545,133.602,78.545,131.252z' />

  <path d='M74.056,185.128c0-2.35,0.974-3.322,3.322-3.322c2.35,0,3.322,0.973,3.322,3.322   c0,2.348-0.973,3.322-3.322,3.322C75.029,188.45,74.056,187.476,74.056,185.128z' />

  <path d='M78.545,117.782c0-2.348,0.973-3.32,3.322-3.32s3.322,0.973,3.322,3.32c0,2.349-0.973,3.322-3.322,3.322   S78.545,120.131,78.545,117.782z' />

  <path d='M78.545,104.313c0-2.347,0.973-3.322,3.322-3.322s3.322,0.976,3.322,3.322c0,2.35-0.973,3.322-3.322,3.322   S78.545,106.663,78.545,104.313z' />

  <path d='M17.848,185.128c0-2.35-0.973-3.322-3.322-3.322s-3.322,0.973-3.322,3.322c0,2.348,0.973,3.322,3.322,3.322   S17.848,187.476,17.848,185.128z' />

  <path d='M78.545,171.658c0-2.35,0.973-3.323,3.322-3.323s3.322,0.974,3.322,3.323c0,2.349-0.973,3.322-3.322,3.322   S78.545,174.007,78.545,171.658z' />

  <path d='M78.545,158.19c0-2.35,0.973-3.322,3.322-3.322s3.322,0.973,3.322,3.322c0,2.347-0.973,3.319-3.322,3.319   S78.545,160.537,78.545,158.19z' />

  <path d='M78.545,144.72c0-2.348,0.973-3.32,3.322-3.32s3.322,0.973,3.322,3.32c0,2.35-0.973,3.322-3.322,3.322   S78.545,147.069,78.545,144.72z' />

  <path d='M13.356,117.782c0-2.348-0.973-3.32-3.322-3.32s-3.322,0.973-3.322,3.32c0,2.349,0.973,3.322,3.322,3.322   S13.356,120.131,13.356,117.782z' />

  <path d='M13.356,104.313c0-2.347-0.973-3.322-3.322-3.322s-3.322,0.976-3.322,3.322c0,2.35,0.973,3.322,3.322,3.322   S13.356,106.663,13.356,104.313z' />

 </g>

</svg>