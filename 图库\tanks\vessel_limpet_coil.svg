<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='91.9px'

    height='118.842px'

    viewBox='0 0 91.9 118.842'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M71.511,4.249C63.061,1.909,54.67,1.06,45.95,1.06s-17.11,0.85-25.56,3.189   C14.5,5.879,1.061,10.27,1.061,19.02c0,26.93,0,53.87,0,80.81c0,8.74,13.45,13.13,19.33,14.76c8.449,2.341,16.84,3.2,25.56,3.2   s17.11-0.859,25.561-3.2c5.89-1.63,19.329-6.02,19.329-14.76c0-26.939,0-53.88,0-80.81C90.84,10.27,77.391,5.879,71.511,4.249z' />

 </g>

 <g id='contours'

     class='contours'>

  <path d='M90.845,99.827c0,8.745-13.445,13.132-19.33,14.763c-8.451,2.341-16.844,3.196-25.563,3.196   c-8.718,0-17.107-0.855-25.558-3.194c-5.882-1.629-19.339-6.02-19.339-14.765c0-26.938,0-53.874,0-80.813   c0-8.747,13.449-13.134,19.333-14.763c8.449-2.342,16.843-3.196,25.563-3.196c8.714,0,17.105,0.854,25.554,3.193   c5.883,1.629,19.339,6.018,19.339,14.766C90.845,45.953,90.845,72.889,90.845,99.827z' />

 </g>

 <g id='fill'

     class='fill'>

  <path d='M13.357,63.909c0-2.349-0.974-3.321-3.322-3.321c-2.35,0-3.322,0.973-3.322,3.321   c0,2.35,0.973,3.322,3.322,3.322C12.384,67.231,13.357,66.259,13.357,63.909z' />

  <path d='M13.357,23.503c0-2.347-0.974-3.322-3.322-3.322c-2.35,0-3.322,0.976-3.322,3.322   c0,2.35,0.973,3.322,3.322,3.322C12.384,26.825,13.357,25.853,13.357,23.503z' />

  <path d='M13.357,77.38c0-2.35-0.974-3.322-3.322-3.322c-2.35,0-3.322,0.973-3.322,3.322   c0,2.347,0.973,3.319,3.322,3.319C12.384,80.699,13.357,79.727,13.357,77.38z' />

  <path d='M13.357,36.973c0-2.349-0.974-3.321-3.322-3.321c-2.35,0-3.322,0.973-3.322,3.321   c0,2.348,0.973,3.322,3.322,3.322C12.384,40.295,13.357,39.32,13.357,36.973z' />

  <path d='M78.546,50.441c0-2.35,0.973-3.322,3.322-3.322s3.322,0.973,3.322,3.322s-0.973,3.322-3.322,3.322   S78.546,52.791,78.546,50.441z' />

  <path d='M13.357,90.848c0-2.35-0.974-3.324-3.322-3.324c-2.35,0-3.322,0.975-3.322,3.324   c0,2.348,0.973,3.322,3.322,3.322C12.384,94.17,13.357,93.195,13.357,90.848z' />

  <path d='M13.357,50.441c0-2.35-0.974-3.322-3.322-3.322c-2.35,0-3.322,0.973-3.322,3.322s0.973,3.322,3.322,3.322   C12.384,53.764,13.357,52.791,13.357,50.441z' />

  <path d='M17.846,104.316c0-2.35-0.973-3.322-3.322-3.322s-3.322,0.973-3.322,3.322c0,2.349,0.973,3.322,3.322,3.322   S17.846,106.665,17.846,104.316z' />

  <path d='M78.546,90.848c0-2.35,0.973-3.324,3.322-3.324s3.322,0.975,3.322,3.324c0,2.348-0.973,3.322-3.322,3.322   S78.546,93.195,78.546,90.848z' />

  <path d='M78.546,77.38c0-2.35,0.973-3.322,3.322-3.322s3.322,0.973,3.322,3.322c0,2.347-0.973,3.319-3.322,3.319   S78.546,79.727,78.546,77.38z' />

  <path d='M78.546,63.909c0-2.349,0.973-3.321,3.322-3.321s3.322,0.973,3.322,3.321c0,2.35-0.973,3.322-3.322,3.322   S78.546,66.259,78.546,63.909z' />

  <path d='M78.546,36.973c0-2.349,0.973-3.321,3.322-3.321s3.322,0.973,3.322,3.321c0,2.348-0.973,3.322-3.322,3.322   S78.546,39.32,78.546,36.973z' />

  <path d='M74.056,104.316c0-2.35,0.975-3.322,3.322-3.322c2.349,0,3.321,0.973,3.321,3.322   c0,2.349-0.973,3.322-3.321,3.322C75.03,107.639,74.056,106.665,74.056,104.316z' />

  <path d='M78.546,23.503c0-2.347,0.973-3.322,3.322-3.322s3.322,0.976,3.322,3.322c0,2.35-0.973,3.322-3.322,3.322   S78.546,25.853,78.546,23.503z' />

 </g>

</svg>