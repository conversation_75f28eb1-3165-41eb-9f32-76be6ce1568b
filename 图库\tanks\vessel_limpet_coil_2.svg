<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='100.883px'

    height='118.821px'

    viewBox='0 0 100.883 118.821'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M95.336,99.819c0-4.34,0-8.67,0-13.011v-0.01c0.29-0.05,0.59-0.109,0.88-0.17c2.61-0.53,3.61-1.75,3.61-4.41   c0-2.529-2.061-4.5-4.49-4.489v-8.881c0.29-0.06,0.59-0.119,0.88-0.18c2.61-0.52,3.61-1.74,3.61-4.399c0-2.54-2.061-4.5-4.49-4.5   v-8.881c0.29-0.06,0.59-0.119,0.88-0.18c2.61-0.52,3.61-1.74,3.61-4.4c0-2.539-2.061-4.5-4.49-4.5v-8.88   c0.29-0.06,0.59-0.12,0.88-0.18c2.61-0.52,3.61-1.74,3.61-4.4c0-2.54-2.07-4.5-4.49-4.489c0-1.62,0-3.24,0-4.86   c0-1.59-0.45-2.89-1.26-4.21c0.97-0.72,1.369-1.83,1.369-3.53c0-2.83-2.59-4.95-5.369-4.399c-1.53,0.31-3.061,0.609-4.591,0.92   c-12.149-6.07-29.92-7.37-42.88-6.46c-7.359,0.529-14.439,1.66-21.47,4.08c-5.13,1.77-15.59,6.149-15.59,13.6   c0,1.59,0,3.18,0,4.771c-0.29,0.06-0.59,0.119-0.88,0.18c-2.431,0.479-4.011,2.85-3.521,5.28c0.42,2.13,2.3,3.609,4.4,3.609v8.88   v0.011c-0.29,0.05-0.59,0.109-0.88,0.17c-2.431,0.489-4.011,2.85-3.521,5.29c0.42,2.13,2.3,3.609,4.4,3.609v8.88   c-0.29,0.061-0.59,0.12-0.88,0.181c-2.431,0.489-4.011,2.85-3.521,5.29c0.42,2.14,2.3,3.609,4.4,3.609v8.88   c-0.29,0.061-0.59,0.12-0.88,0.181c-2.431,0.489-4.011,2.85-3.521,5.279c0.42,2.141,2.3,3.62,4.4,3.62v8.88   c-0.29,0.061-0.58,0.12-0.87,0.181c-2.43,0.479-4.01,2.85-3.521,5.279c0.48,2.431,2.851,4.011,5.28,3.521   c0.21-0.04,0.42-0.08,0.63-0.12c7.811,11.68,35.03,13.53,45.851,13.29c8.3-0.18,16.27-1.14,24.27-3.51   C82.836,112.569,95.336,108.149,95.336,99.819z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='5.548'

      y1='77.643'

      x2='5.548'

      y2='68.754' />

  <line x1='5.548'

      y1='59.686'

      x2='5.548'

      y2='50.796' />

  <line x1='5.548'

      y1='95.602'

      x2='5.548'

      y2='86.713' />

  <line x1='5.548'

      y1='41.727'

      x2='5.548'

      y2='32.837' />

  <line x1='95.337'

      y1='68.843'

      x2='95.337'

      y2='77.732' />

  <path d='M95.337,86.801c0,4.338,0,8.678,0,13.016c0,8.332-12.503,12.75-18.146,14.424   c-8.002,2.373-15.979,3.324-24.271,3.506c-10.822,0.24-38.043-1.611-45.852-13.291' />

  <path d='M94.458,59.861c-29.93,5.986-59.861,11.973-89.793,17.958c-2.43,0.487-4.008,2.853-3.521,5.284   s2.852,4.008,5.283,3.521c29.931-5.986,59.86-11.975,89.791-17.959c2.607-0.521,3.609-1.74,3.609-4.4   C99.827,61.431,97.235,59.305,94.458,59.861z' />

  <path d='M95.444,11.264c0-2.83-2.592-4.957-5.369-4.4c-28.47,5.693-56.939,11.389-85.41,17.082   c-2.43,0.484-4.008,2.851-3.521,5.283c0.486,2.432,2.852,4.008,5.283,3.521c28.471-5.695,56.938-11.387,85.408-17.081   C94.442,15.146,95.444,13.926,95.444,11.264z' />

  <path d='M94.458,41.902C64.528,47.89,34.597,53.875,4.665,59.861c-2.43,0.486-4.008,2.852-3.521,5.283   s2.852,4.008,5.283,3.521c29.931-5.986,59.86-11.971,89.791-17.959c2.607-0.521,3.609-1.74,3.609-4.4   C99.827,43.472,97.235,41.35,94.458,41.902z' />

  <path d='M94.458,77.819C64.53,83.807,34.601,89.791,4.675,95.777c-2.432,0.486-4.008,2.85-3.521,5.281   s2.852,4.008,5.283,3.521c29.927-5.984,59.854-11.971,89.781-17.955c2.607-0.521,3.609-1.744,3.609-4.402   C99.827,79.39,97.235,77.264,94.458,77.819z' />

  <path d='M99.827,28.348c0-2.834-2.592-4.957-5.369-4.402c-29.93,5.985-59.861,11.973-89.793,17.957   c-2.43,0.486-4.008,2.852-3.521,5.283s2.852,4.009,5.283,3.521c29.931-5.986,59.86-11.971,89.791-17.957   C98.825,32.228,99.827,31.008,99.827,28.348z' />

  <path d='M5.548,23.768c0-1.586,0-3.176,0-4.764c0-7.451,10.455-11.834,15.587-13.604   c7.031-2.422,14.108-3.555,21.474-4.08c12.962-0.916,30.727,0.391,42.88,6.461' />

  <path d='M93.964,14.597c0.877,1.388,1.373,2.733,1.373,4.407c0,1.619,0,3.236,0,4.855' />

  <line x1='95.337'

      y1='32.926'

      x2='95.337'

      y2='41.814' />

  <line x1='95.337'

      y1='50.884'

      x2='95.337'

      y2='59.773' />

 </g>

</svg>