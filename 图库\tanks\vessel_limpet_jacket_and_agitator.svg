<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='109.861px'

    height='206.673px'

    viewBox='0 0 109.861 206.673'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <polygon points='63.916,36.973 63.916,25.002 63.916,13.032 63.916,1.053 45.956,1.053 45.956,13.032 45.956,25.002    45.956,36.973 54.936,36.973  ' />

  <path d='M106.176,142.093c-2.12-2.12-4.23-4.23-6.351-6.351c0-11.97,0-23.95,0-35.92c0-8.75-13.46-13.13-19.34-14.76   c-8.449-2.34-16.84-3.2-25.55-3.2c-8.72,0-17.12,0.86-25.57,3.2c-5.88,1.63-19.329,6.02-19.329,14.76c0,11.97,0,23.95,0,35.92   c-2.12,2.12-4.23,4.23-6.351,6.351c-1.86,1.859-2.63,3.72-2.63,6.35c0,10.729,0,21.46,0,32.2c0,2.12,0.43,4.229,1.22,6.2   c0.391,0.979,0.88,1.93,1.431,2.819c0.56,0.91,1.189,1.76,1.88,2.57c1.39,1.64,3.01,3.08,4.76,4.35c1.83,1.33,3.8,2.471,5.84,3.471   c2.17,1.06,4.42,1.97,6.72,2.75c2.42,0.829,4.9,1.52,7.4,2.1c0.68,0.16,1.36,0.31,2.04,0.45c3.42,0.71,5.72,0.02,8.18-2.44   c1.57-1.569,3.141-3.14,4.71-4.71h0.011c3.189,0.271,6.42,0.391,9.689,0.391c3.271,0,6.49-0.12,9.69-0.391   c1.569,1.57,3.14,3.141,4.71,4.71c2.47,2.46,4.76,3.15,8.18,2.44c0.68-0.141,1.36-0.29,2.04-0.45c2.5-0.58,4.97-1.271,7.4-2.1   c2.3-0.78,4.55-1.69,6.72-2.75c2.04-1,4.01-2.141,5.84-3.471c1.75-1.27,3.37-2.71,4.771-4.35c1.359-1.62,2.51-3.43,3.3-5.39   c0.79-1.961,1.22-4.08,1.22-6.2c0-10.74,0-21.471,0-32.2C108.806,145.813,108.036,143.952,106.176,142.093z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='68.399'

      y1='13.028'

      x2='41.461'

      y2='13.028' />

  <path d='M99.826,180.638c0,8.746-13.447,13.133-19.33,14.764c-8.452,2.34-16.846,3.195-25.565,3.195   c-8.715,0-17.107-0.855-25.555-3.194c-5.883-1.629-19.339-6.019-19.339-14.765c0-26.938,0-53.873,0-80.813   c0-8.746,13.446-13.133,19.33-14.762c8.45-2.343,16.846-3.197,25.563-3.197c8.717,0,17.107,0.854,25.554,3.193   c5.884,1.629,19.342,6.018,19.342,14.766C99.826,126.765,99.826,153.7,99.826,180.638z' />

  <line x1='54.931'

      y1='36.972'

      x2='54.931'

      y2='171.659' />

  <path d='M99.826,135.741c2.115,2.116,4.233,4.232,6.349,6.35c1.857,1.857,2.631,3.723,2.631,6.35   c0,10.732,0,21.465,0,32.197c0,2.12-0.43,4.24-1.219,6.205c-0.788,1.963-1.937,3.772-3.305,5.389   c-1.395,1.641-3.02,3.078-4.764,4.348c-1.837,1.33-3.806,2.469-5.84,3.47c-2.176,1.065-4.428,1.97-6.72,2.751   c-2.429,0.829-4.903,1.52-7.4,2.101c-0.68,0.158-1.36,0.308-2.044,0.45c-3.414,0.713-5.714,0.025-8.181-2.441   c-1.579-1.578-3.158-3.156-4.737-4.735' />

  <path d='M45.265,198.174c-1.578,1.579-3.158,3.157-4.735,4.735c-2.468,2.467-4.767,3.154-8.183,2.441   c-0.683-0.143-1.363-0.292-2.042-0.45c-2.499-0.581-4.975-1.271-7.403-2.101c-2.291-0.781-4.543-1.686-6.718-2.752   c-2.034-1-4.003-2.139-5.839-3.469c-1.746-1.27-3.369-2.707-4.764-4.352c-0.686-0.805-1.316-1.66-1.875-2.561   c-0.554-0.898-1.037-1.844-1.432-2.824c-0.789-1.969-1.219-4.085-1.219-6.205c0-10.732,0-21.465,0-32.197   c0-2.627,0.773-4.492,2.63-6.35c2.118-2.117,4.233-4.233,6.352-6.35' />

  <rect x='45.952'

      y='1.056'

      fill='none'

      width='17.959'

      height='35.916' />

  <path d='M54.931,171.659c3.984,2.777,12.252,8.979,17.959,8.979c4.958,0,8.978-4.02,8.978-8.979   c0-4.961-4.02-8.98-8.978-8.98C67.183,162.679,58.915,168.882,54.931,171.659z' />

  <line x1='68.399'

      y1='25'

      x2='41.461'

      y2='25' />

  <path d='M36.973,162.679c5.707,0,13.975,6.203,17.958,8.98c-3.983,2.777-12.251,8.979-17.958,8.979   c-4.96,0-8.979-4.02-8.979-8.979C27.993,166.698,32.013,162.679,36.973,162.679z' />

 </g>

 <g id='fill'

     class='fill'>

  <path d='M22.336,131.253c0-2.35-0.973-3.322-3.322-3.322c-2.349,0-3.322,0.973-3.322,3.322s0.974,3.322,3.322,3.322   C21.363,134.575,22.336,133.603,22.336,131.253z' />

  <path d='M87.526,171.659c0-2.35,0.973-3.324,3.322-3.324c2.348,0,3.322,0.975,3.322,3.324   c0,2.348-0.975,3.322-3.322,3.322C88.499,174.981,87.526,174.007,87.526,171.659z' />

  <path d='M87.526,144.72c0-2.348,0.973-3.32,3.322-3.32c2.348,0,3.322,0.973,3.322,3.32c0,2.35-0.975,3.322-3.322,3.322   C88.499,148.042,87.526,147.069,87.526,144.72z' />

  <path d='M87.526,117.783c0-2.349,0.973-3.321,3.322-3.321c2.348,0,3.322,0.973,3.322,3.321s-0.975,3.322-3.322,3.322   C88.499,121.105,87.526,120.132,87.526,117.783z' />

  <path d='M83.036,185.128c0-2.35,0.973-3.322,3.322-3.322s3.322,0.973,3.322,3.322c0,2.348-0.973,3.322-3.322,3.322   S83.036,187.476,83.036,185.128z' />

  <path d='M22.336,158.19c0-2.35-0.973-3.322-3.322-3.322c-2.349,0-3.322,0.973-3.322,3.322   c0,2.348,0.974,3.32,3.322,3.32C21.363,161.511,22.336,160.538,22.336,158.19z' />

  <path d='M22.336,104.313c0-2.346-0.973-3.322-3.322-3.322c-2.349,0-3.322,0.977-3.322,3.322   c0,2.35,0.974,3.322,3.322,3.322C21.363,107.636,22.336,106.663,22.336,104.313z' />

  <path d='M22.336,144.72c0-2.348-0.973-3.32-3.322-3.32c-2.349,0-3.322,0.973-3.322,3.32   c0,2.35,0.974,3.322,3.322,3.322C21.363,148.042,22.336,147.069,22.336,144.72z' />

  <path d='M87.526,158.19c0-2.35,0.973-3.322,3.322-3.322c2.348,0,3.322,0.973,3.322,3.322   c0,2.348-0.975,3.32-3.322,3.32C88.499,161.511,87.526,160.538,87.526,158.19z' />

  <path d='M87.526,131.253c0-2.35,0.973-3.322,3.322-3.322c2.348,0,3.322,0.973,3.322,3.322s-0.975,3.322-3.322,3.322   C88.499,134.575,87.526,133.603,87.526,131.253z' />

  <path d='M87.526,104.313c0-2.346,0.973-3.322,3.322-3.322c2.348,0,3.322,0.977,3.322,3.322   c0,2.35-0.975,3.322-3.322,3.322C88.499,107.636,87.526,106.663,87.526,104.313z' />

  <path d='M22.336,171.659c0-2.35-0.973-3.324-3.322-3.324c-2.349,0-3.322,0.975-3.322,3.324   c0,2.348,0.974,3.322,3.322,3.322C21.363,174.981,22.336,174.007,22.336,171.659z' />

  <path d='M22.336,117.783c0-2.349-0.973-3.321-3.322-3.321c-2.349,0-3.322,0.973-3.322,3.321s0.974,3.322,3.322,3.322   C21.363,121.105,22.336,120.132,22.336,117.783z' />

  <path d='M26.827,185.128c0-2.35-0.975-3.322-3.322-3.322c-2.35,0-3.322,0.973-3.322,3.322   c0,2.348,0.973,3.322,3.322,3.322C25.853,188.45,26.827,187.476,26.827,185.128z' />

 </g>

</svg>