<?xml version='1.0' encoding='utf-8'?>

<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'>

<svg version='1.1'

    xmlns='http://www.w3.org/2000/svg'

    xmlns:xlink='http://www.w3.org/1999/xlink'

    x='0px'

    y='0px'

    width='91.9px'

    height='118.842px'

    viewBox='0 0 91.9 118.842'

    xmlns:agg='http://www.example.com'

    xml:space='preserve'>
<agg:params>
	<agg:param  type="C" name="Color" classes="boundary" description="Color" cssAttributes="fill"></agg:param>
	<agg:param  type="C" name="strokeColor" classes="contours,dashed,thick" description="Stroke Color" cssAttributes="stroke"></agg:param>
</agg:params>

<style type="text/css" >
<![CDATA[

	.contours,.dashed,.thick{
		stroke-linecap:round;
		stroke-linejoin:round;
		stroke: #000000;
		fill:none;
	}
	
	.contours,dashed {
		stroke-width:0.5;
	}
	
	.thick {
		stroke-width:1.5;
	}
	
	.boundary {
		fill:   #A1BFE2;
		stroke:none;
	}
	
	.dashed {
		stroke-dasharray:1.5,1.5;
	}
]]>
</style>

 <g id='boundary'

     class='boundary'>

  <path d='M71.505,4.246c-8.45-2.34-16.841-3.189-25.551-3.189c-8.72,0-17.119,0.85-25.569,3.199   c-5.88,1.631-19.33,6.011-19.33,14.761c0,0.989,0,1.979,0,2.97c0,24.96,0,49.9,0,74.86c0,0.989,0,1.989,0,2.979   c0,8.75,13.46,13.14,19.34,14.771c8.45,2.329,16.84,3.189,25.56,3.189c8.71,0,17.11-0.86,25.561-3.2   c5.88-1.63,19.33-6.01,19.33-14.76c0-0.99,0-1.99,0-2.979c0-24.96,0-49.9,0-74.86c0-0.99,0-1.98,0-2.97   C90.845,10.267,77.385,5.876,71.505,4.246z' />

 </g>

 <g id='contours'

     class='contours'>

  <line x1='43.706'

      y1='21.991'

      x2='43.706'

      y2='96.851' />

  <line x1='65.032'

      y1='21.991'

      x2='65.032'

      y2='96.851' />

  <line x1='22.38'

      y1='21.991'

      x2='22.38'

      y2='96.851' />

  <line x1='1.056'

      y1='21.991'

      x2='90.845'

      y2='21.991' />

  <line x1='5.544'

      y1='21.991'

      x2='5.544'

      y2='96.851' />

  <line x1='86.355'

      y1='21.991'

      x2='86.355'

      y2='96.851' />

  <line x1='69.521'

      y1='21.991'

      x2='69.521'

      y2='96.851' />

  <line x1='26.87'

      y1='21.991'

      x2='26.87'

      y2='96.851' />

  <line x1='48.194'

      y1='21.991'

      x2='48.194'

      y2='96.851' />

  <line x1='1.056'

      y1='96.851'

      x2='90.845'

      y2='96.851' />

  <path d='M90.845,99.827c0,8.745-13.447,13.132-19.331,14.763c-8.45,2.341-16.845,3.196-25.563,3.196   c-8.715,0-17.105-0.855-25.555-3.194c-5.883-1.628-19.34-6.02-19.34-14.765c0-26.938,0-53.874,0-80.813   c0-8.745,13.447-13.132,19.33-14.763c8.451-2.341,16.846-3.196,25.564-3.196c8.717,0,17.107,0.855,25.553,3.194   c5.885,1.628,19.342,6.02,19.342,14.765C90.845,45.953,90.845,72.889,90.845,99.827z' />

 </g>

</svg>